<?php

namespace App\Listeners;

use App\Models\Organization\OrganizationUser;
use App\Models\Organization\OrganizationUserPermission;
use App\Models\Organization\TeamInvite;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class UserLoginAt
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        try{
            $event->user->update([
                'last_login' => now(),
            ]);
            $already_registered_organization = OrganizationUser::query()
                ->where('user_id',$event->user->id)
                ->get()
                ->pluck('organization_id')->toArray();
            $team_invite                     = TeamInvite::query()
                ->withoutGlobalScopes()
                ->where('email',$event->user->email)
                ->get();

            foreach ($team_invite as $invite){
                $org = in_array($invite->organization_id, $already_registered_organization);

                if(!$org){
                    //link organization and user
                    $org_user = OrganizationUser::create([
                        'user_id'         => $event->user->id,
                        'organization_id' => $invite->organization_id,
                    ]);

                    //save organization_user_id in OrganizationUserPermission table
                    OrganizationUserPermission::query()
                        ->where('team_invite_id',$invite->id)
                        ->update([
                            'organization_user_id'=>$org_user->id
                        ]);
                }

            }
        }
        catch(\Exception $e)
        {
            Log::info('ERROR IN TEAM INVITATION');
            Log::error($e);
        }


    }
}
