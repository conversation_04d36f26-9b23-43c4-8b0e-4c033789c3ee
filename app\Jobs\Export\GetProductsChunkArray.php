<?php

namespace App\Jobs\Export;

use App\Classes\Export\ExportToCsv;
use App\Traits\AWSSetting;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class GetProductsChunkArray implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels, AWSSetting;

    public $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public $file_url)
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        $this->data = $this->decodeS3Json($this->file_url);
        $export_to_csv_obj = new ExportToCsv();
        $export_to_csv_obj->get_products_chunk_array($this->data['products'],$this->data['product_obj'], $this->data['data']);
    }

    public function get_final_products()
    {
        return $this->final_products;
    }
}
