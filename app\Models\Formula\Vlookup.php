<?php

namespace App\Models\Formula;

use App\Classes\Template\GenerateNode;
use App\Models\Product\Category;
use App\Rules\UniqueManyToMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

/**
 * @deprecated This class will be removed in the future.
 */
class Vlookup extends Model
{
    private $data;



    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    public static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if(!isset($model->organization_id)) {
                if (Auth::check()) {
                    $model->organization_id = Auth::user()->organization_id;
                }
            }
        });

        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            }
        });

    }



    public function set_data($data)
    {
        if (isset($data['_token']))
            unset($data['_token']);

        $this->data = $data;
        return $this;
    }

    public function set_id($id)
    {
        $this->id = $id;
        return $this->find($this->id);
    }


    public function rules()
    {
        $attributes = [];

        if(isset($this->data["organization_id"])) {
            $attributes["organization_id"] = $this->data["organization_id"];
        }

        if(isset($this->data["id"])) {
            $attributes["id"] = $this->data["id"];
        }

        return [
            'name' => ['required', 'max:255', new UniqueManyToMany(new Vlookup(), $attributes)],
            'values' => 'required',
        ];
    }

    public function validation()
    {
        $validator = Validator::make($this->data, $this->rules());
        return $validator;
    }



    public function store($error_callback, $success_callback)
    {
        if ($this->data['submit_type'] == "add"){
            $validation = $this->validation();
            if (isset($validation)){
                if ($validation->fails()) {
                    return $error_callback($validation->errors());
                }
            }
        }
        elseif($this->data['submit_type'] == "edit"){
            if (empty($this->data['id'])){
                $error = [
                    'name' => [
                        "The name field is required."
                    ]
                ];
                return $error_callback($error);
            }
        }
        if (isset($this->data['id'])) {
            $vlookup = $this->find($this->data['id']);
        }else{
            $vlookup = $this;
        }

        $explode_vlookups = explode("\r\n",$this->data['values']);
        $vlookup_array = array();
        if (count($explode_vlookups) > 1){
            $explode_vlookups = array_filter($explode_vlookups);
            foreach ($explode_vlookups as $vl){
                $vlookup_array[explode(",", $vl)[0]] = explode(",", $vl)[1];
            }
        }
        else{
            $error = [
                'values' => [
                    "Please enter values in proper format."
                ]
            ];
            return $error_callback($error);
        }


        $payload = json_encode($vlookup_array);

        if (isset($this->data['organization_id']))
        {
            $vlookup->organization_id = $this->data['organization_id'];
        }
        $vlookup->name = $this->data['name'];
        $vlookup->payload = $payload;
        $vlookup->save();
        return $success_callback($vlookup);
    }
}
