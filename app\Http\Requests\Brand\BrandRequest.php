<?php

namespace App\Http\Requests\Brand;

use Illuminate\Foundation\Http\FormRequest;

class BrandRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'organization_id' => 'prohibited',
            //unique for all in organization excepet when updating itselfI
            'name' => 'required|unique:brands,name,' . $this->route('brand') . ',id,organization_id,' . auth()->user()->id,
            'is_default' => 'nullable|in:0,1'
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'The brand name is required.',
            'name.max' => 'The brand name may not be greater than 255 characters.',
            // Add other custom messages here
        ];
    }
}
