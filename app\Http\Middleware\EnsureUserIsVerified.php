<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class EnsureUserIsVerified
{
    public function handle(Request $request, Closure $next)
    {
        // If the user is not authenticated, redirect to login
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        // If the user is authenticated but not verified, redirect to verify page
        if (auth()->user() && !auth()->user()->hasVerifiedEmail()) {
            return redirect()->route('verify');
        }

        // If user is verified, allow access to the requested route
        return $next($request);
    }
}
