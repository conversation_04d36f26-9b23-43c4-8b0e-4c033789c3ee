<?php

namespace App\Http\Controllers\Product;

use App\Models\Setting;
use Illuminate\Http\Request;
use App\Models\Product\Family;
use App\Models\Channel\Channel;
use App\Models\Product\Attribute;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use App\Models\Product\AttributeOption;
use Illuminate\Support\Facades\Session;
use App\Services\AntDesignArrayConvertor;
use App\Classes\Shopify\MetaFieldDefinition;
use Apimio\MappingConnectorPackage\models\Template;

class AttributeController extends Controller
{

    public function __construct()
    {
        $this->middleware(['auth', 'verified', 'activeOrganization']);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (Gate::denies('SubscriptionAccess', 'attribute')) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        $organizationId = Auth::user()->organization_id;
        $channel = Channel::query()
            ->where([
                'organization_id' => $organizationId,
            ])
            ->first();
        $attribute = new Attribute();
        if ($request->has('q')) {
            $attribute->filter(["name" => $request->get('q')]);
        }
        $data['attribute'] = $attribute->fetch();
        $settings = Setting::where('organization_id', $organizationId)->get()->groupBy(function ($item) {
            return in_array($item->key, ['vendor', 'category', 'brand']) ? 'special' : 'variant';
        });

        $variantSettings = $settings->get('variant', collect())->pluck('value', 'key');
        $specialAttributeSettings = $settings->get('special', collect())->pluck('value', 'key');

        return view("products.attributes.view", compact('data', 'request', 'channel', 'variantSettings', 'specialAttributeSettings'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $families = Family::NotDefault()->where('organization_id', Auth::user()->organization_id)->get();
        return view('products.attributes.add', compact('families'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        if ($request->isNotFilled('name')) {
            return back()->withInput()->withErrors(['name' => 'Attribute title is required']);
        }

        $attribute = new Attribute();
        return $attribute->isShopifyMetaFieldSyncEnable(true)->set_data($request->all())->store(function ($error) use ($request) {
            if (isset($request->method_type) && $request->method_type == "import") {
                return response()->json(['errors' => $error], 404);
            }
            return back()->withInput()->withErrors($error);
        }, function ($data, $isShopifyMetaFieldSyncEnable = false) use ($request) {
            if (isset($request->method_type) && $request->method_type == "import") {

                $sessiondata = Session::get('data');

                $sessiondata['apimio_attributes_required']['all_families'] = Family::select('id', 'name')->whereNotIn('name', ['General', 'SEO'])->get()->toArray();

                if ($request->method_type == "import") {
                    //for output
                    $sessiondata['output_array'] = Template::apimio_mapping_array();
                    $sessiondata['converted_output_array'] = (new AntDesignArrayConvertor())->FamilyAttributes($sessiondata['output_array']);;
                }
                Session::put('data', $sessiondata);

                $selected_attribute = $request->get('attribute_family_name').",".$data['handle'] ?? null;
                
                return response()->json(['data' => $sessiondata, 'attribute_obj' => $data, 'selected_attribute' => $selected_attribute ], 200);
            }
            if ($data->attribute_type_id == 13) {
                return back()->withSuccess("Variant created successfully.");
            }
            $successMsg = "Attribute created successfully.";
            if ($isShopifyMetaFieldSyncEnable) {
                $successMsg = "Success! Attribute created and syncing to Shopify. Check notifications later for status update.";
            }
            return redirect(route("attributes.index"))->withSuccess($successMsg);
        });
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $attribute = Attribute::findOrFail($id);
        $families = Family::NotDefault()->where('organization_id', Auth::user()->organization_id)->get();
        $family_attributes = Attribute::where('attributes.id', $id)->first()->families()->pluck('families.id')->toArray();

        if (!$attribute->is_default) {
            if ($attribute->attribute_type_id != 4) {
                $response = json_decode($attribute->rules, true);
                return view('products.attributes.add', compact('attribute', 'response', 'families', 'family_attributes')); // redering add page without relation
            } else {
                $attribute = Attribute::with("attribute_options")->find($id);
                $response = json_decode($attribute->rules, true);
                return view('products.attributes.add', compact('attribute', 'response', 'families', 'family_attributes')); // redering add page with relation
            }
        } else {
            return back()->withErrors(['main' => 'Default attributes cannot be edited.']);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $attribute = Attribute::findOrFail($id);
        if ($attribute->attribute_type_id == 13) {
            foreach ($request->input('attribute_options', []) as $key => $optionData) {
                $isNewOption = strpos($key, 'new_') === 0;
                $optionId = isset($optionData['id']) ? $optionData['id'] : null;
                if (empty($optionData['name'])) {
                    continue;
                }
                if ($isNewOption || ($optionId !== null && !AttributeOption::where('id', $optionId)->exists())) {
                    $attribute->attribute_options()->create(['name' => $optionData['name'], 'attribute_id' => $attribute->id,]);
                } else {
                    $optionId = isset($optionData['id']) ? $optionData['id'] : null;
                    if ($optionId !== null) {
                        $option = AttributeOption::findOrFail($optionId);
                        if ($option && $option->name !== $optionData['name']) {
                            $option->name = $optionData['name'];
                            $option->save();
                        }
                    }
                }
            }
            return redirect(route("variant_attribute.index"))->withSuccess("Variant updated successfully.");
        } else {
            $attribute = new Attribute();
            $attribute->set_id($id)->set_data($request->all())->store(
                // when error
                function ($error) {
                    return back()->withInput()->withErrors($error);
                },
                // when success
                function ($data) {
                    if ($data->attribute_type_id == 13) {
                        return back()->withSuccess("Attribute updated successfully.");
                    }
                    return redirect(route("attributes.index"))->withSuccess("Attribute updated successfully.");
                }
            );
            return redirect(route("attributes.index"))->withSuccess("Attribute updated successfully.");
        }
    }
    /**
     * alter the specified attribute rule for Completness.
     */
    public function attributeSaveRules(Request $request)
    {
        $data = $request->input('attribute_rules', [])[0] ?? null;
        $variantData = $request->only(['sku_enabled', 'name_enabled', 'barcode_enabled', 'weight_enabled', 'compare_at_price_enabled', 'cost_price_enabled']);
        $userId = Auth::id(); // Ensure you're getting the correct user ID
        foreach ($variantData as $key => $value) {
            Setting::updateOrCreate(
                ['key' => $key, 'user_id' => $userId],
                ['value' => $value]
            );
        }
        if ($data) {
            $attribute = Attribute::find($data['id']);
            if ($attribute) {
                $rules = json_decode($attribute->rules, true) ?? [];

                if ($data['required'] == 1) {
                    $rules['required'] = 1;
                    // for "type" is "price or weight" than set "min" to 0.01
                    if (isset($rules['type']) && in_array($rules['type'], ['price', 'weight'])) {
                        $rules['min'] = 0.01;
                    }
                } else {
                    unset($rules['required']);
                    // for "type" is "price or weight" than unset "min"
                    if (isset($rules['type']) && in_array($rules['type'], ['price', 'weight'])) {
                        unset($rules['min']);
                    }
                }
                $attribute->update(['rules' => json_encode($rules)]);
            }
        }
        return redirect()->back()->with('success', 'Attribute rules saved successfully.');
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $attr = new Attribute();
        $attr = $attr->findOrFail($id);

        if ($attr->is_default)
            return back()->withErrors(["main" => "Default attribute cannot be deleted."]);

        $attr->delete();
        return back()->withSuccess("Deleted successfully.");
    }

    public function variantAttributeindex(Request $request)
    {
        if (Gate::denies('SubscriptionAccess', 'variant-option')) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        $families = Family::where("is_default", 0)->get();
        $data['variant_attribute'] = Attribute::where('attribute_type_id', 13);
        if ($request->has('q')) {
            $data['variant_attribute'] = $data['variant_attribute']->where('name', 'like', '%' . $request->get('q') . '%');
        }
        $data['variant_attribute'] = $data['variant_attribute']->orderBy('id', 'desc')->paginate(10);
        return view('products.variant_attribute.view', compact('data', 'request', 'families'));
    }

    public function variantAttributeEdit($id)
    {
        $attribute = Attribute::with(['attribute_options', 'families'])->findOrFail($id);
        $families = Family::where("is_default", 0)->get();
        if ($attribute->families) {
            $assigned_family_ids = array();
            foreach ($attribute->families as $key => $family) {
                $assigned_family_ids[$key] = $family->id;
            }
        }
        return view('products.variant_attribute.edit', compact('attribute', 'families', 'assigned_family_ids'));
    }

    public function syncShopifyAttribute()
    {
        DB::beginTransaction();
        try {
            $channel = Channel::query()
                ->where([
                    'organization_id' => auth()->user()->organization_id,
                    'type' => 'shopify',
                ])
                ->first();
            (new MetaFieldDefinition($channel->id, auth()->user()->organization_id))->getDefinitions()->getAttributeArray()->store();
            DB::commit();
            return back()->withSuccess("Attributes fetch successfully.");
        } catch (\Exception $exception) {
            DB::rollBack();
            return back()->withErrors(["main" => "Something went wrong. Please try again later."]);
        }
    }
}
