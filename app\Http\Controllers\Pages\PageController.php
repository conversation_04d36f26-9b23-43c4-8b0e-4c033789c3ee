<?php

namespace App\Http\Controllers\Pages;

use Laravel\Cashier\Cashier;
use App\Models\Invite\Invite;
use App\Models\Product\Brand;
use App\Models\Channel\Channel;
use App\Models\Product\Product;
use App\Models\Product\Category;
use Apimio\Gallery\Models\File;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\Channel\ShopifyChannel;
use App\Models\Organization\Organization;

class PageController extends Controller
{
    public static $NEW_VERSION_DATE = "2023-10-10 09:15:18";

    public function __construct()
    {
        $this->middleware(['auth', 'verified', "activeOrganization"]);
    }

    public function dashboard($onBoarding = null)
    {
        $product = new Product();
        $user = auth()->user();
        // Getting total products with their respective sku's
        $data['sku_count'] = $product->count_variant();
        $data['product_count'] = $product->count();

        $brand = new Brand();
        $data['brand_count'] = $brand->get_total_count();

        // Getting Invited Retailers list
        $invites = new Invite();
        $invites_list = $invites->get_Invite(auth()->user()->email);
        $data['images'] = File::whereNotNull('link')->count();
         $totalProducts = Product::count();
        $channel = Channel::query()
            ->where('organization_id', $user->organization_id)
            ->where('updated_at', '<', self::$NEW_VERSION_DATE)
            ->first();
        if (isset($channel)) {
            $shopify_channel = ShopifyChannel::query()->where('channel_id', $channel->id)->first();
            if(isset($shopify_channel)){
                if ($shopify_channel->updated_at <  self::$NEW_VERSION_DATE) {
                    $show_popup = true;
                } else {
                    $show_popup = false;
                }
            }else{
                $show_popup = false;
            }

        } else{
            $show_popup = false;
            $shopify_channel = null;
        }
        $show_popup = false;
        $shopify_channel = null;
        return view('pages.dashboard', compact('data', 'invites_list', 'onBoarding', 'show_popup' , 'shopify_channel', 'totalProducts'));
    }

    public function dashboardSuccess()
    {
        return redirect(route('dashboard'))->withSuccess('Plan changed successfully');
    }

}
