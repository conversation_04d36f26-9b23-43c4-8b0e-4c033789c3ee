<?php

namespace App\Jobs\Shopify;

use App\Classes\ConvertToApimioProduct;
use App\Classes\Shopify\ConvertPayload;

use App\Traits\AWSSetting;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class FetchShopifyProduct implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable, AWSSetting;

    /**
     * Create a new job instance.
     *
     * @return void
     */
  //  public function __construct(public array $products, public $event)
    public function __construct(public $s3Url, public $event )
    {
        //
    }

    /**
     * Execute the job.
     *
     */
    public function handle()
    {
        $event = $this->event;
        //$products = $this->products;
        //$data = Storage::disk('s3')->get($this->s3Url);
       // $products = json_decode($data);
       if(env('CUSTOM_QUEUE_WORKER') == 'local'){
        $products = $this->s3Url;
        }else{
            $products = $this->decodeS3Json($this->s3Url);
        }

        foreach ($products as $product) {
            (new \App\Classes\Shopify\StoreProduct($product, $event->channel_id, $event->organization_id))
                ->store(function($obj){
                return $obj;
            });
        }
    }
}
