{"__meta": {"id": "X9d3e9f3ccf833194416bc662be726a91", "datetime": "2025-05-27 07:18:19", "utime": **********.29431, "method": "GET", "uri": "/v2/products/importcsv", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748330298.676247, "end": **********.294331, "duration": 0.618084192276001, "duration_str": "618ms", "measures": [{"label": "Booting", "start": 1748330298.676247, "relative_start": 0, "end": **********.194537, "relative_end": **********.194537, "duration": 0.5182900428771973, "duration_str": "518ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.19455, "relative_start": 0.5183031558990479, "end": **********.294334, "relative_end": 2.86102294921875e-06, "duration": 0.09978389739990234, "duration_str": "99.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 31135416, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x product/Products", "param_count": null, "params": [], "start": **********.287813, "type": "react", "hash": "reactC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\js/Pages/product/Products.jsxproduct/Products", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fjs%2FPages%2Fproduct%2FProducts.jsx&line=1", "ajax": false, "filename": "Products.jsx", "line": "?"}, "render_count": 1, "name_original": "product/Products"}]}, "route": {"uri": "GET v2/products/{tab}", "middleware": "web", "uses": "Closure($tab) {#1861\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#1899 …}\n  file: \"C:\\Users\\<USER>\\Desktop\\work\\apimio\\routes\\web_routes\\integrations.php\"\n  line: \"12 to 16\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Froutes%2Fweb_routes%2Fintegrations.php&line=12\" onclick=\"\">routes/web_routes/integrations.php:12-16</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01768, "accumulated_duration_str": "17.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 40}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 83}], "start": **********.241802, "duration": 0.01676, "duration_str": "16.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 94.796}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 3 and `organizations`.`id` = '11' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 3) limit 1", "type": "query", "params": [], "bindings": [3, "11", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Providers\\AppServiceProvider.php", "line": 51}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.276228, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:51", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Providers\\AppServiceProvider.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FProviders%2FAppServiceProvider.php&line=51", "ajax": false, "filename": "AppServiceProvider.php", "line": "51"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 94.796, "width_percent": 5.204}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Organization\\Organization": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/image-quality-score\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq", "organization_id": "11"}, "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f027ca4-e389-4d7b-b35a-5ee836e9f025\" target=\"_blank\">View in Telescope</a>", "path_info": "/v2/products/importcsv", "status_code": "<pre class=sf-dump id=sf-dump-70149346 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-70149346\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1298312135 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1298312135\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-371570034 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-371570034\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1532482978 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1317 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlBnUFZnSUIxd2RabTBsbnNiNVpmTUE9PSIsInZhbHVlIjoibEYxTENta2l3WE5hdnQ2UGVMeE9KU2lZdEE4YitHM1dsNlpJMDRpTUROUUtuc2FhU2l6Qnl4aklkYXZpK0l5WXhCT0g3Yi9UTE5XUXlpWUtUVFM4OXNkTG5BYnRtRlk2cUtuOUdKVC9NYmpQRmNSSWFCL20wSEVBekE0WVQ4L1dpZ21xVkE2bmJDblVGdWtZYmdhenprZ1J4NzFFZjFHWEs4NDhGM0l6ZkJ5RGJSRVAwVTFpSUFjNE5jOGdXY2JLcU02VWRjc2lwZWQxRzBHNlZSUFEydERmdW5CN3UvdGJwT2VkMDIwd08xQT0iLCJtYWMiOiJiYmM1ZTEzODhjYjA3MzUyMDE3Zjc0ZTI0YWE3N2RjNjUxYmJjOTgzOWZkYjJiNWI0ZTIzNWU0MjdiZjFlOTBhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImlKSVVOZVV2dDN1Q1U2R2d6azdEUnc9PSIsInZhbHVlIjoiZmF3KzhXdERpTW9NMHFDMXp1UGZTNnVBU010eG12OTdWa3YvandLRk9RMkhtcnh0bmorOXNkTXhMNUVIRnNGR25PdDRobnVBVWloZGdMVytBNzg3a2VDMm94ZjZueU92K3dsSGUrSjFIU2lMaUNjK3pmdGp2VDZuZTdrUzdSeW4iLCJtYWMiOiIyMzNmNmU4YWJhYmU0ZTVmYjJhZDYxOGIwYmQ0MjAyMmIxNGMzYTJiNTI5OTEwYjA4MTJmMWYwZjI0MjdiZjVhIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6InMvd0FDaFlpOXVhYlk3Tzc2MFVsVGc9PSIsInZhbHVlIjoiVEgyMmVldmFTQTBDTW9yT1RvcUkya2IxemgvZGFJaWVka1pkQm1aV2l4VmEwcW12UGplZFNWWWdLQ0VxQmgyRXBjRnI0VU1veCs5ajBDcVNBNHFCcE1HRzh1eWVNNjErSGd4MUJHcDREYVpIVnBWdW9nZGZhSEo2SzhjandiR0UiLCJtYWMiOiIwYjAzN2ZjYTkwMTBjMjI3NmUxNjVkZGIwODdlODhmNzcyNmE4ZmE5YTc1NGIxMTkwODZlOWI3NTFmNTQzNzQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532482978\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1181332099 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">3|lyOnN9rFaNXD362RGbrGZMM746BZDyLiYeGFkfAHYF0FfGnJZdaE7RGsFJsg|$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k1WJWB6ZnSJcQ4dYcV3CZVMdsUzKOM46xMgpx7dI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1181332099\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 27 May 2025 07:18:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>vary</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">X-Inertia</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ii9wN2RLc09RMWFTaWprVm1IcnVQN1E9PSIsInZhbHVlIjoiU2NMT3l4aDAvZ3o5RFY3VTB6QUZ3VEJ2eEk3Q1R0UDMxT2ZNdWp0cVB0d2IzNVNhSFFCbWROZXJJZjljSUNNZ0x3VlFnQVArSkZFRGNLdXdjaW9EUnJZT1Y5QWN5RjBnQzZuOVIrRmxYS0U4ZjhDRkhwNVJxS0JCTlVZaUZ3RU4iLCJtYWMiOiJjYjA2MmVhOTJiNGZhYmRhNDEzYzFmOTZmYmQ5YjQyMGY3YTE0N2U3OWEzOThhNGQ2NjdhYzM4NmQ0NzJjZmIyIiwidGFnIjoiIn0%3D; expires=Tue, 27 May 2025 09:18:19 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6IjNPM3UydnFFVVhQRXFmSXV2M0p6TXc9PSIsInZhbHVlIjoidVIzK0VyVWZTaExsZ09wWThPQ3pBUFNFVW9sZFBsbFpFamJYUFFOQUxEcHRVS0JWMWt5M1JTZzlYcWRoQ3MzUFByQmlaRlBSWVhab080L1lpTWxvbVJrdlhSYmdkYjZsOVFsR1B2TlpqSG9QOHYwSWNYMHFJSXZGWUtlakV6WGMiLCJtYWMiOiI0ZDllMWNmZTFhNDgyNGJhMWZhOGFjNjc0Y2EwY2UzN2RkNmQ2OGI2ZTY3ZjM4OGZmMGI5MzYxMWM4ZmU3ZDBiIiwidGFnIjoiIn0%3D; expires=Tue, 27 May 2025 09:18:19 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ii9wN2RLc09RMWFTaWprVm1IcnVQN1E9PSIsInZhbHVlIjoiU2NMT3l4aDAvZ3o5RFY3VTB6QUZ3VEJ2eEk3Q1R0UDMxT2ZNdWp0cVB0d2IzNVNhSFFCbWROZXJJZjljSUNNZ0x3VlFnQVArSkZFRGNLdXdjaW9EUnJZT1Y5QWN5RjBnQzZuOVIrRmxYS0U4ZjhDRkhwNVJxS0JCTlVZaUZ3RU4iLCJtYWMiOiJjYjA2MmVhOTJiNGZhYmRhNDEzYzFmOTZmYmQ5YjQyMGY3YTE0N2U3OWEzOThhNGQ2NjdhYzM4NmQ0NzJjZmIyIiwidGFnIjoiIn0%3D; expires=Tue, 27-May-2025 09:18:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6IjNPM3UydnFFVVhQRXFmSXV2M0p6TXc9PSIsInZhbHVlIjoidVIzK0VyVWZTaExsZ09wWThPQ3pBUFNFVW9sZFBsbFpFamJYUFFOQUxEcHRVS0JWMWt5M1JTZzlYcWRoQ3MzUFByQmlaRlBSWVhab080L1lpTWxvbVJrdlhSYmdkYjZsOVFsR1B2TlpqSG9QOHYwSWNYMHFJSXZGWUtlakV6WGMiLCJtYWMiOiI0ZDllMWNmZTFhNDgyNGJhMWZhOGFjNjc0Y2EwY2UzN2RkNmQ2OGI2ZTY3ZjM4OGZmMGI5MzYxMWM4ZmU3ZDBiIiwidGFnIjoiIn0%3D; expires=Tue, 27-May-2025 09:18:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-464198644 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost:8000/api/2024-12/image-quality-score</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq</span>\"\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-464198644\", {\"maxDepth\":0})</script>\n"}}