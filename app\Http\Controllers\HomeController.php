<?php

namespace App\Http\Controllers;

use App\User;
use http\Exception;
use Illuminate\Http\Request;
use STS\HubSpot\Crm\Contact;
use Illuminate\Support\Facades\DB;
use function GuzzleHttp\Promise\all;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Redirect;
use App\Models\Organization\Organization;
use App\Notifications\ApimioNotification;
use Illuminate\Support\Facades\Validator;

use App\Services\Marketing\Hubspot\Hubspot;
use Illuminate\Support\Facades\Notification;
use Illuminate\Validation\ValidationException;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        return Redirect::route('dashboard');
    }


    /**
     * To show profile setting view
     *
     * @param  array  $data
     *
     */
    public function settings()
    {
        if (Gate::denies('SubscriptionAccess', 'settings')) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        $organization = Organization::where('id',Auth::user()->organization_id)->firstOrFail();
        return view('pages.settings',compact('organization'));
    }



    /**
     * Get a validator for an incoming profile update request.
     *
     * @param  array  $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        $validator = Validator::make($data, [
            'fname' => ['required', 'string', 'max:255'],
            'lname' => ['required', 'string', 'max:255'],
            'organization_name' => ['required', 'string', 'max:255'],
            'phone' => 'nullable|regex:/^([0-9\s\-\+\(\)]*)$/|min:10|max:16',
        ], $messages = [
            'fname.required' => 'First name cannot be empty',
            'fname.min' => 'First name should not be less than 3 characters',
            'fname.max' => 'First name should not be greater than 255 characters',
            'organization_name.required' => 'Organization name cannot be empty',
            'organization_name.min' => 'Organization name should not be less than 3 characters',
            'organization_name.max' => 'Organization name should not be greater than 255 characters',
            'lname.required' => 'Last name cannot be empty',
            'lname.min' => 'Last name should not be less than 3 characters',
            'lname.max' => 'Last name should not be greater than 255 characters',
            'phone.max' => 'Phone number should not be greater than 16 digits',
            'phone.min' => 'Phone number should not be less than 10 digits'
        ]);
        if ($validator->fails())
            return $validator;

        return $validator;

    }



    public function settings_update(Request $request)
    {
        $data = $request->except(['email']);
        $this->validator($data)->validate();
        $user = new User();
        $user->profile_update($request->all());
        Organization::where('id',Auth::user()->organization_id)->update([
            'name' => $request->organization_name,
            'region' => $request->region,
            'units' => $request->units,
            'currency' => $request->currency,
        ]);
        return redirect()->back()->with('success', 'Profile update successfully..!');
    }


    public function sendNotification()
    {
        $user = Auth::user();

        $details = [
            'greeting' => 'Hi '.$user->fname,
            'body' => 'Your Shopify Export Csv with version <b>us_english</b> is generated successfully ',
            'thanks' => 'Thank you for using '.request()->getHttpHost(),
            'actionText' => 'Download CSV File',
            'actionURL' => url(route('notification.index')),
        ];
        $user->notify(new ApimioNotification($details));
        Redirect::back();
    }

    public function on_boarding()
     {
        return view('auth.onboarding');
    }

    public function shopifyEventCall()
    {
        event(new \App\Events\PostBillingEvent(auth()->user()->organization_id, auth()->id()));
        return redirect()->route('dashboard');
    }

    public function saveCustomFields(Request $request)
    {
        $hubspot = new Hubspot(Auth::user());
        $hubspot->update($request->get('key'), $request->get('value'));
    }


    function createLambdaZip(string $lambdaCode): string
    {
        $zip = new \ZipArchive();
        $zipFilePath = tempnam(sys_get_temp_dir(), 'lambda') . '.zip';

        if ($zip->open($zipFilePath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) !== true) {
            throw new \RuntimeException('Cannot create zip archive');
        }

        // Add the lambda function code to the zip file as index.py
        $zip->addFromString('index.py', $lambdaCode);

        $zip->close();

        // Return the file contents
        $zipFileContent = file_get_contents($zipFilePath);
        unlink($zipFilePath); // Clean up the temporary file
        return $zipFileContent;
    }
}
