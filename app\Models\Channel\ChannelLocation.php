<?php

namespace App\Models\Channel;
use App\Models\Channel\Channel;
use App\Models\Location\Location;
use App\Models\Product\Inventory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ChannelLocation extends Model
{
    use HasFactory;

    protected $fillable = [
        'store_type', 'store_connect_id', 'channel_id', 'location_id'
    ];

    protected $table = 'channel_location';

    public function channels() {
        return $this->belongsTo(Channel::class,'channel_id');
    }

    public function location() {
        return $this->belongsTo(Location::class, 'location_id');
    }

    public function inventory()
    {
        return $this->hasMany(Inventory::class,'location_id');
    }
}
