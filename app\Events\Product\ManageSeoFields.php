<?php

namespace App\Events\Product;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ManageSeoFields
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    public $product, $version_ids;

    /**
     * Create a new event instance.
     *
     * @param $product
     * @param $version_id
     */
    public function __construct($product, $version_ids)
    {
        $this->product = $product;
        $this->version_ids = $version_ids;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
