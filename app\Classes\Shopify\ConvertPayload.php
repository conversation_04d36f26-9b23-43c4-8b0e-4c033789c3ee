<?php

namespace App\Classes\Shopify;

use App\Notifications\ApimioNotification;
use App\Traits\Billing\RemainingProductLimit;
use App\User;
use Exception;
use Psr\Log\InvalidArgumentException;
use Illuminate\Support\Facades\Log;

/**
 * Convert your payloads to generic apimio objects/values.
 *
 * @test php vendor/bin/phpunit tests/Unit/ConvertPayloadClassTest.php
 * @deprecated class
 * */
class ConvertPayload
{
    use RemainingProductLimit;

    private const VARIANT_MAX_LIMIT = 100;
    private const IMAGES_MAX_LIMIT  = 250;

    /**
     * Chunk payload and check if payload heads exist.
     *
     * @param array product payload from shopify
     *
     * @return array heads array
     * */
    private function chunkShopifyPayload(array $product) : array {

        // chunk payload and apimio compatible create array
        $apimio_product["shopify_id"]         = $this->getShopifyId($product);

        $apimio_product["sku"]                = $this->getSku($product);

        if(empty($apimio_product["sku"])) {
            unset($apimio_product["sku"]);
        }

        $apimio_product["status"]              = $this->getStatus($product);
        $apimio_product["inventory_id"]              = $this->getInventoryId($product);

        if(!empty($this->getVendor($product))) {
            $apimio_product["vendors"][]       = ["name" => $this->getVendor($product)];
        }

//        if(!empty($this->getCategory($product))) {
//            $apimio_product["categories"][]    = ["name" => $this->getCategory($product)];
//        }

        $apimio_product["files"]           = $this->getImages($product);
//        if(!isset($apimio_product["files"][0])) {
//            $apimio_product["files"] = $this->getFeaturedImage($product);
//            if(!isset($apimio_product["files"][0])) {
//                unset($apimio_product["files"]);
//            }
//        }

        $apimio_product["variants"]        = $this->getVariants($product);
        if(!isset($apimio_product["variants"][0])) {
            unset($apimio_product["variants"]);
        }

        $apimio_product["families"][]      = [
            "name" => "General",
            "attributes" => [
                [
                    "handle" => "product_name",
                    "name" => "Product Name",
                    "attribute_type_id" => 1,
                    "value" => $this->getTitle($product)
                ],
                [
                    "handle" => "upc_barcode",
                    "name" => "UPC / Barcode",
                    "attribute_type_id" => 1,
                    "value" => $this->getBarcode($product)
                ],
                [
                    "handle" => "price",
                    "name" => "Price",
                    "attribute_type_id" => 2,
                    "value" => $this->getPrice($product)
                ],
                [
                    "handle" => "compare_at_price",
                    "name" => "Compare at Price",
                    "attribute_type_id" => 2,
                    "value" => $this->getCompareAtPrice($product)
                ],
                [
                    "handle" => "cost_price",
                    "name" => "Cost Price",
                    "attribute_type_id" => 2,
                    "value" => 0.00
                ],
                [
                    "handle" => "weight",
                    "name" => "Weight",
                    "attribute_type_id" => 1,
                    "value" => $this->getWeight($product)
                ], [
                    "handle" => "quantity",
                    "name" => "Quantity",
                    "attribute_type_id" => 2,
                    "value" => $this->getQuantity($product)
                ],
                [
                    "handle" => "description",
                    "name" => "Description",
                    "attribute_type_id" => 3,
                    "value" => $this->getDescription($product)
                ]
            ]
        ];
        $apimio_product["families"][]      = [
            "name" => "SEO",
            "attributes" => [
                [
                    "handle" => "seo_title",
                    "name" => "SEO Title",
                    "attribute_type_id" => 1,
                    "value" => ''
                ],
                [
                    "handle" => "seo_url",
                    "name" => "SEO URL",
                    "attribute_type_id" => 1,
                    "value" => $this->getSeoURL($product)
                ],
                [
                    "handle" => "seo_description",
                    "name" => "SEO Description",
                    "attribute_type_id" => 3,
                    "value" => ''
                ],
                [
                    "handle" => "seo_keyword",
                    "name" => "SEO Tags",
                    "attribute_type_id" => 1,
                    "value" => $this->getSeoKeywords($product),
                ]

            ]
        ];

        return $apimio_product;
    }

    /**
     * Get shopify id.
     *
     * @param array $product from shopify
     *
     * @return string id or empty string if id is not available
     * */
    public function getShopifyId(array $product) : string {
        if(isset($product["id"])) {
            return $product["id"];
        }

        return '';
    }

    /**
     * Get sku.
     *
     * @param array $product from shopify
     *
     * @return string sku or empty string if sku is not available
     * */
    public function getSku(array $product) : string {
        if(isset($product["variants"][0]["sku"]) && !empty(($product["variants"][0]["sku"]))) {
            return $product["variants"][0]["sku"];
        }
        return str_replace(" ", "-", $this->getTitle($product));
    }


    /**
     * Get status.
     *
     * @param array $product from shopify
     *
     * @return bool true as published and false as draft
     * */
    public function getStatus(array $product) : bool {
        if(isset($product["status"]) && $product["status"] == "active") {
            return true;
        }

        return false;
    }

    /**
     * Get vendor.
     *
     * @param array $product from shopify
     *
     * @return string vendor or empty string if vendor is not available
     * */
    public function getVendor(array $product) : string {
        if(isset($product["vendor"])) {
            return $product["vendor"];
        }

        return '';
    }

    /**
     * Get category.
     *
     * @param array $product from shopify
     *
     * @return string
     * */
    public function getCategory(array $product) : string {
        if(isset($product["product_type"])) {
            return $product["product_type"];
        }

        return '';
    }

    /**
     * Get title.
     *
     * @param array $product from shopify
     *
     * @return string
     * */
    public function getTitle(array $product) : string {
        if(isset($product["title"])) {
            return $product["title"];
        }

        return '';
    }

    /**
     * Get barcode.
     *
     * @param array $product from shopify
     *
     * @return string
     * */
    public function getBarcode(array $product) : string {
        if(isset($product["variants"][0]["barcode"])) {
            return $product["variants"][0]["barcode"];
        }

        return '';
    }

    /**
     * Get price.
     *
     * @param array $product from shopify
     *
     * @return float
     * */
    public function getPrice(array $product) : float {
        if(isset($product["variants"][0]["price"])) {
            return (float) $product["variants"][0]["price"];
        }

        return 0;
    }

    public function getQuantity(array $product) : float {
        if(isset($product["variants"][0]["inventory_quantity"])) {
            return (float) $product["variants"][0]["inventory_quantity"];
        }

        return 0;
    }

    public function getInventoryId(array $product) : ?int {
        if(isset($product["variants"][0]["inventory_item_id"])) {
            return  $product["variants"][0]["inventory_item_id"];
        }
        return NULL;
    }

    /**
     * Get compare at price.
     *
     * @param array $product from shopify
     *
     * @return float
     * */
    public function getCompareAtPrice(array $product) : float {
        if(isset($product["variants"][0]["compare_at_price"])) {
            return (float) $product["variants"][0]["compare_at_price"];
        }

        return 0;
    }

    /**
     * Get weight in grams.
     *
     * @param array $product from shopify
     *
     * @return  array
     * */
    public function getWeight(array $product): array
    {
        $value = [];
        if(isset($product["variants"][0]["weight"])) {
            $value['value'] = $product["variants"][0]["weight"];
        }
        if(isset($product["variants"][0]["weight_unit"])){
            $value['measurement'] = $product["variants"][0]["weight_unit"];
        }
        return $value;
    }

    /**
     * Get description (html formatted).
     *
     * @param array $product from shopify
     *
     * @return string
     * */
    public function getDescription(array $product) : string {
        if(isset($product["body_html"])) {
            return $product["body_html"];
        }

        return '';
    }

    /**
     * @param array $product
     * @return string
     */
    public function getSeoURL(array $product) : string {
        if(isset($product["handle"])) {
            return $product["handle"];
        }
        return '';
    }

    /**
     * Get Seo keywords (comma separated).
     *
     * @param array $product from shopify
     *
     * @return string
     * */
    public function getSeoKeywords(array $product) : string {
        if(isset($product["tags"])) {
            return $product["tags"];
        }
        return '';
    }

    /**
     * Get featured/first image with image details.
     *
     * @param array $product from shopify
     *
     * @return array
     * */
    public function getFeaturedImage(array $product) : array {
        if(isset($product["image"])) {
            $image = $product["image"];
            if($image) {
                return [
                    "link" => $image["src"],
                    "width" => $image["width"],
                    "height" => $image["height"],
                    "ext" => null,
                    "type" => "shopify",

                ];
            }
        }

        return [];
    }

    /**
     * Get images.
     *
     * @param array $product from shopify
     *
     * @return array images or empty array if images are not available
     * */
    public function getImages(array $product) : array {
        $images = [];

        if(isset($product["images"])) {
            $count = 1;
            foreach ($product["images"] as $image) {
                $imageName = basename(parse_url($image["src"], PHP_URL_PATH));
                $images[] = [
                    "id" => $image["id"],
                    "link" => $image["src"],
                    "name" => $imageName,
                    "width" => $image["width"],
                    "height" => $image["height"],
                    "ext" => null,
                    "type" => "shopify",
                    "variant_ids"=>isset($image['variant_ids'])?$image['variant_ids']:[]
                ];
                if($count > self::IMAGES_MAX_LIMIT) {
                    break;
                }
                $count++;
            }

            return $images;
        }

        return [];
    }

    /**
     * Get variant images (comma separated).
     *
     * @param string $shopify_variant_id from shopify
     * @param array $images shopify images array
     *
     * @return string images comma separated
     * */
    public function getVariantImages(string $shopify_variant_id, array $images) : string {
        try {
            $links = [];
            foreach ($images as $image) {
                foreach ($image['variant_ids'] as $variant_id) {
                    if($variant_id == $shopify_variant_id) {
                        $links[] = $image['src'];
                    }
                }
            }
            if(sizeof($links) > 0)
                return implode(", ", $links);
            else
                return "";
        }
        catch(Exception $e) {
            return '';
        }
    }

    public function getVariantImageWidth(string $shopify_variant_id, array $images) : string {
        try {
            foreach ($images as $image) {
                foreach ($image['variant_ids'] as $variant_id) {
                    if($variant_id == $shopify_variant_id) {
                        $width = $image['width'];
                    }
                }
            }
            return $width ??'';
        }
        catch(Exception $e) {
            return '';
        }
    }

    public function getVariantImageHeight(string $shopify_variant_id, array $images) : string {
        try {
            foreach ($images as $image) {
                foreach ($image['variant_ids'] as $variant_id) {
                    if($variant_id == $shopify_variant_id) {
                        $height = $image['height'];
                    }
                }
            }
            return $height ??'';
        }
        catch(Exception $e) {
            return '';
        }
    }

    /**
     * Get option attributes for variant.
     *
     * @param array $options from shopify vendor object list
     *
     * @return array attributes used to create all the variant options.
     * */
    public function getOptionAttributes(array $options) : array {
        try {
            $option_array = [];
            foreach($options as $option) {
                $option_array[] = [
                    "name" => $option["name"],
                    "options" => $option["values"]
                ];
            }

            return $option_array;
        }
        catch (Exception $e) {
            return [];
        }
    }

    /**
     * Get option values.
     *
     * @param array $variant from shopify product object
     *
     * @return array variant options
     * */
    public function getOptionValues(array $variant) : array {
        $variants = [];
        if(isset($variant['option1'])) {
            $variants[] = $variant["option1"];
        }
        if(isset($variant["option2"])) {
            $variants[] = $variant["option2"];
        }
        if(isset($variant["option3"])) {
            $variants[] = $variant["option3"];
        }
        return $variants;
    }

    /**
     * Get variants.
     *
     * @param array $product from shopify
     *
     * @return array variants or empty array if variants are not available
     * */
    public function getVariants(array $product) : array {
        $variants = [];

        if(isset($product["variants"])) {

            $count = 0;


            foreach ($product["variants"] as $variant) {
                if (isset($variant["option1"]) && $variant["option1"] == "Default Title" || $count > self::VARIANT_MAX_LIMIT) {
                    break;
                }

                $variants[] = [
                        "id"=>$variant['id']??null,
                        "link" => isset($product["images"]) ? $this->getVariantImages($variant["id"], $product["images"]): '',
                        "attributes" => isset($product["options"]) ? $this->getOptionAttributes($product["options"]) : [],
                        "options" => $this->getOptionValues($variant),
                        "name" => $variant["title"],
                        "sku" => $variant["sku"],
                        "price" => $variant["price"],
                        "weight" => $variant["weight"],
                        "weight_unit" => $variant["weight_unit"],
                        "cost_price" => $variant["compare_at_price"],
                        "quantity" => $variant["inventory_quantity"],
                        "barcode" => $variant["barcode"],
                        "image_id" => $variant["image_id"]??null,
                        "image_width" => isset($product["images"]) ? $this->getVariantImageWidth($variant["id"], $product["images"]): '',
                        "image_height" => isset($product["images"]) ? $this->getVariantImageHeight($variant["id"], $product["images"]): '',
                        "created_at" => $variant["created_at"],
                        "inventory_id" => $variant["inventory_item_id"],
                    ];


                $count++;
            }


            return $variants;
        }

        return [];
    }


    /**
     * Convert shopify payload to apimio array which can be saved to Apimio.
     *
     * @param array product payload from shopify
     *
     *
     * */
    public function shopifyProductToApimio(array $payload, callable $callback = null, $data = null)  {
        try {
                $product = $this->chunkShopifyPayload($payload);
                if ($callback) {
                    $callback($product);
                }
        }
        catch(InvalidArgumentException $e){
            Log::info($e->getMessage());
            return [];
        }
        catch(Exception $e) {
            Log::error($e->getMessage());
            return [];
        }

    }
}
