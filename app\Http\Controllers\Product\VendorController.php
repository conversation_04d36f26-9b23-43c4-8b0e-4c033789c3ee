<?php

namespace App\Http\Controllers\Product;

use Illuminate\Http\Request;
use App\Models\Invite\Invite;
use App\Models\Product\Vendor;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Gate;

class VendorController extends Controller
{

    public function __construct()
    {
        $this->middleware(['auth', 'verified', 'activeOrganization']);
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (Gate::denies('SubscriptionAccess', 'vendor')) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        $vendor = new Invite();
        if ($request->has('q')) {
            $vendor->filter(["fname" => $request->get('q')]);
        }
        $data['vendor'] = $vendor->fetch();
        return view('products.vendors.view', compact('data', 'request'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('products.vendors.add');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $vendor = new Invite();

        return $vendor->set_data($request->all())->store(function ($error) {
            return back()->withInput()->withErrors($error);
        }, function () {
            return redirect(route("vendors.index"))->withSuccess("Vendor created successfully.");
        });

    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $vendor = Invite::find($id);
        return view('products.vendors.add', compact('vendor'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $vendor = new Invite();
        return $vendor->set_id($id)->set_data($request->all())->store(function ($error) {
            return back()->withInput()->withErrors($error);
        }, function () use ($id) {

            $msg = "";
        if($id){
                $msg = "Vendor updted successfully.";
        }
        else{
                $msg = "Vendor created successfully.";
        }
            return redirect(route("vendors.index"))->withSuccess($msg);
        });
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        if (isset($id)) {
            $vendor = new Invite();
            $vendor->delete_by_id($id);
            return redirect()->back();
        }
    }
}
