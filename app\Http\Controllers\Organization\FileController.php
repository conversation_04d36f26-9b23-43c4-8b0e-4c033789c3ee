<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
//use App\Models\Organization\File;
use App\Models\TemperoryFile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File as F;
use Illuminate\Support\Facades\Storage;
use DB;
use App\Models\Organization\File;

class FileController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data['images'] = File::where('organization_id', 1)->get();
        return view('organization.files.view', compact('data'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('organization.files.add');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
//        DB::beginTransaction();
//        try{
        $file = new File();
        $file->set_files($request->file("file"))->store(
        // when error
            function () {
                return "error";
            },

            function () {
                return "success";
            }
        );

    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
       //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $file = new File();
        $file->delete_file($id);
        return back()->withSuccess(['message'=>'file deleted successfully.']);
    }

    public function upload(Request $request)
    {
        $file = new File();
        return  $file->set_files($request->all())->store(
//            // when error
            function ($errors) {
                return $errors;
            },
            function ($success) {
                return $success;
            }
        );

    }

    public function deleteFile($id, $type = null)
    {
        $file = new File();
        $file->delete_file($id);
        if (isset($type)) {
            return response()->json(['success' => 'deleted successfully']);
        } else {
            return back()->withSuccess('file deleted successfully.')->with(['delete'=>'delete']);
        }

    }

    //test file to store s3 file
    public function testFile(Request $request){

        //from url
        $url = 'https://cdn.shopify.com/s/files/1/0569/8633/7333/products/61fXHnDzOWL._AC_UX679_9629f3f2-48c4-4f3a-abc0-14c7758e6f9c.jpg';
        $contents = file_get_contents($url);
        $name = substr($url, strrpos($url, '/') + 1);

        $path = Storage::disk('s3')->put('images/'.$name,  $contents );
//
//        // from form request
//         $path = Storage::disk('s3')->put('images',  $request->file('image') );

        return $path = Storage::disk('s3')->url('images/'.$name);

    }



}
