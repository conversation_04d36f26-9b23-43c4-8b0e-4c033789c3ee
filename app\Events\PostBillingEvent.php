<?php

namespace App\Events;

use App\Models\Channel\Channel;
use App\Models\Organization\Organization;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class PostBillingEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $organization_id;
    public $user_id;
    public $channel_id;

    /**
     * Create a new event instance.
     *
     * @param $organization_id
     *
     * @return void
     */
    public function __construct($organization_id ,$user_id)
    {
        $this->organization_id = $organization_id;

        $this->user_id = $user_id;

        $channels = new Channel();
        $channel = $channels->withCount('shopify_channel')->first();
        $this->channel_id = $channel->id;

    }

}
