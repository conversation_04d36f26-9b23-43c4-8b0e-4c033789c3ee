<?php

namespace App\Http\Controllers\Formula;

use App\Http\Controllers\Controller;
use App\Models\Formula\Vlookup;
use Illuminate\Http\Request;

/**
 * @deprecated This class will be removed in the future.
 */
class VLookupController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $vlookup = new Vlookup();
        $message = "";
        $submit_type = $request->submit_type;
        if ($submit_type == 'add'){
            $message = "VLookup created successfully.";
        }
        elseif($submit_type == 'edit'){
            $message = "VLookup updated successfully.";

        }
        return $vlookup->set_data($request->all())->store(function ($error) {
            return response()->json([
                'status' => 'error',
                'data' => $error,
                'message' => "Please fill all the fields",
            ],500);
        }, function ($success) use ($message) {
            return response()->json([
                'status' => 'success',
                'data' => $success,
                'message' => $message,
            ],200);
        });
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Formula\Vlookup  $vlookup
     * @return \Illuminate\Http\Response
     */
    public function show(Vlookup $vlookup)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Formula\Vlookup  $vlookup
     * @return \Illuminate\Http\Response
     */
    public function edit(Vlookup $vlookup)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Formula\Vlookup  $vlookup
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Vlookup $vlookup)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Formula\Vlookup  $vlookup
     * @return \Illuminate\Http\Response
     */
    public function destroy(Vlookup $vlookup)
    {
        //
    }


    /**
     * fetch the specified id values.
     *
     * @param  \App\Models\Formula\Vlookup  $vlookup
     * @return \Illuminate\Http\JsonResponse
     */
    public function fetch(Request $request)
    {
        $value = "";
        $vlookup = Vlookup::find($request->id);
        $vlookup_values = json_decode($vlookup->payload,true);
        foreach ($vlookup_values as $lookup_key => $lookup_value){
            $value .= $lookup_key.",".$lookup_value."\n";
        }
        return response()->json([
            'status' => 'success',
            'data' => $value,
            'message' => "value fetched",
        ],200);

    }
}
