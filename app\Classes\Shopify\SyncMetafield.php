<?php

namespace App\Classes\Shopify;

use App\Models\Notification\ErrorLog;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Psr\Log\InvalidArgumentException;
use Str;

class SyncMetafield
{
    public function __construct(){
    }

    private function getValue($attribute){

        $value=null;
        $rules = json_decode($attribute->rules,true);
        foreach ($attribute->value as $attr){

            if(isset($rules['type']) && $rules['type'] == "price"){
                $value = [
                    'amount' =>(float)$attr->value,
                    'currency_code'=>$attr->unit
                ];
            }
            elseif(!isset($rules['value_type']) || $rules['value_type'] == "single" ){
                if($attr->unit){
                    $value = [
                        'value'=>$attr->value,
                        'unit'=>$attr->unit,
                    ];
                    return json_encode($value , true);
                }
                else{
                    if($attribute->attribute_type && $attribute->attribute_type->id == 8){

                        $value = [
                            'scale_min'=>number_format((float)$rules['min'],1),
                            'scale_max'=>number_format((float)$rules['max'],1),
                            'value'=>  number_format((float)$attr->value,1),
                        ];

                        return json_encode($value );

                    }elseif($attribute->attribute_type && $attribute->attribute_type->id == 5){

                        if($rules['type'] == 'date_and_time'){
                            return Carbon::parse($attr->value)->toIso8601String();
                        }else{
                            return Carbon::parse($attr->value)->format('Y-m-d');
                        }

                    }
                    else {

                        return empty($attr->value)?0:$attr->value;
                    }
                }
            }
            else{
                if($attr->unit){
                    $value[] = [
                        'value'=>$attr->value,
                        'unit'=>$attr->unit,
                    ];
                }
                else{

                    if($attribute->attribute_type && $attribute->attribute_type->id == 8){

                        $value[] = [
                            'scale_min'=>number_format((float)$rules['min'],1),
                            'scale_max'=>number_format((float)$rules['max'],1),
                            'value'=>  number_format((float)$attr->value,1),
                        ];

                    }elseif($attribute->attribute_type && $attribute->attribute_type->id == 5){
                        if($rules['type'] == 'date_and_time'){
                            $value[] = Carbon::parse($attr->value)->toIso8601String();
                        }else{
                            $value[] = Carbon::parse($attr->value)->format('Y-m-d');
                        }
                    }
                    else {
                        $value[] = $attr->value;
                    }
                }
            }
        }
        // to delete the existing value from shopify
        if(!$value && isset($rules['type'])){
//            if( $rules['type'] == "integer" || $rules['type'] == "decimal"){
//                $value = 0;
//            }else{
                $value = null;
//            }

        }
        elseif(!$value ){
            $value  = null;
        }
        return $value?json_encode($value , true):$value;

    }


    /**l
     * @param $product
     * @return array
     */
    public function createAttributeArray($product): array
    {

        $attributes = array();
        $count = 0;
        foreach($product['versions'] as $version ){
            foreach($version->families as $family){
                if($family->name != 'General' && $family->name != 'SEO' ){
                    foreach($family['attributes'] as $attribute){

                        if($attribute->value){

                            //create array for metafield
                            $value = $this->getValue($attribute);
                            if($value || $value === 0) {
                                $family_name = Str::slug($family->name, '-');
                                $attributes[$count]['namespace'] = $family_name;
                                $attributes[$count]['key'] = $attribute->handle;
                                $attributes[$count]['value'] = $value;

                                $count++;
                            }
                        }

                    }//end attributes

                } //end if not get general family

            } // end family

        } //end version foreach

        return $attributes;

    } //end function



    public function store(array $attributes ,string $shopify_base_url,int $shopify_product_id , $product = null, int $organization_id = null): void
    {
        try{

            $error_list = "<ul>";
            $error_occur = 0;
            foreach ( $attributes as $attribute){
                usleep(700000);
                $response = Http::retry(1, 1000)->post($shopify_base_url."products/". $shopify_product_id ."/metafields.json", [
                    "metafield" => $attribute
                ]);
                if($response->successful()){
                    Log::channel('shopify')->info('Metafield syncing successfully');
                }else{
                    $error_occur = 1;
                    $data = $response->json();
                    Log::channel('shopify')->info('ERROR IN syncing METAFIELDS');
                    Log::channel('shopify')->error($data);

                    $error = "";
                    foreach ($data["errors"] as $error_row) {
                        $error .= $attribute['key']." ".implode( "<br>",$error_row);
                        $error_list .= "<li>".$error.'</li>';
                    }
                    // throw new InvalidArgumentException($error);
                }
            }
            $error_list.= "</ul>";

            if($error_occur) {
                $data = [
                    'organization_id' => $organization_id,
                    'description' => "There are some errors in attribute sets while syncing product back to shopify with <b> sku : ".$product['sku']."</b>.".$error_list,
                    'type' => 'Shopify syncing',
                    'link' => route('products.edit', $product['id']),
                    'link_text' => 'View Product',
                    'status' => 'error',
                ];

                $error = new ErrorLog();
                $error->setData($data)->store(function ($error) {
                    Log::channel('shopify')->info('Error in saving ErrorLogs.');
                    Log::channel('shopify')->error($error);
                }, function () {
                });
            }
        }catch (Exception $exception){
            Log::channel('shopify')->info('ERROR IN syncing METAFIELDS');
            Log::channel('shopify')->error($exception);
           // throw new InvalidArgumentException($exception->getMessage());
        }
    }

}// end class
