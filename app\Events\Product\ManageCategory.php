<?php

namespace App\Events\Product;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ManageCategory
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $data;
    public $product;
    public $refresh;

    /**
     * Create a new event instance.
     *
     * @param $product
     * @param $data
     */
    public function __construct($product,$data,$refresh = null)
    {
        $this->data = $data;
        $this->product = $product;
        $this->refresh = $refresh;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
