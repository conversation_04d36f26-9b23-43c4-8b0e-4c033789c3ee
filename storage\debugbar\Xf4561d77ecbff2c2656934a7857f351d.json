{"__meta": {"id": "Xf4561d77ecbff2c2656934a7857f351d", "datetime": "2025-05-27 07:17:58", "utime": **********.841122, "method": "POST", "uri": "/api/2024-12/organization", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[07:17:50] LOG.warning: Optional parameter $invite_channel_ids declared before required parameter $authuser is implicitly treated as a required parameter in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php on line 372", "message_html": null, "is_string": false, "label": "warning", "time": **********.499017, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1748330268.929019, "end": **********.841163, "duration": 9.91214394569397, "duration_str": "9.91s", "measures": [{"label": "Booting", "start": 1748330268.929019, "relative_start": 0, "end": **********.416256, "relative_end": **********.416256, "duration": 0.48723697662353516, "duration_str": "487ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.416269, "relative_start": 0.48725008964538574, "end": **********.841167, "relative_end": 4.0531158447265625e-06, "duration": 9.424897909164429, "duration_str": "9.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 38785560, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/2024-12/organization", "middleware": "api, auth:sanctum", "as": "organization.store", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@store", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=67\" onclick=\"\">app/Http/Controllers/Api/OrganizationController.php:67-111</a>"}, "queries": {"nb_statements": 56, "nb_visible_statements": 58, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.10105, "accumulated_duration_str": "101ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.462736, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 3.196}, {"sql": "select count(*) as aggregate from `organizations` where `name` = 'Tanzayb' and (`id` = 3)", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 948}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 658}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 457}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 492}], "start": **********.96373, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 3.196, "width_percent": 1.385}, {"sql": "select count(*) as aggregate from `organizations` where `name` = 'Tanzayb' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 3)", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Rules/UniqueManyToMany.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Rules\\UniqueManyToMany.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 861}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 650}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 457}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 492}], "start": **********.02964, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "UniqueManyToMany.php:55", "source": {"index": 16, "namespace": null, "name": "app/Rules/UniqueManyToMany.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Rules\\UniqueManyToMany.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FRules%2FUniqueManyToMany.php&line=55", "ajax": false, "filename": "UniqueManyToMany.php", "line": "55"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 4.582, "width_percent": 0.782}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 230}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.038596, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Organization.php:230", "source": {"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 230}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=230", "ajax": false, "filename": "Organization.php", "line": "230"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 5.364, "width_percent": 0}, {"sql": "insert into `organizations` (`shop_id`, `name`, `region`, `units`, `currency`, `updated_at`, `created_at`) values (null, 'Tanzayb', null, null, null, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": [null, "<PERSON><PERSON><PERSON><PERSON>", null, null, null, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.0393429, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "Organization.php:245", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=245", "ajax": false, "filename": "Organization.php", "line": "245"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 5.364, "width_percent": 3.662}, {"sql": "insert into `channels` (`organization_id`, `name`, `type`, `updated_at`, `created_at`) values (11, 'Tanzayb Store', 'shopify', '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": [11, "Tanzayb Store", "shopify", "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 328}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.048678, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "Organization.php:328", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=328", "ajax": false, "filename": "Organization.php", "line": "328"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 9.025, "width_percent": 4.018}, {"sql": "select count(*) as aggregate from `locations` where `organization_id` = 11 and `organization_id` = 11", "type": "query", "params": [], "bindings": [11, 11], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Location/Location.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Location\\Location.php", "line": 36}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 334}, {"index": 25, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 32, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.083311, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "Location.php:36", "source": {"index": 16, "namespace": null, "name": "app/Models/Location/Location.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Location\\Location.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FLocation%2FLocation.php&line=36", "ajax": false, "filename": "Location.php", "line": "36"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 13.043, "width_percent": 2.632}, {"sql": "insert into `locations` (`organization_id`, `name`, `default_location`, `updated_at`, `created_at`) values (11, 'Tanzayb Store Warehouse', 1, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": [11, "Tanzayb Store Warehouse", 1, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 334}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0891562, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Organization.php:334", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 334}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=334", "ajax": false, "filename": "Organization.php", "line": "334"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 15.675, "width_percent": 0.614}, {"sql": "insert into `channel_location` (`channel_id`, `location_id`, `updated_at`, `created_at`) values (8, 8, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": [8, 8, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 339}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.093287, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "Organization.php:339", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 339}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=339", "ajax": false, "filename": "Organization.php", "line": "339"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 16.289, "width_percent": 3.652}, {"sql": "insert into `versions` (`name`, `organization_id`, `is_default`, `currency`, `separator`, `updated_at`, `created_at`) values ('en-US', 11, 1, 'usd', '.', '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": ["en-US", 11, 1, "usd", ".", "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 349}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1017258, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "Organization.php:349", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=349", "ajax": false, "filename": "Organization.php", "line": "349"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 19.941, "width_percent": 3.543}, {"sql": "insert into `channel_version` (`channel_id`, `version_id`, `updated_at`, `created_at`) values (8, 8, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": [8, 8, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 355}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.110764, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "Organization.php:355", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 355}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=355", "ajax": false, "filename": "Organization.php", "line": "355"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 23.483, "width_percent": 3.137}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `updated_at`, `created_at`) values ('Tanzayb', 11, 1, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>", 11, 1, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 228}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 359}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.120055, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "Folder.php:228", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 228}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=228", "ajax": false, "filename": "Folder.php", "line": "228"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 26.62, "width_percent": 3.147}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6ImlNcGxPVFdFR215UXZVYm52d2t5K1E9PSIsInZhbHVlIjoialI2QXhGUVNQSERLcnJlazYwZnVYQT09IiwibWFjIjoiOWQxZDg0MjZmMmFjNDBmMzY0YjMwZGEwOTFhY2M3OTM1NDY3MDdmMWRmNGVhZWRiMTYxZWI4NWNkMDM0YmE5OSIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-05-27 07:17:50' where `id` = 29 and `organization_id` = 11", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6ImlNcGxPVFdFR215UXZVYm52d2t5K1E9PSIsInZhbHVlIjoialI2QXhGUVNQSERLcnJlazYwZnVYQT09IiwibWFjIjoiOWQxZDg0MjZmMmFjNDBmMzY0YjMwZGEwOTFhY2M3OTM1NDY3MDdmMWRmNGVhZWRiMTYxZWI4NWNkMDM0YmE5OSIsInRhZyI6IiJ9", "2025-05-27 07:17:50", 29, 11], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 230}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 359}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.127314, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 29.767, "width_percent": 0.703}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `type`, `updated_at`, `created_at`) values ('Instagram', 11, 1, 'social_media', '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": ["Instagram", 11, 1, "social_media", "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.1309948, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Folder.php:242", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=242", "ajax": false, "filename": "Folder.php", "line": "242"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 30.47, "width_percent": 0.594}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6IkNsVHV4QVdOdG1rNWRZUmNpMm9vUWc9PSIsInZhbHVlIjoiS3Bnb0xwNkRPWmlFeUNYWHFxS3BHdz09IiwibWFjIjoiZGMwM2JmYWZkYzc3NmE5MTA5Y2ViZWNkM2YwODU5NWQzYTI5ZGE0MWQ4MjdjMzBkNTc5YWI1Y2I1ZDNmN2Y4ZCIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-05-27 07:17:50' where `id` = 30 and `organization_id` = 11", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6IkNsVHV4QVdOdG1rNWRZUmNpMm9vUWc9PSIsInZhbHVlIjoiS3Bnb0xwNkRPWmlFeUNYWHFxS3BHdz09IiwibWFjIjoiZGMwM2JmYWZkYzc3NmE5MTA5Y2ViZWNkM2YwODU5NWQzYTI5ZGE0MWQ4MjdjMzBkNTc5YWI1Y2I1ZDNmN2Y4ZCIsInRhZyI6IiJ9", "2025-05-27 07:17:50", 30, 11], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 244}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.1345701, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 31.064, "width_percent": 0.614}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `type`, `updated_at`, `created_at`) values ('Youtube', 11, 1, 'social_media', '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": ["Youtube", 11, 1, "social_media", "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.139459, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Folder.php:242", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=242", "ajax": false, "filename": "Folder.php", "line": "242"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 31.677, "width_percent": 0.554}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6Ind3YWsvaHhUZ0tkd1daUmFrMFhGYUE9PSIsInZhbHVlIjoiakxhV0VicFJrY1JkSHNWZHN2THVFQT09IiwibWFjIjoiNWZhZTYwZDkwM2Q0M2Y1NDkwMzc0M2JhMmZjNmNmM2NlMzAwMWRmMDc3YmZhYzE0NjNlNTZjY2ZlYWMyZjdmZSIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-05-27 07:17:50' where `id` = 31 and `organization_id` = 11", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6Ind3YWsvaHhUZ0tkd1daUmFrMFhGYUE9PSIsInZhbHVlIjoiakxhV0VicFJrY1JkSHNWZHN2THVFQT09IiwibWFjIjoiNWZhZTYwZDkwM2Q0M2Y1NDkwMzc0M2JhMmZjNmNmM2NlMzAwMWRmMDc3YmZhYzE0NjNlNTZjY2ZlYWMyZjdmZSIsInRhZyI6IiJ9", "2025-05-27 07:17:50", 31, 11], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 244}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.143106, "duration": 0.0125, "duration_str": "12.5ms", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 32.232, "width_percent": 12.37}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `type`, `updated_at`, `created_at`) values ('Facebook', 11, 1, 'social_media', '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": ["Facebook", 11, 1, "social_media", "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.158546, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Folder.php:242", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=242", "ajax": false, "filename": "Folder.php", "line": "242"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 44.602, "width_percent": 0.524}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6IklpZlZwcll0ajNTZkozZTZoZmZDTHc9PSIsInZhbHVlIjoiaVZNSDhVRnNaVG9Fcnpia3lyeHBYQT09IiwibWFjIjoiZGU4MmRmMTU0YWYzMTE2NDc3MGMwMzAyNTM5MzAzZjY3OTkwYzRjZDVlMTk3ZjU3YzJlZGEwMmM3ODdkMzNjMSIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-05-27 07:17:50' where `id` = 32 and `organization_id` = 11", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6IklpZlZwcll0ajNTZkozZTZoZmZDTHc9PSIsInZhbHVlIjoiaVZNSDhVRnNaVG9Fcnpia3lyeHBYQT09IiwibWFjIjoiZGU4MmRmMTU0YWYzMTE2NDc3MGMwMzAyNTM5MzAzZjY3OTkwYzRjZDVlMTk3ZjU3YzJlZGEwMmM3ODdkMzNjMSIsInRhZyI6IiJ9", "2025-05-27 07:17:50", 32, 11], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 244}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.161728, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 45.126, "width_percent": 0.554}, {"sql": "insert into `families` (`name`, `is_default`, `organization_id`, `updated_at`, `created_at`) values ('General', 1, 11, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": ["General", 1, 11, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 371}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.166023, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "Organization.php:371", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 371}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=371", "ajax": false, "filename": "Organization.php", "line": "371"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 45.68, "width_percent": 2.246}, {"sql": "insert into `settings` (`key`, `organization_id`, `value`) values ('sku', 11, '1'), ('name', 11, '1'), ('barcode', 11, '0'), ('weight', 11, '1'), ('price', 11, '1'), ('compare_at_price', 11, '0'), ('cost_price', 11, '0'), ('brand', 11, '1'), ('vendor', 11, '1'), ('category', 11, '1')", "type": "query", "params": [], "bindings": ["sku", 11, "1", "name", 11, "1", "barcode", 11, "0", "weight", 11, "1", "price", 11, "1", "compare_at_price", 11, "0", "cost_price", 11, "0", "brand", 11, "1", "vendor", 11, "1", "category", 11, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 373}, {"index": 11, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 18, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.172529, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "Organization.php:373", "source": {"index": 10, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 373}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=373", "ajax": false, "filename": "Organization.php", "line": "373"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 47.927, "width_percent": 2.682}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Product Name', 'product_name', 1, 11, '{\\\"required\\\":1,\\\"max\\\":255}', 1, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": ["Product Name", "product_name", 1, 11, "{\"required\":1,\"max\":255}", 1, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 434}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.180783, "duration": 0.011630000000000001, "duration_str": "11.63ms", "memory": 0, "memory_str": null, "filename": "Organization.php:434", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 434}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=434", "ajax": false, "filename": "Organization.php", "line": "434"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 50.609, "width_percent": 11.509}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (18, 66, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": [18, 66, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 439}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.196127, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "Organization.php:439", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=439", "ajax": false, "filename": "Organization.php", "line": "439"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 62.118, "width_percent": 3.533}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Title', 'title', 13, 11, null, 1, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": ["Title", "title", 13, 11, null, 1, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 449}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.203196, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Organization.php:449", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=449", "ajax": false, "filename": "Organization.php", "line": "449"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 65.651, "width_percent": 0.693}, {"sql": "insert into `attribute_options` (`name`, `attribute_id`, `updated_at`, `created_at`) values ('Default Title', 67, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": ["Default Title", 67, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 454}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2078779, "duration": 0.003, "duration_str": "3ms", "memory": 0, "memory_str": null, "filename": "Organization.php:454", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 454}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=454", "ajax": false, "filename": "Organization.php", "line": "454"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 66.343, "width_percent": 2.969}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Description', 'description', 3, 11, '{\\\"required\\\":1,\\\"max\\\":63000}', 1, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": ["Description", "description", 3, 11, "{\"required\":1,\"max\":63000}", 1, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 463}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2136812, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Organization.php:463", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 463}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=463", "ajax": false, "filename": "Organization.php", "line": "463"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 69.312, "width_percent": 0.614}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (18, 68, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": [18, 68, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 468}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.217125, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Organization.php:468", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 468}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=468", "ajax": false, "filename": "Organization.php", "line": "468"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 69.926, "width_percent": 0.534}, {"sql": "insert into `families` (`name`, `is_default`, `organization_id`, `updated_at`, `created_at`) values ('SEO', 1, 11, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": ["SEO", 1, 11, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 476}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.221185, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Organization.php:476", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 476}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=476", "ajax": false, "filename": "Organization.php", "line": "476"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 70.46, "width_percent": 0.495}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('URL Slug', 'seo_url', 11, 11, '{\\\"required\\\":1,\\\"0\\\":\\\"slug\\\",\\\"1\\\":\\\"url\\\",\\\"max\\\":255}', 1, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": ["URL Slug", "seo_url", 11, 11, "{\"required\":1,\"0\":\"slug\",\"1\":\"url\",\"max\":255}", 1, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 486}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2244601, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Organization.php:486", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 486}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=486", "ajax": false, "filename": "Organization.php", "line": "486"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 70.955, "width_percent": 0.614}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (19, 69, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": [19, 69, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 491}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.227802, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Organization.php:491", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 491}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=491", "ajax": false, "filename": "Organization.php", "line": "491"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 71.569, "width_percent": 0.554}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('SEO Title', 'seo_title', 1, 11, '{\\\"required\\\":1,\\\"max\\\":255}', 1, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": ["SEO Title", "seo_title", 1, 11, "{\"required\":1,\"max\":255}", 1, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 501}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2312272, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Organization.php:501", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 501}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=501", "ajax": false, "filename": "Organization.php", "line": "501"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 72.123, "width_percent": 0.633}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (19, 70, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": [19, 70, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 506}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.234724, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Organization.php:506", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 506}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=506", "ajax": false, "filename": "Organization.php", "line": "506"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 72.756, "width_percent": 0.515}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('SEO Description', 'seo_description', 3, 11, '{\\\"required\\\":1,\\\"max\\\":160}', 1, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": ["SEO Description", "seo_description", 3, 11, "{\"required\":1,\"max\":160}", 1, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 516}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2389429, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Organization.php:516", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 516}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=516", "ajax": false, "filename": "Organization.php", "line": "516"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 73.271, "width_percent": 0.554}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (19, 71, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": [19, 71, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 521}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.242135, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Organization.php:521", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 521}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=521", "ajax": false, "filename": "Organization.php", "line": "521"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 73.825, "width_percent": 0.455}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Tags', 'seo_keyword', 1, 11, '{\\\"required\\\":1,\\\"max\\\":255}', 1, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": ["Tags", "seo_keyword", 1, 11, "{\"required\":1,\"max\":255}", 1, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 531}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.245371, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Organization.php:531", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 531}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=531", "ajax": false, "filename": "Organization.php", "line": "531"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 74.28, "width_percent": 0.515}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (19, 72, '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": [19, 72, "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 536}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.248672, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Organization.php:536", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 536}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=536", "ajax": false, "filename": "Organization.php", "line": "536"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 74.795, "width_percent": 0.524}, {"sql": "select `id` from `families` where `is_default` = 1 and `organization_id` = 11", "type": "query", "params": [], "bindings": [1, 11], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 554}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.2519069, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Organization.php:554", "source": {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 554}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=554", "ajax": false, "filename": "Organization.php", "line": "554"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 75.319, "width_percent": 0.574}, {"sql": "select * from `attribute_family` where `family_id` in (18, 19)", "type": "query", "params": [], "bindings": [18, 19], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.256211, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Organization.php:555", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=555", "ajax": false, "filename": "Organization.php", "line": "555"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 75.893, "width_percent": 0.663}, {"sql": "select * from `attributes` where `attributes`.`id` in (66, 68, 69, 70, 71, 72) and `organization_id` = 11", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 29, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.345614, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Organization.php:555", "source": {"index": 20, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=555", "ajax": false, "filename": "Organization.php", "line": "555"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 76.556, "width_percent": 0.891}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (59, 11, 'title', '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": [59, 11, "title", "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 563}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.350127, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "Organization.php:563", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 563}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=563", "ajax": false, "filename": "Organization.php", "line": "563"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 77.447, "width_percent": 3.929}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (60, 11, 'body_html', '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": [60, 11, "body_html", "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 566}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.357718, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Organization.php:566", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 566}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=566", "ajax": false, "filename": "Organization.php", "line": "566"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 81.376, "width_percent": 0.584}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (62, 11, 'metafields_global_title_tag', '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": [62, 11, "metafields_global_title_tag", "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 581}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.36111, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Organization.php:581", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 581}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=581", "ajax": false, "filename": "Organization.php", "line": "581"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 81.959, "width_percent": 0.495}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (63, 11, 'metafields_global_description_tag', '2025-05-27 07:17:50', '2025-05-27 07:17:50')", "type": "query", "params": [], "bindings": [63, 11, "metafields_global_description_tag", "2025-05-27 07:17:50", "2025-05-27 07:17:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 584}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.36423, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Organization.php:584", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 584}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=584", "ajax": false, "filename": "Organization.php", "line": "584"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 82.454, "width_percent": 0.505}, {"sql": "select count(*) as aggregate from `invites` where `email` = '<EMAIL>' and `is_accepted` = '0' and `is_declined` = '0'", "type": "query", "params": [], "bindings": ["<EMAIL>", "0", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 124}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 544}, {"index": 18, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 25, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.500176, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "Invite.php:124", "source": {"index": 16, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FInvite%2FInvite.php&line=124", "ajax": false, "filename": "Invite.php", "line": "124"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 82.959, "width_percent": 3.008}, {"sql": "select * from `invites` where `email` = '<EMAIL>'", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 486}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 250}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.506803, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Invite.php:486", "source": {"index": 15, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 486}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FInvite%2FInvite.php&line=486", "ajax": false, "filename": "Invite.php", "line": "486"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 85.967, "width_percent": 0.614}, {"sql": "update `organizations` set `stripe_id` = 'cus_SO3tgcCCKhtI4m', `organizations`.`updated_at` = '2025-05-27 07:17:58' where `id` = 11", "type": "query", "params": [], "bindings": ["cus_SO3tgcCCKhtI4m", "2025-05-27 07:17:58", 11], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/cashier/src/Concerns/ManagesCustomer.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\cashier\\src\\Concerns\\ManagesCustomer.php", "line": 97}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 252}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.766893, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ManagesCustomer.php:97", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/cashier/src/Concerns/ManagesCustomer.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\cashier\\src\\Concerns\\ManagesCustomer.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fcashier%2Fsrc%2FConcerns%2FManagesCustomer.php&line=97", "ajax": false, "filename": "ManagesCustomer.php", "line": "97"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 86.581, "width_percent": 0.703}, {"sql": "update `organizations` set `trial_ends_at` = '2025-06-10 07:17:58', `organizations`.`updated_at` = '2025-05-27 07:17:58' where `id` = 11", "type": "query", "params": [], "bindings": ["2025-06-10 07:17:58", "2025-05-27 07:17:58", 11], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 254}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.7729218, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Organization.php:254", "source": {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 254}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=254", "ajax": false, "filename": "Organization.php", "line": "254"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 87.284, "width_percent": 0.584}, {"sql": "select * from `users` where `users`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 263}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.776629, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Organization.php:263", "source": {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 263}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=263", "ajax": false, "filename": "Organization.php", "line": "263"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 87.867, "width_percent": 0.534}, {"sql": "select * from `organization_user` where `organization_user`.`organization_id` = 11", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.780051, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Organization.php:277", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=277", "ajax": false, "filename": "Organization.php", "line": "277"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 88.402, "width_percent": 0.95}, {"sql": "insert into `organization_user` (`organization_id`, `user_id`) values (11, 3)", "type": "query", "params": [], "bindings": [11, 3], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.783886, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Organization.php:277", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=277", "ajax": false, "filename": "Organization.php", "line": "277"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 89.352, "width_percent": 0.426}, {"sql": "select `id` from `organization_user` where `user_id` = 3 and `organization_id` = 11 limit 1", "type": "query", "params": [], "bindings": [3, 11], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 280}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.788707, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Organization.php:280", "source": {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 280}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=280", "ajax": false, "filename": "Organization.php", "line": "280"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 89.777, "width_percent": 0.574}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 283}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.792744, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "Organization.php:283", "source": {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 283}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=283", "ajax": false, "filename": "Organization.php", "line": "283"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 90.351, "width_percent": 4.156}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (11, 1, '2025-05-27 07:17:58', '2025-05-27 07:17:58')", "type": "query", "params": [], "bindings": [11, 1, "2025-05-27 07:17:58", "2025-05-27 07:17:58"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.800525, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 94.508, "width_percent": 3.85}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (11, 2, '2025-05-27 07:17:58', '2025-05-27 07:17:58')", "type": "query", "params": [], "bindings": [11, 2, "2025-05-27 07:17:58", "2025-05-27 07:17:58"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.807478, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 98.357, "width_percent": 0.416}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (11, 3, '2025-05-27 07:17:58', '2025-05-27 07:17:58')", "type": "query", "params": [], "bindings": [11, 3, "2025-05-27 07:17:58", "2025-05-27 07:17:58"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.811086, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 98.773, "width_percent": 0.406}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (11, 4, '2025-05-27 07:17:58', '2025-05-27 07:17:58')", "type": "query", "params": [], "bindings": [11, 4, "2025-05-27 07:17:58", "2025-05-27 07:17:58"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.814487, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.179, "width_percent": 0.396}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (11, 5, '2025-05-27 07:17:58', '2025-05-27 07:17:58')", "type": "query", "params": [], "bindings": [11, 5, "2025-05-27 07:17:58", "2025-05-27 07:17:58"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8180468, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.574, "width_percent": 0.426}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 298}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.829991, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Organization.php:298", "source": {"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=298", "ajax": false, "filename": "Organization.php", "line": "298"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\Product\\AttributeFamily": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "App\\Models\\Product\\Attribute": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "App\\Models\\Organization\\Permission": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Product\\Family": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FFamily.php&line=1", "ajax": false, "filename": "Family.php", "line": "?"}}, "App\\Models\\Organization\\OrganizationUser": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganizationUser.php&line=1", "ajax": false, "filename": "OrganizationUser.php", "line": "?"}}}, "count": 22, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/organization\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq", "organization_id": "11"}, "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f027c85-b380-48cb-9db8-973b6de4e5a3\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/2024-12/organization", "status_code": "<pre class=sf-dump id=sf-dump-1262780832 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1262780832\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2095744543 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2095744543\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-874691267 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Tanzayb</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">usd</span>\"\n  \"<span class=sf-dump-key>separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n  \"<span class=sf-dump-key>weightUnit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">oz</span>\"\n  \"<span class=sf-dump-key>trial_ends_at</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-874691267\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-175323537 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkRIS1NvaXBoUGdiV1BoeGlFeklSd1E9PSIsInZhbHVlIjoiUUE5Ulhyc1dxVElhSTB3RGV3Y3FqN3ZSUGZEeWNDSWtxZnhRdU1PY21TaGhoNm9mQXBxdDE4VjRBWEduWmk2V3VMbWpTRmpKWlNmclVzdGFHdEhUNVhWa0ZsSVZGSjNHS0R1aUIxWThBVXJ5MkhyL3lvR2xMRDc2eWpyWjJJcC8iLCJtYWMiOiIyZjM4NWQ3ZmE4MDg5YjAwYTYwMzVlNzhhYTM4ZGU0NzdjNTU4MzRjNTEwMTliOWRkODEyOTFhMDM3NzdmZjhkIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost:8000/organization/select</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1317 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlBnUFZnSUIxd2RabTBsbnNiNVpmTUE9PSIsInZhbHVlIjoibEYxTENta2l3WE5hdnQ2UGVMeE9KU2lZdEE4YitHM1dsNlpJMDRpTUROUUtuc2FhU2l6Qnl4aklkYXZpK0l5WXhCT0g3Yi9UTE5XUXlpWUtUVFM4OXNkTG5BYnRtRlk2cUtuOUdKVC9NYmpQRmNSSWFCL20wSEVBekE0WVQ4L1dpZ21xVkE2bmJDblVGdWtZYmdhenprZ1J4NzFFZjFHWEs4NDhGM0l6ZkJ5RGJSRVAwVTFpSUFjNE5jOGdXY2JLcU02VWRjc2lwZWQxRzBHNlZSUFEydERmdW5CN3UvdGJwT2VkMDIwd08xQT0iLCJtYWMiOiJiYmM1ZTEzODhjYjA3MzUyMDE3Zjc0ZTI0YWE3N2RjNjUxYmJjOTgzOWZkYjJiNWI0ZTIzNWU0MjdiZjFlOTBhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkRIS1NvaXBoUGdiV1BoeGlFeklSd1E9PSIsInZhbHVlIjoiUUE5Ulhyc1dxVElhSTB3RGV3Y3FqN3ZSUGZEeWNDSWtxZnhRdU1PY21TaGhoNm9mQXBxdDE4VjRBWEduWmk2V3VMbWpTRmpKWlNmclVzdGFHdEhUNVhWa0ZsSVZGSjNHS0R1aUIxWThBVXJ5MkhyL3lvR2xMRDc2eWpyWjJJcC8iLCJtYWMiOiIyZjM4NWQ3ZmE4MDg5YjAwYTYwMzVlNzhhYTM4ZGU0NzdjNTU4MzRjNTEwMTliOWRkODEyOTFhMDM3NzdmZjhkIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IkpsOWtoOTVuN0d1WGFFeUM1WXphMkE9PSIsInZhbHVlIjoiZTlYbDVtak5ZNk5PT2ZjMnVzRTY2RTJ1TG5JWWhvL3lkRE5YU1JxT3ByWHJYUTdaOTh1ZUZlUXl0N0lvcFYyM3pRUXdLTjNJNW5sbnEvR2MvT0N5eHJvaFZTNnhVZ3FZY2FwZmRiNW9WU2RIeHMwUjVVV1pPZVJiR1NWTXhGM3EiLCJtYWMiOiIzMmFiYmI5NjNjYWEzNzEzNDA4ZDg3YWVmYjFmODZiMTViNmExY2ZjMTQ4NmYwNjI3MjYzYzdkNjM5MTczNWQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-175323537\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-653020491 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">3|lyOnN9rFaNXD362RGbrGZMM746BZDyLiYeGFkfAHYF0FfGnJZdaE7RGsFJsg|$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k1WJWB6ZnSJcQ4dYcV3CZVMdsUzKOM46xMgpx7dI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-653020491\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-773631681 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 27 May 2025 07:17:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImpGQ3pLUm1vRVJuRWQ0azBNNFVUTkE9PSIsInZhbHVlIjoiQlJENU9YVVJFMS9OTWFzTnhTNnRvNHUzOFlCTmFHTVRjSStUZnNGcldWRm41UXE2VlJ4ZHJjRGNKeXkyWHZsSzJaVXJxK0lGQVpLbmFPYXFhaEFPMDBxUHBMc25QRE5leGJ0M1JyTDR5R1F2S28wVEhGRnZvV3pleWJUTlRjUisiLCJtYWMiOiIwNDgzMDliNWM5NGYwMDdlOTU4MzBjY2U5MTg5Y2YyZjA1ZmJhZmQ0ZmEzYjMyNjBmNzIwNDc0ZjE2YmZlNjVkIiwidGFnIjoiIn0%3D; expires=Tue, 27 May 2025 09:17:58 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6IkszdHhFbm5IdFJpek1tNXlZL1ZxYmc9PSIsInZhbHVlIjoiYitnSHRvTXIxVURoOTlzUy9YaU9Gc0JZQTgzOFQyQ1pXclE4dmxvdUhTQkpjOGwvbzJ5TmVDZVZEMzQ1RmdHR1FEWW5QSVZQbEJWbSt2TnBlSENvdC9qWmZWRnNYbm1jZEIwRmp6ZFk2VERmYkZLdGdPSi81UkV0dkZNMUR5YzQiLCJtYWMiOiIzZWE4MzZkZTcxZDFjN2UzZTRhYWI5NDIxMmNhNTE3YjMwZDgyNmUzNmFiNWJlNWIyZWUwNGYxMTE0YzNmMDJkIiwidGFnIjoiIn0%3D; expires=Tue, 27 May 2025 09:17:58 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImpGQ3pLUm1vRVJuRWQ0azBNNFVUTkE9PSIsInZhbHVlIjoiQlJENU9YVVJFMS9OTWFzTnhTNnRvNHUzOFlCTmFHTVRjSStUZnNGcldWRm41UXE2VlJ4ZHJjRGNKeXkyWHZsSzJaVXJxK0lGQVpLbmFPYXFhaEFPMDBxUHBMc25QRE5leGJ0M1JyTDR5R1F2S28wVEhGRnZvV3pleWJUTlRjUisiLCJtYWMiOiIwNDgzMDliNWM5NGYwMDdlOTU4MzBjY2U5MTg5Y2YyZjA1ZmJhZmQ0ZmEzYjMyNjBmNzIwNDc0ZjE2YmZlNjVkIiwidGFnIjoiIn0%3D; expires=Tue, 27-May-2025 09:17:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6IkszdHhFbm5IdFJpek1tNXlZL1ZxYmc9PSIsInZhbHVlIjoiYitnSHRvTXIxVURoOTlzUy9YaU9Gc0JZQTgzOFQyQ1pXclE4dmxvdUhTQkpjOGwvbzJ5TmVDZVZEMzQ1RmdHR1FEWW5QSVZQbEJWbSt2TnBlSENvdC9qWmZWRnNYbm1jZEIwRmp6ZFk2VERmYkZLdGdPSi81UkV0dkZNMUR5YzQiLCJtYWMiOiIzZWE4MzZkZTcxZDFjN2UzZTRhYWI5NDIxMmNhNTE3YjMwZDgyNmUzNmFiNWJlNWIyZWUwNGYxMTE0YzNmMDJkIiwidGFnIjoiIn0%3D; expires=Tue, 27-May-2025 09:17:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-773631681\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://localhost:8000/api/2024-12/organization</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq</span>\"\n  \"<span class=sf-dump-key>organization_id</span>\" => <span class=sf-dump-num>11</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}