<?php

namespace App\Exports;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithMapping;


class ProductsExport implements FromCollection , WithMapping ,WithHeadings
{


    use Exportable;
    private $products;
    private $heading_attributes;


    public function __construct(Collection $products,$heading_attributes) {
        $this->products = $products;
        $this->heading_attributes = $heading_attributes;
    }


    public function collection()
    {
        return $this->products;
    }




    /*
     * method for re-arrange product record array according to heading
     *
     *
     * @return array
     */
    function custom_array_merge($array1, $array2): array
    {
        $result = Array();
        foreach ($array1 as $key_1 => $value_1) {
            $result[$key_1] = '';
            foreach ($array2 as $key_2 => $value_2) {

                if($value_1 === $key_2) {

                    $result[$key_1] = $value_2;

                    break;

                }
            }
        }
        return $result;
    }



    /*
     * method for setting Mapping
     *
     *
     * @return array
     */
    public function map($row): array
    {
        $heading_array = $this->headings();
        return $this->custom_array_merge($heading_array,$row);
    }


    /*
     * method for defining headings that we want
     *
     *
     * @return array
     */
    public function headings(): array
    {
        $heading = [];
        return array_merge($heading, $this->heading_attributes);
    }



}
