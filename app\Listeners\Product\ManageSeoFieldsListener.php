<?php

namespace App\Listeners\Product;

use App\Models\Product\Attribute;
use App\Models\Product\AttributeFamily;
use App\Models\Product\AttributeFamilyProductVersion;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;


class ManageSeoFieldsListener
{
    public $seo_url, $seo_description, $seo_title, $seo_keyword;
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        /*Below code is being used to check seo fields if user erase data from them*/
        $attributes = Attribute::whereIn('handle', ['seo_url', 'seo_title', 'seo_description', 'seo_keyword'])->pluck('id', 'handle');
        $attributeFamilies = AttributeFamily::whereIn('attribute_id', $attributes)->pluck('id', 'attribute_id');
        if ($attributes->count() === 3 && $attributeFamilies->count() === 3 && isset($event->product->id)) {
            $this->seo_url = AttributeFamilyProductVersion::where('product_id', $event->product->id)
                ->where('attribute_family_id', $attributeFamilies[$attributes['seo_url']])
                ->value('value');
            $this->seo_title = AttributeFamilyProductVersion::where('product_id', $event->product->id)
                ->where('attribute_family_id', $attributeFamilies[$attributes['seo_title']])
                ->value('value');
            $this->seo_description = AttributeFamilyProductVersion::where('product_id', $event->product->id)
                ->where('attribute_family_id', $attributeFamilies[$attributes['seo_description']])
                ->value('value');
            $this->seo_keyword = AttributeFamilyProductVersion::where('product_id', $event->product->id)
                ->where('attribute_family_id', $attributeFamilies[$attributes['seo_keyword']])
                ->value('value');
        }

        /*Above code is being used to check seo fields if user erase data from them*/


        try {
            // if ( $event->product->versions->first() ) {


                $versions_ids = [];
                if(isset($event->version_ids)){
                    if(is_iterable(isset($event->version_ids))){
                        $versions_ids = $event->version_ids;
                    }
                    else{
                        $versions_ids = [$event->version_ids];
                    }

                }


                $versions_ids = empty($versions_ids) ? [$event->product->versions->first()->id] : $versions_ids;
                $product = $event->product->get_product_version($event->product->id,$versions_ids);

                foreach($versions_ids as $versions_id){
                    if(!$product->get_seo_url() && empty($this->seo_url)) {
                        $product->set_seo_url($product->getNameVersionWise($versions_id));
                    }
                    if(!$product->get_seo_description() &&  empty($this->seo_description)) {
                        $product->set_seo_description($product->get_description());
                    }
                    if(!$product->get_seo_title() && empty($this->seo_title)) {
                        $product->set_seo_title($product->getNameVersionWise($versions_id));
                    }
                    if(!$product->get_seo_keyword() && empty($this->seo_keyword)) {
                        $product->set_seo_keyword($product->get_seo_keyword());
                    }
                }


            // }
        } catch (\Exception $e) {
            return $e->getMessage();
        }


    }
}
