<?php

namespace App\Listeners;

use App\Models\Location\Location;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Events\LocationEvent;
use Illuminate\Support\Facades\Log;


class LocationEventListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param LocationEvent $event
     * @return void
     */
    public function handle(LocationEvent $event)
    {
        $obj = null;
        $id = null;
        if (!$event->data) {
            Log::warning("Location data not available");
            return;
        }
        foreach ($event->data as $data) {
            if (isset($data["id"]) || isset($data["name"])) {
                $obj = Location::CreateOrUpdate($data, $event->channel);
                $id  = $obj?->id ?? null;
            } else {
                Log::warning('Both "id", "name" keys and data of Location are required.');
            }
            if ($id) {
                if ($event->refresh) {
                    $obj->channelLocation()->sync([
                        $event->channel->id  => ['store_type' => $data['store_type'] ?? null, 'store_connect_id' => $data['store_connect_id'] ?? null]
                    ]);
                } else {
                    $obj->channelLocation()->syncWithoutDetaching([
                        $event->channel->id => ['store_type' => $data['store_type']  ?? null, 'store_connect_id' => $data['store_connect_id'] ?? null]
                    ]);
                }
            }
        }
    }
}
