<?php

namespace App\Models\Product;

use Carbon\Carbon;
use Apimio\Gallery\Models\File;
use App\Models\Channel\Channel;
use Illuminate\Support\Facades\Log;
use App\Models\ProductVariantSetting;
use App\Models\Channel\ChannelProduct;
use App\Models\Channel\ChannelVariant;
use App\Models\Channel\ChannelLocation;
use Illuminate\Database\Eloquent\Model;
use App\Events\ChannelUpdateStatusEvent;
use App\Models\Organization\Organization;
use Illuminate\Support\Facades\Validator;
use App\Models\Channel\ChannelProductStatus;
use App\Events\Product\CalculateScore;
use Auth;

use function App\Http\Controllers\Product\combinations;

class Variant extends Model
{
    protected $guarded = [];
    private $data, $filter;
    private const VARIANT_MAX_LIMIT = 100;

    protected $inventory_created_token = false;
    protected $organization_id = null;

    /**
     * Constructor for storing data and applying post logic
     * to organization structure.
     *
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->organization_id = auth()->user()->organization_id ?? null;
    }

    public function setInventoryCreatedTokenWithOrganization($token = false, $organization_id = null)
    {
        $this->inventory_created_token = $token;
        $this->organization_id = $organization_id ?? $this->organization_id;
    }

    public static function boot()
    {
            parent::boot();

            static::created(function ($model) {
                if($model->inventory_created_token && $model->organization_id){
                    $productChannels = ChannelProduct::where('product_id', $model->product_id)->get();
                    if(!empty($productChannels)){
                        foreach ($productChannels as $key => $channel) {
                            $channelLocations = ChannelLocation::where('channel_id',$channel->channel_id)->get();
                            foreach ($channelLocations as $key => $location) {
                                    $inventory = new Inventory();
                                    $inventory->organization_id = $model->organization_id;
                                    $inventory->product_id = $model->product_id;
                                    $inventory->variant_id = $model->id;
                                    $inventory->location_id = $location->id;
                                    $inventory->store_type = 'shopify';
                                    $inventory->available_quantity = null;
                                    $inventory->save();
                            }
                        }
                    } else {
                        Log::warning("No channel found for product id: ".$model->product_id);
                    }
                }

                });
}


    /**
     * Set data attribute.
     *
     *
     * @param array
     * @return void
     */
    public function set_data(array $data)
    {
        $this->data = $data;
        return $this;
    }

    /**
     * Get data attribute.
     *
     * @return string
     */
    public function get_data()
    {
        return $this->data;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        return [
            "sku" => "nullable|array",
            "sku.*" => "nullable|max:255",
            'name.*' => 'nullable|max:255',
            'quantity.*' => 'nullable|min:0|max:255',
            'weight.*' => 'nullable|min:0|max:255',
            'price.*' => 'nullable|min:0|max:255',
            'cost_price.*' => 'nullable|min:0|max:255',
            'barcode' => 'nullable|array',
            'barcode.*' => 'nullable|max:255',
            "file.*" => "mimes:png,jpg,jpeg,webp|max:2048"
        ];
    }

    public function messages()
    {
        return [

            'name.*.max' => 'Name length exceeds limit',
            'sku.*.max' => 'sku length exceeds limit',
            'quantity.*.required' => 'The quantity is required',
            'price.*.required' => 'The price is required',
            'cost_price.*.required' => 'The cost price is required',
            'barcode.*.max' => 'barcode length exceeds limit',
            "file.*.mimes" => "Image type is incorrect",
            "file.*.max" => "Image size is greater then 2mb",
            "quantity.*.min" => "Quantity cannot be negative",
            "weight.*.min" => "Weight cannot be negative",
            "price.*.min" => "Price cannot be negative",
            "cost_price.*.min" => "Compare at price cannot be negative",
            "quantity.*.max" => "Quantity cannot be greater then 255",
            "weight.*.max" => "Weight cannot be greater then 255",
            "price.*.max" => "Price cannot be greater then 255",
            "cost_price.*.max" => "Compare at price cannot be greater then 255",
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    public function validation()
    {
        $validator = Validator::make($this->data, $this->rules(), $this->messages());
        return $validator;
    }

    public function store($error_callback, $success_callback)
    {
        $validation = $this->validation();
        if ($validation->fails()) {
            return $error_callback($validation->errors());
        }
        $size = null;
        if (isset($this->data['id']))
            $size = sizeof($this->data['id']);
        for ($i = 0; $i < $size; $i++) {
            $file_data = null;

            if (isset($this->data['file'][$i])) {
                $file = new File();
                $file_data = $file->save_file($this->data['file'][$i]);
            }

            if (isset($this->data['id'])) {
                $variant = $this->find($this->data['id'][$i]);
                // $is_update = true;
            } else {
                $variant = $this;
                // $is_update = false;
            }


            $variant->sku = $this->data['sku'][$i];

            if (isset($file_data)) {
                $variant->file_id = $file_data->id;
                if ($variant->response) {
                    $variant_resp = json_decode($variant->response, true);
                    unset($variant_resp['image_id']);
                    $variant->response = json_encode($variant_resp);
                }
            }

            $variant->name = $this->data['name'][$i];
            if (isset($this->data['quantity'][$i])){
                $variant->quantity = $this->data['quantity'][$i];
            }else{
                $variant->quantity = null;
            }
            $variant->price = $this->data['price'][$i];
            $variant->cost_price = $this->data['cost_price'][$i];
            $variant->compare_at_price = $this->data['compare_at_price'][$i];
            $variant->weight = $this->data['weight'][$i];
            $variant->weight_unit = $this->data['weight_unit'][$i];
            $variant->barcode = $this->data['barcode'][$i];
            $variant->save();
            $product = Product::find($variant->product_id);
            // if ($product) {
                //     $product->updated_at = Carbon::now();
                //     $product->save();
                // }
            }
            if($product){
                event(new ChannelUpdateStatusEvent(
                    product: $product,
                    isUpdated: true
                ));
                event(new CalculateScore($product));
            }
        return $success_callback();
    }
    /**
     * @deprecated this is function is deprecated replace by event(new ChannelUpdateStatusEvent)
     */
    public function changeSyncChannelStatus($obj,$channels = null){
        if ($channels == null){
            $channels = Channel::pluck('id');
        }
        if(isset($channels) && count($channels) > 0){
            foreach($channels as $key => $channel_id){
                $channel_product = ChannelProduct::where([
                    'channel_id'=>$channel_id,
                    'product_id'=>$obj->id
                ])->first();
               if($channel_product){
                    $product_status = ChannelProductStatus::firstOrCreate(
                        [
                            'channel_product_id' => $channel_product->id,
                            'organization_id' => $obj->organization_id,
                            'type' => 'shopify'
                        ],
                        [
                            'created_at' => now(),
                            'updated_at' => now(),
                            'status' => 0,
                        ]
                    );

                    if($product_status){
                        $product_status->status = 0;
                        $product_status->save();
                    }
                }

            }
        }
    }



    public function set_id($id)
    {
        $this->id = $id;
        return $this->find($this->id);
    }

    public function delete_variant($id)
    {
        $this->where('id', $id)->delete();
    }

    private function combinations($arrays, $i = 0)
    {
        if (!isset($arrays[$i])) {
            return array();
        }
        if ($i == count($arrays) - 1) {
            return $arrays[$i];
        }

        // get combinations from subsequent arrays
        $tmp = $this->combinations($arrays, $i + 1);

        $result = array();

        // concat each array from tmp with each element from $arrays[$i]
        foreach ($arrays[$i] as $v) {
            foreach ($tmp as $t) {
                $result[] = is_array($t) ?
                    array_merge(array($v), $t) :
                    array($v, $t);
            }
        }

        return $result;
    }

    function update_existing_variant_combinations($existingVariants, $newCombinations, $attributes)
    {
        $last_index = 0;

        $newCombinations = array_values($newCombinations);

        if (count($newCombinations) > 0) {

            if (count($existingVariants) > 0) {

                $check = json_decode($existingVariants[0]->option);

                if (count($attributes) > count($check->attributes)) {

                    foreach ($existingVariants as $key => $variant) {

                        // Decode the JSON in the "options" field into a PHP object
                        $options = json_decode($variant->option);

                        // Update the options with the new combinations
                        $options->options = $this->format_array_to_no_indexes(array_unique($newCombinations[$key]));

                        $name = json_decode($options->options);

                        if (is_array($name)) {
                            $json = ["attributes" => $attributes, "options" => $name];
                        } else {
                            $json = ["attributes" => $attributes, "options" => array($name)];
                        }

                        // Encode the updated object back to JSON
                        $updatedOptionsJson = json_encode($json);

                        if (count($name) > 0) {
                            $name =  implode('-', $name);
                        } else {
                            $name = $options->options[0];
                        }

                        // Update the variant's options with the updated JSON
                        $variant->update(['option' => $updatedOptionsJson, 'name' => $name]);

                        $last_index = $key;
                    }
                } else {

                    return $newCombinations;
                }
            } else {

                return $newCombinations;
            }

            return $this->remove_updated_combinations_from_array(0, $last_index, $newCombinations);
        } else {

            return array();
        }
    }


    function format_array_to_no_indexes($inputArray)
    {
        // Use implode to join the elements with commas and place them inside a single set of square brackets
        $formattedString = '[';

        foreach ($inputArray as $input) {

            $formattedString .= '"' . $input . '"';

            if (!next($inputArray) === false) {
                $formattedString .= ",";
            }
        }
        $formattedString .= ']';
        return  $formattedString;
    }

    function remove_updated_combinations_from_array($start_index, $end_index, $array)
    {

        // Use array_splice to remove the elements
        $new_combination = array_splice($array, $end_index + 1);

        return  $new_combination;
    }

    function sort_array($array_1, $array_2, $check)
    {
        $array_1 =  json_decode($array_1[0]->option)->attributes;

        // Use array_map and json_encode to create a temporary array with unique string representations
        $tempArray = array_map('json_encode', $array_1);

        // Use array_unique to remove duplicates
        $uniqueArray = array_unique($tempArray);

        // Convert the unique array back to its original format by decoding JSON
        $array_1 = array_map('json_decode', $uniqueArray, array_fill(0, count($uniqueArray), true));

        // Create a mapping of IDs in array_1
        $idMap = [];
        foreach ($array_1 as $index => $item) {
            $idMap[$item['id']] = $index;
        }
        // Loop through array_2
        foreach ($array_2 as $item2) {

            $id2 = $item2['id'];

            if ($check == 'options') {
                // Check if the ID exists in array_1
                if (isset($idMap[$id2])) {
                    // Replace the existing item in array_1 with the item from array_2
                    $array_1[$idMap[$id2]] = $item2['options'];
                } else {
                    // If the ID doesn't exist in array_1, add the item from array_2 to array_1
                    $array_1[] = $item2['options'];
                }
            }

            if ($check == 'attributes') {
                // Check if the ID exists in array_1
                if (isset($idMap[$id2])) {
                    // Replace the existing item in array_1 with the item from array_2
                    $array_1[$idMap[$id2]] = $item2;
                } else {
                    // If the ID doesn't exist in array_1, add the item from array_2 to array_1
                    $array_1[] = $item2;
                }
            }
        }

        return $array_1;
    }

    public function save_variant_combinations($error_callback, $success_callback)
    {
            // Check for default variant and delete it if exists
            $default_variant = $this->where('product_id', $this->data['product_id'])->where('version_id', $this->data['version_id'])->where('name', 'Title')->first();
            if ($default_variant) {
                $default_variant->delete();
            }
        if ($this->data['attributes'] > 0) {
            $count = 0;
            foreach ($this->data['attributes'] as $attribute) {
                if (isset($attribute['options'])) {
                    foreach ($attribute['options'] as $options) {
                        $count++;
                    }
                }
            }
            if ($count < 2) {
                return $error_callback('Select at least two attribute.');
            }
        }
        $options = array_filter(array_map(function ($array) {
            if (isset($array["options"]))
                return $array["options"];
        }, $this->data['attributes']));

        $attributes = array_filter(array_map(function ($array) {
            if (isset($array["options"]))
                return [
                    "id" => $array["id"],
                    "name" => $array["name"],
                    "options" => $array["options"]
                ];
        }, $this->data["attributes"]));

        $final_options = array();
        foreach ($options as $option) {
            $final_options[] = $option;
        }

        //get the options from database and removal of attributes which are already created F
        $variants =   $this->where('product_id', $this->data['product_id'])->where('version_id', $this->data['version_id'])->get();

        if (count($variants) > 0) {

            $final_options = $this->sort_array($variants, $attributes, 'options');
            // $final_options = $sort_data['options'];
            $attributes =   $this->sort_array($variants, $attributes, 'attributes');
        }

        //get the attribute combination
        $attribute_options = $this->combinations($final_options);
        // dd($attribute_options);
        foreach ($variants as $variant) {
            $variant_decode = json_decode($variant->option, TRUE);
            $database_options = $variant_decode['options'];

            foreach ($attribute_options as $key => $option) {
                // for single option string returns so the below if condition will convert the string to array
                if (!is_array($option)) {
                    $option = [$option];
                }

                // removal of attributes which are already saved in database
                if ($database_options == $option) {
                    unset($attribute_options[$key]);
                }
            }
        }

        $count = $variants->count();

        if ($count == 0) {
            $count = 0;
        }

        $updatedCombinations = $this->update_existing_variant_combinations($variants, $attribute_options, $attributes);

        $options_list = [
            "attributes" => $attributes,
            "options" => (isset($updatedCombinations) ?  $updatedCombinations : array()),
        ];

        $variant_count = 0;

        $saved_variants_count = $this->where('product_id', $this->data['product_id'])->where('version_id', $this->data['version_id'])->count();


        foreach ($options_list["options"] as $option) {
            if ($saved_variants_count < self::VARIANT_MAX_LIMIT) {
                // if ($variant_count < Organization::find($this->data['organization_id'])->as_subscription()->limit_remaining_products()) {
                    if (is_array($option)) {
                        $json = ["attributes" => $options_list["attributes"], "options" => $option];
                    } else {
                        $json = ["attributes" => $options_list["attributes"], "options" => array($option)];
                    }
                    foreach ($json["attributes"] as $key => $attribute) {
                        if(isset($attribute["name"])){
                            unset($attribute["name"]);
                        }
                        if(isset($attribute["options"])){
                            unset($attribute["options"]);
                        }
                        $json["attributes"][$key] = $attribute;
                    }
                    $json = json_encode($json);

                    if (is_array($option))
                        $name = implode("-", $option);
                    else
                        $name = $option;
                    $product = Product::findOrFail($this->data['product_id']);


                    $variant_obj = new Variant();
                    $variant_obj->setInventoryCreatedTokenWithOrganization(true, $this->data['organization_id'] ?? null);

                    $variant_obj->product_id = $this->data['product_id'] ?? null;
                    $variant_obj->version_id = $this->data['version_id'] ?? null;
                    $variant_obj->option = $json;
                    $variant_obj->name = $name;
                    $variant_obj->sku = $product->sku . "-" . ++$count;
                    $variant_obj->save();
                    // $variant_obj->create([
                    //     "product_id" => $this->data['product_id'],
                    //     "version_id" => $this->data['version_id'],
                    //     "option" => $json,
                    //     "name" => $name,
                    //     "sku" => $product->sku . "-" . ++$count
                    // ]);

                    $variant_count++;
                    $saved_variants_count++;
                // } else {
                //     return $error_callback('Adding SKU limit reached.');
                // }
            } else {
                break;
            }
        }

        return $success_callback($this->data['product_id']);
    }

    public function delete_file($id)
    {
        $variant = $this->findOrFail($id);
        $file_id = $variant->file_id;
        $variant->file_id = null;
        $variant->save();
        if ($file_id) {
            File::where('id', $file_id)->delete();
        }
        if ($variant->channelVariant) {
            $variant->channelVariant->update(["store_connect_image_id" => null]);
        }
        return $variant;
    }
    public function fetch()
    {
        return $this->with('file')->where('product_id', $this->data['id'])->where('product_id', $this->data['version_id'])->get();
    }

    public function filter_already_assigned_variants($variants)
    {
        $variants_id = array(); //will add assigned variants id in this variable
        foreach ($variants as $key => $variant) {
            foreach (json_decode($variant, 'true')['attributes'] as $attributes) {
                if (isset($attributes['id']) && !in_array($attributes['id'], $variants_id)) {
                    $variants_id[$key] = $attributes['id'];
                }
            }
        }
        return $variants_id;
    }

    public function filter_assigned_options($variant_options)
    {
        $options = array();
                    $variantModel = new Variant();
                    $selected_attributes_id = $variantModel->filter_already_assigned_variants($variant_options);
                        $attributes =  Attribute::where('attribute_type_id', 13)
                        ->whereNot('is_default', 1)
                        ->with('attribute_options')
                         ->whereIn('id', $selected_attributes_id)
                         ->get();

                    foreach ($attributes as $attribute) {
                        foreach ($attribute->attribute_options as $option) {
                            foreach ($variant_options as $variant) {
                                $variantData = json_decode($variant, true);
                                foreach ($variantData['attributes'] as $variantAttribute) {
                                    if ($variantAttribute['id'] == $attribute->id && in_array($option->name, $variantData['options'])) {
                                        $options[$attribute['id']][] = $option->name;
                                        break 2;
                                    }
                                }
                            }
                        }
                    }
        return $options;
    }

    //====== relationships =====//

    public function file()
    {
        return $this->belongsTo(File::class, 'file_id', 'id');
    }

    public function inventories()
    {
        return $this->hasMany(Inventory::class);
    }

    public function channelVariant(){
        return $this->hasOne(ChannelVariant::class, 'variant_id', 'id');
    }

    public function settings()
    {
        return $this->morphOne(ProductVariantSetting::class, 'morphable')->withDefault([
            'track_quantity' => 1,
            'continue_selling' => 0,
        ]);
    }
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function decode_option_with_attached_attributes_name()
    {
        $decode_options = [];
        $decode_options = json_decode($this->option, true);
        if (isset($decode_options['attributes'])) {
            foreach ($decode_options['attributes'] as $key_attr => $attribute) {
                $attribute = Attribute::find($attribute['id']);
                if ($attribute){
                    $decode_options['attributes'][$key_attr]['name'] = $attribute->name;
                }
            }
        }
        return $decode_options;
    }
    public function version()
    {
        return $this->belongsTo(Version::class);
    }
}
