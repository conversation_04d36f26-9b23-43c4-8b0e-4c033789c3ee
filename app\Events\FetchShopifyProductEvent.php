<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class FetchShopifyProductEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $organization_id;

    public $user_id;
    
    public $channel_id;

    /**
     * Create a new event instance.
     *
     * @param $organization_id
     *
     * @return void
     */
    public function __construct( $organization_id, $user_id, $channel_id )
    {
        $this->organization_id = $organization_id;

        $this->user_id = $user_id; 

        $this->channel_id = $channel_id ;

    }
}
