<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Models\Organization\Permission;
use App\Models\Organization\TeamInvite;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class InviteTeamController extends Controller
{

    /**
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index(){
        if (Gate::denies('SubscriptionAccess', 'invite-team')) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        $this->authorize( 'invite_team' , [\App\Models\Organization\OrganizationUserPermission::class , auth()->user()->organization_id]);

        $invites = (new TeamInvite())->fetch(function($invite){
            return $invite->get();
        });
        return view('organization.invite_team.index', compact('invites'));
    }

    /**
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function create(){
        $this->authorize( 'invite_team' , [\App\Models\Organization\OrganizationUserPermission::class , auth()->user()->organization_id]);

        $permissions = Permission::query()->get();
        return view('organization.invite_team.create', compact('permissions'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return mixed
     */
    public function store(Request $request){
        $response = Gate::inspect('createTeamMember', TeamInvite::class);
        if ($response->allowed()) {
            return (new TeamInvite())->setData($request->all()+[
                    'organization_id'=>auth()->user()->organization_id ,
                    'own_id'=> auth()->id()
                ])
                ->store(
                    function($errors){
                        return back()->withErrors($errors)->withInput();
                    },
                    function(){
                        return redirect()->route('organization.invite_team.index')->withSuccess('Invitation send successfully.');
                    }
                );
        }
        else {
            return \redirect(route('dashboard'))->withErrors(['main' => $response->message()]);
        }

    }

    /**
     * @param $id
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function edit($id){
        $this->authorize( 'invite_team' , [\App\Models\Organization\OrganizationUserPermission::class , auth()->user()->organization_id]);

        $permissions = Permission::query()->get();
        $invite      = (new TeamInvite())->fetch(function($invite) use ($id){
            return $invite->findOrFail($id);
        });
        return view('organization.invite_team.edit', compact('permissions','invite'));

    }

    /**
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id): RedirectResponse
    {
        $this->authorize( 'invite_team' , [\App\Models\Organization\OrganizationUserPermission::class , auth()->user()->organization_id]);
        DB::beginTransaction();
        try{
            $invite =   TeamInvite::query()->findOrFail($id);
            $invite->delete();

            DB::commit();
            return back()->withSuccess('Member delete successfully.');
        }catch(Exception $e){
            DB::rollBack();
            return back()->withErrors(['main'=>'Something went Wrong.']);
        }

    }
}
