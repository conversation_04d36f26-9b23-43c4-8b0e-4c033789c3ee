<?php

namespace App\Classes\Billing;

use App\Classes\Plan\PlanClass;
use Illuminate\Support\Facades\DB;
use App\Models\Organization\Organization;
use Illuminate\Support\Facades\Validator;

class BillingBaseClass
{

    public $data;

    public $organization;

    public $plan;

    public $price;

    public $stripe_price;

    public $stripe_product_id;

    public $current_stripe_price;

    public function __construct()
    {
    }

    public function validation()
    {
        $validator = Validator::make($this->data, $this->rules());

        return $validator;
    }

    public function rules()
    {
        //validation constants
        return [
            'handle' => 'required|max:255',
            'organization_id' => 'required|max:255',
        ];
    }

    public function set_data($data)
    {
        $this->data = $data;
        return $this;
    }

    public function set_user($user)
    {
        $this->data['user'] = $user;
        return $this;
    }

    public function set_organization($organization_id = null)
    {
        $this->organization = Organization::where('id', $organization_id ?? $this->data["organization_id"] ?? null)->first();
        return $this;
    }

    public function setMonthlyPlan($handle = null)
    {
        $this->plan = PlanClass::$plans->where("handle", $handle)->first();
        if (isset($this->plan->stripe_monthly_id)) {
            $this->stripe_price = $this->plan->stripe_monthly_id;
        }
        if (isset($this->plan->stripe_product_id)) {
            $this->stripe_product_id = $this->plan->stripe_product_id;
        }
        if (isset($this->plan->price_per_month)) {
            $this->price = $this->plan->price_per_month;
        }
        if($this->organization && isset($this->organization->plan_handle)){
            $plan = PlanClass::$plans->where("handle",$this->organization->plan_handle)->first();
            $this->current_stripe_price = $plan->stripe_monthly_id;
        }else if($this->organization){
            $subscription = $this->organization->subscriptions->first();
            if ($subscription) {
                $plan = PlanClass::$plans->where("stripe_monthly_id",$subscription?->stripe_price)->first();
                if ($this->plan) {
                    $this->current_stripe_price = $plan->stripe_monthly_id;
                }
            }
        }
        // else{
        //     if ($this->organization->subscribed("default")) {
        //         $subscription = $this->organization->subscriptions()->first();
        //         if (isset($subscription)) {
        //             $priceIds = $subscription->items()->pluck('stripe_price')->toArray();
        //             if (isset($item)) {
        //                 $this->current_stripe_price = $item->stripe_price;
        //                 $plan = PlanClass::$plans->whereIn("stripe_monthly_id",$priceIds)->first();
        //                 if($plan){
        //                     $this->organization->plan_handle = $plan->handle;
        //                     $this->organization->save();
        //                 }
        //             }
        //         }
        //     }
        // }
        return $this;
    }

    public function setYearlyPlan($handle = null)
    {
        $this->plan = PlanClass::$plans->where("handle", $handle)->first();
        if (isset($this->plan->stripe_yearly_id)) {
            $this->stripe_price = $this->plan->stripe_yearly_id;
        }
        if (isset($this->plan->stripe_product_id)) {
            $this->stripe_product_id = $this->plan->stripe_product_id;
        }
        if (isset($this->plan->price_per_year)) {
            $this->price = $this->plan->price_per_year;
        }
        return $this;
    }
}
