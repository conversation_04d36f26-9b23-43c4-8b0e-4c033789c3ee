<?php

namespace App\Jobs;

use App\Exports\ProductsExport;
use App\Notifications\ApimioNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;


class ExportProducts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $data = $this->data;


        // TODO: remove this memory limit code in future
//        ini_set('memory_limit', '-1');

        (new \App\Classes\Export\ExportToCsv())->export_to_csv($data,
            function ($error){
                Log::error($error);
            },
            function ($success){
                return $success;
            });

//        if ($final_products_collection == 'template'){
//            return false;
//        }



    }
}
