<?php

namespace App\Jobs\Shopify;

use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use App\Models\Product\Product;
use App\Models\Product\Category;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class StoreCollectJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    public array $data = [];
    /**
     * Create a new job instance.
     */
    public function __construct(array $data = [])
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $categoryIds = [];
            foreach($this->data['collects'] as $collect){
              if(isset($collect->collection_id)){
                  $cat = Category::query()
                            ->where('organization_id',$this->data['organization_id'])
                            ->with('channel')
                            ->whereHas('channel',function ($q) use ($collect) {
                                $q->where('channel_id',$this->data['channel_id'])
                                    ->where('store_connect_type','shopify')
                                    ->where('store_connect_id',$collect->collection_id);
                            })
                            ->first();
                  if($cat){
                        $categoryIds[] = $cat->id;
                  }else{
                      Log::channel('shopify')->info('Collection not link with product');
                  }
    
              }
            }
    
            $product = Product::find($this->data['product_id']);
    
            if ($product) {
                $product->categories()->sync($categoryIds);
            }
        } catch (\Exception $e) {
            Log::channel('shopify')->error([$e->getMessage()]);
        }

    }
}
