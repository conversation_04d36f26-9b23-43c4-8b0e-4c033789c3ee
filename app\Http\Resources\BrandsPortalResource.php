<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class BrandsPortalResource extends JsonResource
{
    protected $channelIds;
    protected $templateIds;

    // Constructor to accept IDs as well
    public function __construct($resource, $channelIds, $templateIds)
    {
        // Ensure you call the parent constructor
        parent::__construct($resource);
        $this->channelIds = $channelIds;
        $this->templateIds = $templateIds;
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
                'id' => $this->id,
                'name' => $this->name,
                'url' => $this->url,
                'primary_color' => $this->primary_color,
                'logo_url' => $this->logo_url,
                'organization_id' => $this->organization_id,
                'file_id' => $this->file_id,
            'channelIds' => $this->channelIds,
            'templateIds' => $this->templateIds
        ];
    }
}
