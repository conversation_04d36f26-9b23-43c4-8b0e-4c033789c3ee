<?php

namespace App\Jobs;

use Apimio\MappingConnectorPackage\Http\Controllers\MappingFieldController;
use App\Classes\ImportExport;
use App\Exports\ProductsExport;
use App\Notifications\ApimioNotification;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ImportProducts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $data;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public array $request_data)
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        $request_data = $this->request_data;

        $mapping_field_package = new MappingFieldController();

        $request = new Request($request_data['all_data']);

        $this->data = $mapping_field_package->mapping_convert($request);

        // TODO: remove this memory limit code in future
        ini_set('memory_limit', '-1');
        try{
            $import_export = new ImportExport();
            $import_export->set_data($request_data)
                ->convert_CSV_to_apimio(
                    $this->data->put('organization_id' , $request_data['organization_id']),
                    // error function
                    function ($errors) use ($request_data) {
                        Log::channel('mapping')->error("ImportProducts Queue error for user ". $request_data['user']->id);
                        return $errors;
                    },

                    // success function
                    function ($success) use($import_export){
                        return $success;
                    }
                );
        }catch(\Exception $e){
            info("ImportProducts Queue error for user ".$e->getMessage());
        }
    }

}
