<?php

namespace App\Http\Controllers\Api;

use App\User;
use App\Models\Channel\ShopifyChannel;
use App\Models\Organization\Organization;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public static $NEW_VERSION_DATE = "2023-10-10 09:15:18";
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware("auth:sanctum");
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = $request->user();
        // Getting total products with their respective sku's
        $product = $user->products();

        $data['sku_count'] = $product->withCount('variants')->get()->sum(function ($product) {
            return $product->variants_count;
        });

        $channel = $user->channels()
            ->where('updated_at', '<', self::$NEW_VERSION_DATE)
            ->first();

        if (isset($channel)) {
            $shopify_channel = ShopifyChannel::query()->where('channel_id', $channel->id)->first();
            if (isset($shopify_channel)) {
                if ($shopify_channel->updated_at <  self::$NEW_VERSION_DATE) {
                    $show_popup = true;
                }
            }
        }

        return response([
            'message' => 'Dashboard data retrieved successfully',
            'data' => [
                'sku_count' => $data['sku_count'],
                'product_count' => $product->count() ?? null,
                'brand_count' => $user->brands()->count() ?? null,
                'images' =>  $user->files()->whereNotNull('link')->count() ?? null,
                'invites_list' => $user->teamInvites()->count() ?? null,
                'show_popup' => $show_popup ?? false,
                'shopify_channel' => $shopify_channel ?? null,
                'channels' =>  $user->channels()->count() ?? null,
            ]
        ]);
    }

    /**
     * Renders the dashboard view.
     *
     * @return \Inertia\Response
     */
    public function renderDashboard()
    {
        return Inertia::render('dashboard/Dashboard', [
            'title' => 'Dashboard'
        ]);
    }
    /**
     * Renders the welcome view.
     *
     * @return \Inertia\Response
     */
    public function renderWelcome()
    {
        return Inertia::render('welcome/Welcome');
    }

    /**
     * Renders the onboarding view.
     *
     * @return \Inertia\Response
     */
    public function renderOnboarding()
    {
        return Inertia::render('onboarding/Onboarding', [
            'title' => 'Onboarding'
        ]);
    }

    /**
     * Renders the onboarding products view.
     *
     * @return \Inertia\Response
     */
    public function renderOnboardingProducts()
    {
        return Inertia::render('shopifyproductsync/ShopifyProductsSync');
    }
    
    /**
     * get single user resource.
     */
    public function show(Request $request)
    {
        if (get_class($request->user()) == Organization::class) {
            $user = $request->user()->users;
        } elseif (get_class($request->user()) == User::class) {
            $user = $request->user();
        }
        return response([
            'message' => 'User data retrieved successfully',
            'user' => $user
        ]);
    }



    public function productQualityScore()
    {
        // Step 1: Compute the average score per product
        $subquery = auth()->user()->products()
            ->join('product_version', 'products.id', '=', 'product_version.product_id')
            ->selectRaw('products.id, AVG(product_version.score) as mean_score')
            ->groupBy('products.id')
            ->toBase(); // Convert Eloquent Builder to Query Builder

        // Step 2: Categorize products based on their mean_score
        $qualityCounts = DB::table(DB::raw("({$subquery->toSql()}) as avg_scores"))
            ->mergeBindings($subquery) // Properly merge query bindings
            ->selectRaw("
                SUM(CASE WHEN mean_score >= 90 THEN 1 ELSE 0 END) AS good,
                SUM(CASE WHEN mean_score >= 50 AND mean_score < 90 THEN 1 ELSE 0 END) AS fair,
                SUM(CASE WHEN mean_score < 50 THEN 1 ELSE 0 END) AS bad
            ")
            ->first();

        return response()->json([
            'message' => 'Product quality score retrieved successfully',
            'productQualityScore' => [
                'good' => (int)$qualityCounts->good ?? 0,
                'fair' => (int)$qualityCounts->fair ?? 0,
                'bad' => (int)$qualityCounts->bad ?? 0,
            ]
        ]);
    }

    public function imageQualityScore()
    {
        $files = auth()->user()->files()->get();
        $score = [
            'good' => 0,
            'fair' => 0,
            'bad' => 0
        ];
        foreach ($files as $file) {
            $imageQuality = $file->get_img_status($file);
            if ($imageQuality == 'approve') {
                $score['good']++;
            } elseif ($imageQuality == 'warning') {
                $score['fair']++;
            } elseif ($imageQuality == 'error') {
                $score['bad']++;
            }
        }

        return response([
            'message' => 'Image quality score retrieved successfully',
            'imageQualityScore' => $score
        ]);
    }

    public function shopifySyncStatus()
    {
        $channels = auth()->user()->channels()
            ->whereHas('shopify_channels')
            ->get();

        $syncStatus = $channels->map(function ($channel) {
            $statusCounts = [
                'not_synced' => 0,
                'synced' => 0,
                'in_process' => 0
            ];

            foreach ($channel->products as $product) {
                $status = $product->product_status->first();
                if ($status->status === 0) {
                    $statusCounts['not_synced']++;
                } elseif ($status->status === 1) {
                    $statusCounts['synced']++;
                } elseif ($status->status === -1) {
                    $statusCounts['in_process']++;
                }
            }

            return [
                'id' => $channel->id,
                'name' => $channel->name,
                'not_synced' => $statusCounts['not_synced'],
                'synced' => $statusCounts['synced'],
                'in_process' => $statusCounts['in_process']
            ];
        });

        return response()->json([
            'message' => 'Shopify sync status retrieved successfully',
            'shopifySyncStatus' => $syncStatus
        ]);
    }
}
