<?php

namespace App\Jobs\Shopify;

use App\Classes\Shopify\MetaFieldDefinition;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FetchMetaFieldDefinitions implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public $event)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

       $event =  $this->event;

        //save meta field definitions
        $metafield_definition =  new MetaFieldDefinition($event->channel_id ,$event->organization_id);
        $metafield_definition =  $metafield_definition->getDefinitions()->getAttributeArray();
        $metafield_definition->saveAttributeSet();
        $metafield_definition->store();
    }
}
