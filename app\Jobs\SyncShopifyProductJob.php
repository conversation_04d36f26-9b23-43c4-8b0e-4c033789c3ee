<?php

namespace App\Jobs;

use App\Traits\AWSSetting;
use Throwable;
use App\Models\BatchProgress;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use App\Jobs\Shopify\SyncQuantityJob;
use App\Models\Channel\ChannelProduct;
use App\Models\Channel\ShopifyChannel;
use Illuminate\Queue\SerializesModels;
use App\Jobs\Shopify\SyncSingleProduct;
use Illuminate\Queue\InteractsWithQueue;
use App\Notifications\ApimioNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\Channel\ChannelProductStatus;

class SyncShopifyProductJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, AWSSetting;

    public mixed $channel_id;
    private mixed $template_ids;
    private mixed $organization_id;
    public mixed $user;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public $data) {}

    /**
     * Execute the job.
     *
     * @return void
     * @throws Throwable
     */
    public function handle(): void
    {
        $channel_id = $this->data['channel_id'];
        (new ShopifyChannel())->changeQueueStatus($channel_id, 1);
        $data = $this->data;
        if (env('CUSTOM_QUEUE_WORKER') == 'local') {
            $batch = Bus::batch([])->then(function () use ($data, $channel_id) {

                // on success notification
                $details = [
                    'subject' => 'Syncing products to Shopify Successful.',
                    'greeting' => '',
                    'body' =>  "All products sync successfully to your shopify store.",
                    'thanks' => '',
                    'actionText' => 'view',
                    'actionURL' => '',
                    'user_id' => $data['user']->id,
                    'organization_id' => $data['organization_id'],
                ];

                $notifier = new ApimioNotification($details);

                //un-comment if you want to stop notification in email
                $notifier->only_db_notify(true);
                $data['user']->notify($notifier);

                // sleep for 60 second
                sleep(60);
                (new ShopifyChannel())->changeQueueStatus($channel_id, 0);
            })->dispatch();

            (new BatchProgress())->store(['user_id' => $data['user']->id, 'organization_id' => $data['organization_id'], 'batch_id' => $batch->id, 'type' => 'shopify_syncing']);
        }


        $product_jobs = [];
        foreach ($data['product_id'] as $product_id) {
            $this->data['product_id'] = $product_id;
            if (env('CUSTOM_QUEUE_WORKER') == 'local') {
                $batch->add(new SyncSingleProduct($this->data));
            } else {
                $product_jobs[] = new SyncSingleProduct($this->data);
            }
        }

        $quality_jobs = [];
        foreach ($data['product_id'] as $product_id) {
            if (env('CUSTOM_QUEUE_WORKER') == 'local') {
                $batch->add(new SyncQuantityJob(["product_id" => $product_id, "channel_id" => $channel_id]));
            } else {
                $quality_jobs[] = new SyncQuantityJob(["product_id" => $product_id, "channel_id" => $channel_id]);
            }
        }

        if (env('CUSTOM_QUEUE_WORKER') != 'local') {
            $this->dispatchJobsToFifoQueue($product_jobs, $channel_id, null, $data['queueUrl']);
            $this->dispatchJobsToFifoQueue($quality_jobs, $channel_id, null, $data['queueUrl']);
        }
    }
}
