<?php

namespace App\Classes\Shopify;

use App\Traits\Shopify\ShopifyAPI;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\Notification\ErrorLog;
use App\Models\Channel\ShopifyChannel;
use App\Models\Channel\ChannelLocation;

class SyncInventories
{
    use ShopifyAPI;

    protected $inventory;
    protected $channel_id;
    protected $location_id;

    public function __construct($inventory, $channel_id)
    {
        //set base url
        $shopify_channel = ShopifyChannel::query()->where('channel_id', $channel_id)->first();
        $this->setupCredentialsManually($shopify_channel->access_token, $shopify_channel->shop)->createBaseUrl();
        $this->inventory = $inventory;
        $this->channel_id = $channel_id;
        if ($this->inventory) {
            $this->location_id = ChannelLocation::where("id", $this->inventory->location_id)->first()?->store_connect_id;
        }
    }

    function connect()
    {
        try {
            if ($this->inventory) {
                $params = [
                    'inventory_item_id' => $this->inventory->store_connect_id,
                    'location_id' => $this->location_id,
                ];
                usleep(500000);
                info("Connect Inventory");
                info($this->base_url);
                $response = Http::retry(1, 1000)->post($this->base_url . "inventory_levels/connect.json",  $params);
                if ($response->successful()) {
                    $data = $response->json();
                    Log::channel("shopify")->info("Connect Success");
                    return $data;
                } else {
                    $json = $response->json();
                    // $error = "<ul>";
                    // if (!is_array($json["errors"])) {
                    //     if ($this->inventory->variant_id != null) {
                    //         $error .= '<li><b> variant name : ' . $this->inventory->variant->name . '  </b> with <b> location : </b> '.$this->inventory->location->name.' ,  <b> Error :</b>  '. $json["errors"] . "</li>";
                    //     } else {
                    //         $error .= '<li><b> product sku : ' . $this->inventory->product->sku . '  </b> with <b> location : </b> '.$this->inventory->location->name.' ,  <b> Error :</b>  '. $json["errors"] . "</li>";
                    //     }
                    // }else{
                    //     foreach ($json["errors"] as $key => $error_row) {
                    //         if (is_array($error_row)) {
                    //             if ($this->inventory->variant_id != null) {
                    //                 $error .= '<li><b> variant name : ' . $this->inventory->variant->name . '  </b> with <b> location : </b> '.$this->inventory->location->name.' ,  <b> Error :</b>  ' . implode("<br>", $error_row) . "</li>";
                    //             } else {
                    //                 $error .= '<li><b> product sku : ' . $this->inventory->product->sku . '  </b> with <b> location : </b> '.$this->inventory->location->name.' ,  <b> Error :</b>  '. implode("<br>", $error_row) . "</li>";
                    //             }
                    //         } else {
                    //             if ($this->inventory->variant_id != null) {
                    //                 $error .= '<li><b> variant name : ' . $this->inventory->variant->name . '  </b> with <b> location : </b> '.$this->inventory->location->name.' ,  <b> Error :</b>  '. $error_row . "</li>";
                    //             } else {
                    //                 $error .= '<li><b> product sku : ' . $this->inventory->product->sku . '  </b> with <b> location : </b> '.$this->inventory->location->name.' ,  <b> Error :</b>  '. $error_row . "</li>";
                    //             }
                    //         }
                    //     }
                    // }


                    // $data = [
                    //     'organization_id' => $this->inventory->organization_id,
                    //     'description' => "There is an error in inventories while Connecting inventory back to shopify with <b> sku : {$this->inventory->product->sku} </b>.<br>  {$error}",
                    //     'type' => 'shopify',
                    //     'link' => route('products.edit', $this->inventory->product_id),
                    //     'link_text' => 'View Product',
                    //     'status' => 'error',
                    // ];

                    // $error = new ErrorLog();
                    // $error->setData($data)->store(function ($error) {
                    //     Log::channel('shopify')->info('Error in saving ErrorLogs.');
                    //     Log::channel('shopify')->error($error);
                    // }, function () {
                    // });

                    Log::channel('shopify')->error($response->json());
                }
                return null;
            }
            return null;
        } catch (\InvalidArgumentException | \Exception $e) {
            Log::error($e);
            return false;
        }
    }

    function set()
    {
        try {
            if ($this->inventory) {
                // if ($this->inventory->available_quantity) {
                    $params = [
                        'inventory_item_id' => $this->inventory->store_connect_id,
                        'location_id' => $this->location_id,
                        'available' => ($this->inventory->available_quantity ?? 0),
                    ];
                    usleep(500000);
                    return Http::retry(1, 1000)->post($this->base_url . "inventory_levels/set.json",  $params);

                // }
            }
            return null;
        } catch (\InvalidArgumentException | \Exception $e) {
            Log::error($e);
            return false;
        }
    }


    function setForWebhook()
    {
        $location_id = ChannelLocation::where("id", $this->inventory->location_id)->first()->store_connect_id;
        try {
            if ($this->inventory) {
                // if ($this->inventory->available_quantity) {
                $params = [
                    'inventory_item_id' => $this->inventory->store_connect_id,
                    'location_id' => $location_id,
                    'available' => ($this->inventory->available_quantity ?? 0),
                ];
                usleep(500000);
                return Http::retry(1, 1000)->post($this->base_url . "inventory_levels/set.json",  $params);
                // }
            }
            return null;
        } catch (\InvalidArgumentException | \Exception $e) {
            Log::error($e);
            return false;
        }
    }
}
