<?php

namespace App\Models\Notification;

use App\Models\Organization\Organization;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use JetBrains\PhpStorm\ArrayShape;

class ErrorLog extends Model
{
    use HasFactory;

    public $guarded = [];

    public array $data;

    /**
     * @return void
     */
    public static function boot(): void
    {
        parent::boot();
        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id',  Auth::user()->organization_id);
            }
        });
    }

    /**
     * @param array $data
     * @return \App\Models\Organization\ErrorLog
     */
    public function setData(array $data): static
    {
        $this->data = $data;
        return $this;
    }

    /**
     * @return \Illuminate\Validation\Validator
     */
    public function validation(): \Illuminate\Validation\Validator
    {
        return Validator::make($this->data, $this->rules());
    }

    /**
     * @return array
     */
    #[ArrayShape(['description' => "string", 'type' => "string", 'link' => "string", 'link_text' => "string", "status"=>"string"])] public function rules(): array
    {
        return [
            'description' => 'required|max:6500',
            'type' => 'required|max:255',
            'link' =>'nullable|max:255',
            'status' =>['nullable',Rule::in(['warning','error','success','info'])],
            'link_text' =>'required_with:link|max:255',
        ];
    }


    /**
     * @param $error_callback
     * @param $success_callback
     * @return mixed
     */
    public function store($error_callback, $success_callback): mixed
    {

        $validation = $this->validation();
        if($validation->fails()) {
            return $error_callback($validation->errors());
        }
        DB::beginTransaction();
        try{
            $obj = $this;
            if(isset($this->data['organization_id'])) {
                $obj->organization_id = $this->data['organization_id'];
            }
            $obj->description = $this->data['description'];
            $obj->type = $this->data['type'];
            $obj->link = $this->data['link'] ?? null;
            $obj->link_text = $this->data['link_text'] ?? null;
            $obj->status = $this->data['status'] ?? null;
            $obj->save();

            DB::commit();
            //success callback function
            return $success_callback($obj);

        }catch (\Exception $exception){
            DB::rollBack();
            //error callback
            Log::error($exception);
            return  $error_callback(['main'=>'something went wrong.']);
        }
    }

    public function saveErrorLog(array $data)
    {
        $error = new ErrorLog();
        $error->setData($data)->store(function ($error) {
            Log::channel('shopify')->info('Error in saving ErrorLogs.');
            Log::channel('shopify')->error($error);
        }, function () {
        });
    }


    /** ==============================
     *
     *Relationship
     *
    ================================== **/
    /**
     * get the organization data
     * @return HasOne
     */
    public function organization(): HasOne
    {
        return $this->hasOne(Organization::class);
    }
}
