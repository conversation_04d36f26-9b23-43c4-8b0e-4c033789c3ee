<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;
use App\User;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

        /**
     * Add additional validation after the default validation passes.
     */
    protected function withValidator(Validator $validator)
    {
        $validator->after(function ($validator) {
            $data = $validator->getData();
            if (User::where('email', $data['email'])->whereNotNull('password')->exists()) {
                $validator->errors()->add('email', 'The email has already been taken.');
            }
        });
    }


    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'fname' => ['required', 'string', 'max:75', 'regex:/^[a-zA-Z]+$/u'],
            'lname' => ['nullable', 'string', 'max:75', 'regex:/^[a-zA-Z]+$/u'],
            'phone' => 'required|regex:/^([0-9\s\-\+\(\)]*)$/|min:10|max:20',
            'email' => ['required', 'string', 'email', 'max:255'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ];
    }

    public function messages()
    {
        return  [  'fname.required' => 'First name cannot be empty',
        'fname.regex' => 'Please Enter Valid First Name',
        'fname.min' => 'First name should not be less than 3 characters',
        'fname.max' => 'First name should not be greater than 255 characters',
        'lname.required' => 'Last name cannot be empty',
        'lname.regex' => 'Please Enter Valid Last Name',
        'lname.min' => 'Last name should not be less than 3 characters',
        'lname.max' => 'Last name should not be greater than 255 characters',
        'email.required' => 'Email cannot be empty',
        'email.max' => 'Email should not be greater than 255 characters',
        'phone.max' => 'Phone number should not be greater than 20 digits',
        'phone.min' => 'Phone number should not be less than 10 digits'
    ];
    }
}
