<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\User;


class OrganizationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Adjust authorization logic as needed
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        $data = [
            'trial_ends_at' => now()->addDays(14)->format('Y-m-d'),
        ];

        if ($organizationId = get_class($this->user()) == User::class ? ($this->user()->organization_id ?? null) : $this->user()->id) {
            $data['organization_id'] = $organizationId;
        }

        $this->merge($data);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'max:255',
                Rule::unique('organizations', 'name')
                ->where(function ($query) {
                    $query->where(
                        'id',
                        (auth()->user()->organization_id??auth()->user()->id)
                    );
                })
                ->ignore($this->route('organization')),
            ],
            'region' => 'nullable|max:5',
            'units' => 'nullable|max:20',
            'currency' => 'nullable|max:3',
            'separator' => 'nullable',
            'phone' => 'nullable|regex:/^([0-9\s\-\+\(\)]*)$/|max:20',  'phone' => [
                'nullable',
                'regex:/^([0-9\s\-\+\(\)]*)$/',
                'max:20',
            ],
            'trial_ends_at' => 'nullable|date_format:Y-m-d', // Ensures valid date format
            'language' => 'nullable' // Uncomment if needed
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The organization name is required.',
            'name.max' => 'The organization name may not exceed 255 characters.',
            'region.max' => 'The region code may not exceed 5 characters.',
            'units.max' => 'The units value may not exceed 20 characters.',
            'currency.max' => 'The currency code may not exceed 3 characters.',
            'phone.max' => 'Phone number should not be greater than 20 digits.',
            'phone.regex' => 'Phone number format is invalid.',
            'trial_ends_at.date_format' => 'The trial end date must be in the format YYYY-MM-DD.',
        ];
    }

}
