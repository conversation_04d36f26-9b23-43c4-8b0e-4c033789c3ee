<?php

namespace App\Http\Controllers\Api\Product;

use App\Classes\ProductFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Product\ProductRequest;
use App\Http\Resources\ProductResource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use App\Models\Product\Product;

class ProductController extends Controller
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware("auth:sanctum");
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();

            if (get_class($user) != 'App\Models\Organization\Organization') {
                $this->check_user_organization($user, $request);
            }
            // Validate the request parameters
            $validatedData = $request->validate([
                'id' => 'integer|exists:products,id',
                'paginate' => 'integer|min:1|max:255',
                'status' => 'boolean',
            ]);
            // Query to get products without global scopes
            $products = $user->products();

            // Apply filters if provided
            if ($request->filled("id")) {
                $products->where("id", $request->get("id"));
            }

            if ($request->filled("handle")) {
                $products->where("sku", "LIKE", "%" . $request->get("handle")  . "%");
            }

            if ($request->filled("tableSearch")) {
                $searchTerm = $request->get("tableSearch");
                $products->where(function ($query) use ($searchTerm) {
                    $query->where("sku", "LIKE", "%" . $searchTerm . "%")
                        ->orWhereHas("variants", function ($query) use ($searchTerm) {
                            $query->where("sku", "LIKE", "%" . $searchTerm . "%")
                                ->orWhere("name", "LIKE", "%" . $searchTerm . "%");
                        })
                        ->orWhereHas("values", function ($query) use ($searchTerm) {
                            $query->where("value", "LIKE", "%" . $searchTerm . "%");
                        });
                });
            }

            // Search by multiple IDs for categories, vendors, brands, and versions
            if ($request->filled("category_ids")) {
                if (is_array($request->category_ids)) {
                    $categoryIds = $request->get('category_ids');
                } else {
                    $categoryIds = explode(',', $request->get('category_ids'));
                }
                $products->whereHas('categories', function (Builder $q) use ($categoryIds) {
                    $q->whereIn('categories.id', $categoryIds);
                });
            }

            if ($request->filled("vendor_ids")) {
                if (is_array($request->vendor_ids)) {
                    $vendorIds = $request->get('vendor_ids');
                } else {
                    $vendorIds = explode(',', $request->get('vendor_ids'));
                }
                $products->whereHas('invites', function (Builder $q) use ($vendorIds) {
                    $q->whereIn('invites.id', $vendorIds);
                });
            }

            if ($request->filled("brand_ids")) {
                if (is_array($request->brand_ids)) {
                    $brandIds = $request->get('brand_ids');
                } else {
                    $brandIds = explode(',', $request->get('brand_ids'));
                }
                $products->whereHas('brands', function (Builder $q) use ($brandIds) {
                    $q->whereIn('brands.id', $brandIds);
                });
            }

            if ($request->filled("version_ids")) {
                if (is_array($request->version_ids)) {
                    $versionIds = $request->get('version_ids');
                } else {
                    $versionIds = explode(',', $request->get('version_ids'));
                }
                $products->whereHas('versions', function (Builder $q) use ($versionIds) {
                    $q->whereIn('versions.id', $versionIds);
                });
            }

            if ($request->filled("channel_ids")) {
                if (is_array($request->channel_ids)) {
                    $channelIds = $request->get('channel_ids');
                } else {
                    $channelIds = explode(',', $request->get('channel_ids'));
                }
                $products->whereHas('channels', function (Builder $q) use ($channelIds) {
                    $q->whereIn('channels.id', $channelIds);
                });
            }

            if ($request->filled("status")) {
                $products->where("status", $request->get("status"));
            }


            if ($request->filled("score")) {
                $targetScore = $request->get("score");

                // Filter products based on average score
                $products->whereHas('Product_version', function (Builder $q) use ($targetScore) {
                    $q->select('product_id')
                        ->groupBy('product_id')
                        ->havingRaw('AVG(score) >= ?', [0]);

                    // Apply the score filter based on the provided category
                    if ($targetScore === 'good') {
                        $q->havingRaw('AVG(score) >= ?', [90]);
                    } elseif ($targetScore === 'fair') {
                        $q->havingRaw('AVG(score) BETWEEN ? AND ?', [50, 89]);
                    } elseif ($targetScore === 'bad') {
                        $q->havingRaw('AVG(score) < ?', [50]);
                    }
                });
            }


            // filters
            if ($request->filled("filters")) {
                $filters = new ProductFilter();
                $filters->apply_filters($products, $request->get("filters"));
            }

            // Determine the number of items per page
            $paginate = $request->filled("paginate") ? $request->get("paginate") : 255;


            // Get the paginated data
            $paginatedProducts = $products->paginate($paginate);

            // Prepare pagination details
            $pagination = [
                'current_page' => $paginatedProducts->currentPage(),
                'last_page' => $paginatedProducts->lastPage(),
                'per_page' => $paginatedProducts->perPage(),
                'total' => $paginatedProducts->total(),
            ];

            // Return the response with pagination details
            return response(
                [
                    'message' => 'Products retrieved successfully',
                    'products' => ProductResource::collection($paginatedProducts),
                    'pagination' => $pagination
                ]
            );
        } catch (\Exception $e) {
            return response(
                [
                    'message' => 'Failed to retrieve products',
                    'error' => $e->getMessage()
                ],
                $e->getCode() != 0 ? $e->getCode() : 500
            );
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ProductRequest $request)
    {
        // $product = $request->user()->products()->create($request->all());
        $product = new Product();
        $data = $request->all();
        $data = $data+ ['organization_id'=>auth()->user()->id];

        return $product->set_data($data)->store(
            // when error
            function ($errors) {
                return response([
                    'message' => 'Failed to create product',
                    'errors' => $errors
                ], 422);
            },
            // when success
            function ($product) {
                return response([
                    'product' => new ProductResource($product),
                    'message' => 'Product created successfully'
                ]);
            }
        );

    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, string $id)
    {
        $product = $request->user()->products()->findorfail($id);
        $product = $product->paginate(1);
        return response([
            'product' => new ProductResource($product),
            'message' => 'Product retireved successfully'
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ProductRequest $request, string $id)
    {
        $product = $request->user()->products()->update($request->all());
        return response([
            'product' => new ProductResource($product),
            'message' => 'Product created successfully'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, string $id)
    {
        $product = $request->user()->products()->findOrFail($id);
        $product->delete();
        return response([
            'message' => 'Product deleted successfully'
        ]);
    }
}
