<?php

namespace App\Listeners\Cashier;

use Log;
use <PERSON><PERSON>\Cashier\Cashier;
use App\Classes\Plan\PlanClass;
use Illuminate\Queue\InteractsWithQueue;
use <PERSON><PERSON>\Cashier\Events\WebhookHandled;
use Illuminate\Contracts\Queue\ShouldQueue;

class HandleWebhookListener
{

    /**
     * Handle the event.
     */
    public function handle(WebhookHandled $event): void
    {
        Log::info("WebhookHandled Triggered");
        $data = $event->payload['data']['object'];
        $organization = $this->getUserByStripeId($data['customer']);
        if ($organization) {
            $firstItem = $data['items']['data'][0];
            $stripe_product = $firstItem['price']['product'] ?? null;
            $plan = null;
            $handle = null;
            $productIds = [];
            if (isset($stripe_product)) {
                foreach ($data['items']['data'] as $item) {
                    $productIds[] =  $item['price']['product'];
                }
                $plan = PlanClass::$plans->whereIn("stripe_product_id", $productIds)->first();
                if ($plan) {
                    $handle = $plan->handle;
                }
            }
            Log::error($event->payload['type']);
            Log::error($data['status']);
            switch ($event->payload['type']) {
                case 'customer.subscription.deleted':
                    $handle = null;
                    break;
                case 'customer.subscription.created':
                    break;
                case 'customer.subscription.updated':
                    switch ($data['status']) {
                        case 'active':
                        case 'past_due':
                        case 'unpaid':
                            break;
                        case 'cancelling':
                            // $handle = null;
                            break;
                        case 'canceled':
                            // $handle = null;
                            break;
                    }
                    break;
            }

            $organization->update(['plan_handle' => $handle]);
        }
    }

    /**
     * Get the customer instance by Stripe ID.
     *
     * @param  string|null  $stripeId
     * @return \Laravel\Cashier\Billable|null
     */
    protected function getUserByStripeId($stripeId)
    {
        return Cashier::findBillable($stripeId);
    }
}
