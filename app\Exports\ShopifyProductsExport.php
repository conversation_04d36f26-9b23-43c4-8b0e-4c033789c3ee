<?php

namespace App\Exports;

use App\Models\Product;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithHeadings;

use Maatwebsite\Excel\Concerns\WithMapping;

class ShopifyProductsExport implements FromCollection , WithMapping ,WithHeadings, ShouldQueue
{

    use Exportable;
    private $products;
    protected $count=1;
    private $import_export;


    public function __construct($products,$export) {
        $this->products = $products;
        $this->import_export = $export;
    }


    public function collection(): Collection
    {
        return $this->products;
    }


    /*
     * method for setting Mapping
     *
     *
     * @return array
     */
    public function map($row): array
    {
        $product_mapping_record_array['handle']=$row->handle;
        $product_mapping_record_array['Title']=$row->title;
        $product_mapping_record_array['Body(HTML)']=$row->body_html;


        return $product_mapping_record_array;
    }


    /*
     * method for defining headings that we want
     *
     *
     * @return array
     */
    public function headings(): array
    {
        $heading = [
            'Handle',
            'Title',
            'Body(HTML)',
            'Vendor',
            'Standard product type',
            'Custom product type',
            "Google Product Category",
            "Tags",
            "Published",
            "Option1 Name",
            "Option1 Value",
            "Option2 Name",
            "Option2 Value",
            "Variant SKU",
            "Variant Grams",
            "Variant Inventory Tracker",
            "Variant Inventory Qty",
            "Variant Inventory Policy",
            "Variant Fulfillment Service",
            "Variant Price",
            "Variant Compare at Price",
            "Variant Taxable",
            "Variant Barcode",
            "Image Src",
            "Image Position",
            "Image Alt Text",
            "Gift Card",
            "SEO Title",
            "SEO Description",
            "Google Shopping metafields",
            "Variant Image",
            "Variant Weight Unit",
            "Variant Tax Code",
            "Cost per item",
            "Status"

        ];
        return $heading;
    }
}
