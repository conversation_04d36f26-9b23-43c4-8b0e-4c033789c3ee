<?php

namespace App\Models\Channel;


use App\Rules\CatalogLimit;
use mysql_xdevapi\Exception;
use Apimio\Gallery\Models\File;
use App\Models\Product\Product;
use App\Models\Product\Version;
use Illuminate\Validation\Rule;
use App\Models\Location\Location;
use App\Models\Organization\Plan;
use App\Scopes\UserSpecificScope;
use Illuminate\Support\Facades\DB;
use App\Models\Product\FileProduct;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Psr\Log\InvalidArgumentException;
use Illuminate\Database\Eloquent\Model;
use App\Models\Organization\Organization;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Validator;
use App\Classes\Shopify\ShopifyUsageCharge;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Channel extends Model
{
     /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'organization_id', 'type', 'product_update', 'inventory', 'export_status', 'category_empty', 'syncing_method', 'is_create_product_webhook_enabled', 'is_product_update_webhook_enabled', 'is_product_delete'];

    private
        $data, $filter,$user;

    public $timestamps = true;
    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        parent::booted();
        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where("organization_id", Auth::user()->organization_id);
            }
        });

        static::creating(function ($model) {

            if(!isset($model->organization_id)) {
                if(Auth::check()) {
                    $model->organization_id = Auth::user()->organization_id;
                }
            }
        });

        // static::created(function ($channel) {

        //     $versionName = $channel->versions->first()->name ?? '';
        //     if (!$versionName) {
        //         Log::error('Failed to create location, No version found for channel ID: ' . $channel->id);
        //     }

        //     $versions = request()->get('versions', []);
        //     $locations = request()->get('locations', []);


        //     // $channel->versions()->sync($versions);
        //     // $channel->locations()->attach($locations);
        // });
    }


    /**
     * Constructor for storing data and applying post logic
     * to organization structure.
     *
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if (isset($attributes["data"])) {
            $this->data = $attributes["data"];
        }
    }

    public function scopeAllOrganizations($query)
    {
        $organizations = Organization::pluck('id')->toArray();
        return $query->whereIn('organization_id', $organizations);
    }

    public function set_user($data)
    {
        $this->user = $data;
        return $this;
    }

    /**
     * Set data attribute.
     *
     *
     * @param array
     * @return $this
     */
    public function set_data(array $data)
    {
        $this->data = $data;
        return $this;
    }

    public function set_id(int $id)
    {
        $this->id = $id;
        return $this;
    }

    /**
     * Get data attribute.
     *
     * @return string
     */
    public function get_data()
    {
        return $this->data;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|max:255|unique:channels,name,NULL,id,organization_id,' . Auth::user()->organization_id,
            'type' => "required",
            'versions'=>"required"
//            ,
//            'locations'=>"required"
        ];
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    public function validation()
    {
        $validator = Validator::make($this->data, $this->rules(), [
            'versions.required' => 'The language field is required.',
//            'locations.required' => 'The Location field is required.',
        ]);
        return $validator;
    }

    public function fetch()
    {
        $channel = $this->with("shopify_channels");
        if ($this->filter) {
            if (isset($this->filter["name"]))
                $vendor = $channel->where("name", "LIKE", "%" . $this->filter["name"] . "%");
        }
        return $channel->orderBy("id", "DESC")->paginate(8);
    }

    public function filter($filter)
    {
        $this->filter = $filter;
        return $this;
    }

    public function store($error_callback, $success_callback)
    {
        try {
            $validation = $this->validation();
            if ($validation->fails()) {
                return $error_callback($validation->errors());
            }

            if (isset($this->id)) {
                $channel = $this->find($this->id);
            } else {
                $channel = $this;
            }

            if(isset($this->data['organization_id'])) {
                $channel->organization_id = $this->data['organization_id'];
            }

            $channel->name = $this->data['name'];
            $channel->type = $this->data['type'];

            if (isset($this->data['product_update']))
                $channel->product_update = $this->data['product_update'];
            if (isset($this->data['inventory']))
                $channel->inventory = $this->data['inventory'];
            if (isset($this->data['export_status']))
                $channel->export_status = $this->data['export_status'];
            if (isset($this->data['category_empty']))
                $channel->category_empty = $this->data['category_empty'];
            if (isset($this->data['syncing_method']))
                $channel->syncing_method = $this->data['syncing_method'];
            if (isset($this->data['is_create_product_webhook_enabled']))
                $channel->is_create_product_webhook_enabled = $this->data['is_create_product_webhook_enabled'];
            if (isset($this->data['is_product_update_webhook_enabled']))
                $channel->is_product_update_webhook_enabled = $this->data['is_product_update_webhook_enabled'];
            if (isset($this->data['is_product_delete']))
            $channel->is_product_delete = $this->data['is_product_delete'];
            if ($this->id) {
                $channel->save();
                if (isset($this->data['versions']))
                    $this->data["versions"] = array_filter($this->data['versions']);
                    if(!empty($this->data['versions'])) {
                        $channel->versions()->sync($this->data['versions']);
                    }
                    foreach ($this->data['apimio'] as $id => $location_id) {
                        $channelLocation = ChannelLocation::findOrFail($id);
                        if($channelLocation){
                         $channelLocation->location_id = $location_id;
                         $channelLocation->save();
                        }
                     }
                return $success_callback($channel);
            } else {
                // $usage_charge = new ShopifyUsageCharge();
                // if($usage_charge->catalog_charge($this->user)){
                // if(!Gate::denies('create-channel', \App\Models\Channel\Channel::query())){
                    $channel->save();
                    if (isset($this->data['versions']))
                        $channel->versions()->sync($this->data['versions']);
                    return $success_callback($channel);
                // } else {
                //     return $error_callback(['limit_reached' => "<b>Store limit reached.</b> <br> Please <a href='".route('billing')."'>upgrade</a> your plan to add more stores."]);
                // }
            }

        } catch (\Exception $e) {
            return $error_callback($e->getMessage());
        }

    }

    /**
     * @deprecated no use of this function, use delete eloquent method instead
     * */
    public function delete_by_id($id)
    {
        $this->set_id($id)->delete();
    }
    /**
     * @deprecated no use of this function
     * */
    public function fetch_data($callback)
    {
        $brands = $this::query();
        if ($callback)
            $brands = $callback($brands);
        return $brands;
    }


    public function scopeSyncAction($q, $channel_id, $product) {

        $product_date = $product->updated_at;
        $channel = $q->where("channels.id", $channel_id)->first();
        $channel_product = ChannelProduct::where('product_id',$product->id)
            ->where('channel_id',$channel_id)
            ->with(['status'=>function($query){
                $query->where('type','shopify');
            }])->first();

        //get channel versions
        $channel_versions = ChannelVersion::where('channel_id',$channel_id)->pluck('version_id')->toArray();
        $product_versions = $product->versions->pluck('id')->toArray();

        //check both arrays have any same value or not
        if(!empty(array_intersect($product_versions, $channel_versions))) {
            $channel_product_date = null;
            $channel_product_sync_id = null;
            if ($channel_product && sizeof($channel_product->status) > 0) {
                $channel_product_date = $channel_product->status[0]->updated_at;

                $channel_product_sync = $channel_product->status[0]->response ? $channel_product->status[0]->response : null;

                $channel_product_sync_id = $channel_product_sync ? $channel_product_sync->sync_id : null;
            }

            if( ($channel_product?->status) && $channel_product->status->first()?->status == -1){
                return "in_queue";
            }
            // else if ($product_date > $channel_product_date && $channel_product_sync_id) {
            else if (($channel_product?->status) && $channel_product->status->first()?->status == 0 && $channel_product_sync_id) {
                return 'updated';
            // } else if ($product_date <= $channel_product_date && $channel_product_sync_id) {
            } else if (($channel_product?->status) && $channel_product->status->first()?->status == 1 && $channel_product_sync_id) {
                return 'sync';
            } else if ($product->status) {
                return 'publish';
            } else {
                return 'draft';
            }
        }
        else
        {
            // get the list of product versions which are not same as channel versions
            $version_name = '';
            $arr_1 = array_diff($product_versions, $channel_versions);
            if(sizeof($arr_1) > 0){
                $version_name= $product->versions->where('id',$arr_1[0])->pluck('name')->first();
            }
            return ("version {$version_name} issue") ;
        }
    }


    public static function findOrCreateForProductWithVersion($name, $product, $versionId = null) {

        // Find the version with the given name for the specified product
        $channel = static::where('name', $name)->where('organization_id', $product->organization_id)
            ->first();

        // If the channel exists, return it
        if ($channel) {
            return $channel;
        }
        // Otherwise, create a new channel and associate it with the product
        $channel = new static();
        $channel->name = $name;
        $channel->save();

        if(!$versionId) {
            $versionId = Version::withoutGlobalScopes()
                ->where("organization_id", $product->organization_id)
                ->pluck("id")
                ->first();
        }

        $channel->versions()->sync($versionId);
        return $channel;
    }

    public function scopeCloneAction($q, $channel_id, $product) {
        $cloned_product_date = null;
        $product_date = null;


        //  $product_id = json_encode(['product_id'=>"$product->id"]);
        $channel_products = ChannelProductStatus::where('type','clone')
            ->whereJsonContains('response',['product_id'=>$product->id] )
            ->whereJsonContains('response',['channel_id'=>$channel_id] )
            ->with('channel_product')
            ->first();


        if($channel_products && $channel_products->channel_product) {
            // product from which new product is created
            $cloned_product_date = $product->updated_at;

            $product = Product::withoutGlobalScopes()->where('id', $channel_products->channel_product->product_id)
                ->first();

            $product_date = $product->updated_at??null;

        }

        if( $product_date && $cloned_product_date && $product_date < $cloned_product_date  ){

            return 'update available';
        }
        else if($product_date && $cloned_product_date && $product_date >= $cloned_product_date){
            return 'cloned';
        }

        else{
            return 'clone';
        }
    }

    public function scopeIsShopifyLinked($q,$channel_id){
        if( ShopifyChannel::where('channel_id',$channel_id)->first()){
            return true;
        }else{
            return false;
        }
    }

    //counting catalog of current organization
    /**
     * @deprecated no use of this function, use count eloquent method instead
     */
    public function get_cat() {
        return self::count();
    }

    /*
     *
     *  RELATIONSHIPS
     *
     * */
    public function channel_versions() {
        return $this->hasMany(ChannelVersion::class);
    }

    public function versions() {
        return $this->belongsToMany(Version::class);
    }

    public function shopify_channels() {
        return $this->hasMany(ShopifyChannel::class);
    }

    public function shopify_channel() {
        return $this->hasOne(ShopifyChannel::class);
    }

    public function organization() {
        return $this->belongsTo(Organization::class)->withoutGlobalScopes();
    }

    public function products() {
        return $this->belongsToMany(Product::class);
    }

    public function files()
    {
        return $this->belongsToMany(
            File::class,
            'channel_file_products', // Pivot Table
            'channel_id',
            'file_product_id'
        )->using(FileProduct::class);
    }
    public function locations()
    {
        return $this->belongsToMany(Location::class)->withTimestamps();
    }
    public function channel_locations() {
        return $this->hasMany(ChannelVersion::class);
    }
    public function channelLocation()
    {
        return $this->belongsToMany(ChannelLocation::class ,
            "channel_location" ,
            "channel_id" ,
            "location_id")
            ->withTimestamps()
            ->withoutGlobalScopes();
    }
    public function channelWiseLocation()
{
    return $this->hasMany(ChannelLocation::class, 'channel_id');
}

}
