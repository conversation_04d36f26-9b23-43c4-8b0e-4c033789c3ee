<?php

namespace App\Console\Commands;

use App\Models\Organization\File;
use App\Notifications\SlackNotification;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class SaveImage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:save';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Save the images to s3 bucket from url';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::channel('images')->info('IMAGE SAVE Schedule start running');
       // return "success";
        $files =  File::query()->where('should_sync',1)->limit('50')->get();
        if (env('APP_ENV') == 'production') {
            $env = 'PRODUCTION';
        }else{
            $env = 'STAGING';
        }

        if(count($files) > 0 ){

            $content = 'Scheduler start for image save to s3 bucket.';

            $fields =
                [
                    'total images going to save in s3 bucket'=>count($files)
                ];
            $title = 'Image Saving Start in '.$env;

            $this->sendNotification([
                'title'=>$title,
                'content'=>$content,
                'fields'=>$fields,
            ]);


            $count = 0;
            $failed_images = 0;

            foreach($files as $file){
                $url = strtok($file->link, '?');
                try{
                    //$contents = file_get_contents($url);
                    $response = Http::get($url);
                    if($response->successful()){

                        $contents = $response->body();
                        $name =substr($url, strrpos($url, '/') + 1);

                        $name = time().rand(10000000,99999999).'_'.$name;

                        //save image in s3 bucket
                        Storage::disk('s3')->put('images/'.$name,  $contents);

                        //get image url of s3 bucket
                        $url = Storage::disk('s3')->url('images/'.$name);
                        if(!$file->size) {
                            $size = Storage::disk('s3')->size('images/'.$name);
                            $size = round(($size / 1024),2);
                        }

                        if(!$file->ext) {
                            $extension = pathinfo($url, PATHINFO_EXTENSION);
                        }

                        if(!$file->width && !$file->height ) {
                            $dimensions = getimagesize($url);

                            $width = $dimensions[0];
                            $height = $dimensions[1];
                        }

                        $count++;
                    }else{
                        Log::channel('images')->info('IMAGE SAVE TO S3 BUCKET ERROR');
                        Log::channel('images')->error($response);
                        $failed_images++;
                    }



                }catch(Exception $e){
                    Log::channel('images')->info('IMAGE SAVE TO S3 BUCKET ERROR');
                    Log::channel('images')->error($e->getMessage());
                    $failed_images++;
                }


                $file->bucket_url = $url;
                if(isset($size))
                    $file->size = $size??null;
                if(isset($extension))
                    $file->ext = $extension??null;
                if(isset($width) && isset($height)){
                    $file->width    = $width??null;
                    $file->height   = $height??null;
                }
                $file->should_sync      = 0;
                $file->save();

            }

            $content = 'Scheduler End for image save to s3 bucket.';
            $fields =
                [   'environment'=>$env,
                    'total images updates'=>$count,
                    'failed images'=> $failed_images
                ];
            $title = 'Image Saving End';
            $this->sendNotification([
                'title'=>$title,
                'content'=>$content,
                'fields'=>$fields,
            ]);
        }
    }

    private function sendNotification($data){
       // if (env('APP_ENV') != 'production') {
            $channel = '#apimio-notifications-staging';
//        } else {
//            $channel = '#apimio-notifications';
//        }
        $detail = [
            "content" => $data['content'],
            "fields" => $data['fields'],
            "title" => $data['title'],
            "channel" => $channel
        ];
        \Illuminate\Support\Facades\Notification::route('slack', env('LOG_SLACK_WEBHOOK_URL'))
            ->notify(new SlackNotification($detail));
    }
}
