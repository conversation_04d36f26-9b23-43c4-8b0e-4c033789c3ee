<?php

namespace App\Jobs;

use App\Models\EventStatusHandler;
use App\Notifications\ApimioNotification;
use App\Traits\AWSSetting;
use App\User;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FinalNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, AWSSetting, Batchable;

    /**
     * Create a new job instance.
     * @param array $data
     * @return void
     *  $data = [
     *     'organization_id' => $organization_id, // optional
     *     'user_id' => $user_id,
     *     'details' => $details,
     *     'email_notification' => $email_notification, // optional, true or false, default is false,
     *
     */
    public function __construct(public array $data)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        info('FinalNotificationJob: ' . json_encode($this->data));
        $notifier = new ApimioNotification($this->data['details']);
        $notifier->only_db_notify(true);
        $user = User::query()->find($this->data['user_id']);
        $user->notify($notifier);

        //delete the listener handler table record for this event
        (new EventStatusHandler())
            ->set_data([
            'organization_id' => $this->data['organization_id']
            ])->delete_event();
    }
}
