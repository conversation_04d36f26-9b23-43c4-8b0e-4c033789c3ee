<?php


namespace App\Classes\Export;

use Apimio\MappingConnectorPackage\classes\formulas\ApplyTemplateOnItem;
use Apimio\MappingConnectorPackage\Http\Controllers\MappingFieldController;
use Apimio\MappingConnectorPackage\models\Template;
use App\Classes\ProductFilter;
use App\Classes\Template\GenerateNode;
use App\Exports\ProductsExport;
use App\Imports\ProductsImport;
use App\Jobs\Export\GetProductsChunkArray;
use App\Jobs\FinalNotificationJob;
use App\Models\Channel\Channel;
use App\Models\Product\Attribute;
use App\Models\Product\AttributeFamily;
use App\Models\Product\AttributeFamilyProductVersion;
use App\Models\Product\Product;
use App\Models\Product\ProductVersion;
use App\Models\Product\Version;
use App\Notifications\ApimioNotification;
use App\Traits\AWSSetting;
use App\Traits\ImportExport\Formulas;
use Illuminate\Bus\Batch;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use phpDocumentor\Reflection\Types\Integer;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use function Aws\filter;


class ExportToCsv
{
    use Formulas,AWSSetting;

    public $data, $product;

    /**
     * method for validation of CSV export
     *
     * @param array $data which is used to fetch mapping rows of export csv
     *
     * @deprecated this method is removed in future
     *
     * @return  object $validation_for_CSV_export with return an object of validation
     */

    public function validation_for_CSV_export($data)
    {
        return Validator::make($data, [
            "nodes" => 'required',
            "version" => 'required',
            "catalog" => 'required'
        ]);
    }


    /**
     * method for Fetching product brands name
     *
     * @deprecated this method is removed in future
     *
     */
    public function fetch_brand($product)
    {
        $brands = array();
        if ($product->has('brands')) {
            foreach ($product->brands as $brand) {
                if (isset($brand->name)) {
                    $brands[] = $brand->name;
                }
            }
        }
        return empty($brands) ? null : implode(", ", $brands);
    }


    /**
     * method for Fetching product categories name
     *
     *
     * @deprecated this method is removed in future
     *
     */
    public function fetch_category($product)
    {
        $categories = array();
        if ($product->has('categories')) {
            foreach ($product->categories as $category) {
                if (isset($category->name)) {
                    $categories[] = $category->name;
                }
            }
        }
        return empty($categories) ? null : implode(", ", $categories);
    }


    /**
     * method for Fetching product vendors name
     *
     *
     * @deprecated this method is removed in future
     *
     */
    public function fetch_vendor($product)
    {
        $vendors = array();
        if ($product->has('inviteVendor')) {
            foreach ($product->inviteVendor as $vendor) {
                if (isset($vendor->fname)) {
                    $vendors[] = $vendor->fname;
                }
            }
        }
        return empty($vendors) ? null : implode(", ", $vendors);
    }


    /**
     * method for Fetching product versions name
     *
     * @deprecated this method is removed in future
     *
     */
    public function fetch_version($product)
    {
        $versions = array();
        if ($product->has('versions')) {
            foreach ($product->versions as $version) {
                if (isset($version->name)) {
                    $versions[] = $version->name;
                }
            }
        }
        return empty($versions) ? null : implode(", ", $versions);
    }


    /**
     * method for Fetching product files link
     *
     * @deprecated this method is removed in future
     *
     */
    public function fetch_file($product)
    {
        $files = array();
        if ($product->has('files')) {
            foreach ($product->files as $file) {
                if (isset($file->link)) {
                    $files[] = $file->link;
                }
            }
        }
        return empty($files) ? null : implode(", ", $files);
    }


    /**
     * method for Fetching product files link
     * @param string $temp_create which define the type of template which we want to create.
     * @param boolean $with_variant if we want to add variants default value then pass TRUE otherwise default value is FALSE
     *
     * @deprecated this method is removed in future
     *
     * @return  array fetch all the default values for specific default template
     */
    public function fetch_template_default_values(string $temp_create, $with_variant = FALSE)
    {
        $csv_row = array();
        // if equals to shopify
        if ($temp_create === 'shopify') {
            if ($with_variant) {
                $csv_row['Variant Inventory Tracker'] = 'shopify';
                $csv_row['Variant Inventory Policy'] = 'continue';
                $csv_row['Variant Fulfillment Service'] = 'manual';
                $csv_row['Variant Requires Shipping'] = 'TRUE';
                $csv_row['Variant Taxable'] = 'TRUE';
            } else {
                $csv_row['Published'] = 'TRUE';
                $csv_row['Gift Card'] = 'FALSE';
                $csv_row['Status'] = 'active';
            }
        } // if equals to magento
        elseif ($temp_create === 'magento') {
            $csv_row['attribute_set_code'] = 'Default';
            $csv_row['product_websites'] = 'base';
            $csv_row['tax_class_name'] = 'Taxable Goods';

            if ($with_variant) {
                $csv_row['product_type'] = 'simple';
                $csv_row['visibility'] = 'Not Visible Individually';
            } else {
                $csv_row['product_type'] = 'configurable';
                $csv_row['visibility'] = 'Catalog, Search';
            }
        }
        return $csv_row;
    }


    /**
     * method for export apimio products into csv's
     *
     * @param array $data which is used to fetch mapping rows of export csv
     * @param callable $error_callback if we found any error
     * @param callable $success_callback return success callback with final export csv array
     *
     *
     * @return  array $success_callback with return an final export csv array
     */
    public function export_to_csv(array $data, callable $error_callback, callable $success_callback)
    {
        try {
            //fetching products
            $this->data = $data;
            $product_obj = new Product();
            $chunks_jobs = [];
            $total_variants_limit = 30;

            $jobable = false;

            $products = Product::query();
            if (isset($data['request_data']['organization_id'])) {
                $products = $products->where('organization_id', $data['request_data']['organization_id']);
            }

            $products
                ->withCount('variants')
                ->chunk(15,
                    function ($products) use ($data, $product_obj, &$chunks_jobs, &$jobable, $total_variants_limit) {
                        $total_count = 0;
                        $total_chunks_product_ids = [];
                        $temp_product_ids = [];
                        foreach ($products as $product) {
                            $variant_count = $product->variants_count;

                            $total_count += $variant_count ?? 0;
                            if ($total_count > $total_variants_limit) {
                                $temp_product_ids[] = $product->id;
                                $total_chunks_product_ids[] = $temp_product_ids;
                                $temp_product_ids = [];
                                $total_count = 0;
                            } else {
                                $temp_product_ids[] = $product->id;
                            }
                        }
                        if (!empty($temp_product_ids)) {
                            $total_chunks_product_ids[] = $temp_product_ids;
                        }
                        foreach ($total_chunks_product_ids as $key => $product_ids) {
                            $chunk_products = $products
                                ->whereIn('id', $product_ids);

                            if ($chunk_products->isNotEmpty()) {

                                //////////////////////////////////////below code is for without queue testing do not remove this code//////////////////////////////////
//                                $this->get_products_chunk_array($chunk_products, $product_obj, $data);
//                                $this->send_notification_for_export($data);


                                ///////////// product chunks (JOB BATCHING) //////////////
                                $encode_data_s3_url = $this->saveToS3(
                                    [
                                        'products' => $chunk_products,
                                        'product_obj' => $product_obj,
                                        'data' => $data,
                                    ]
                                    , "import_csv_products_chunk".$key."_".time());


                                $jobable = true;
                                $chunks_jobs[] = new GetProductsChunkArray($encode_data_s3_url);
                                ///////////// (JOB BATCHING END) //////////////
                            }
                        }
                    });

            $notification_data = $this->get_notification_for_export($data);

            //if job able is true then start batching
            if ($jobable) {
                if (env('CUSTOM_QUEUE_WORKER') == 'local') {
                    $batch = Bus::batch($chunks_jobs)->then(function (Batch $batch) use ($data) {
                        $this->send_notification_for_export($data);
                    })->dispatch();
                } else {
                    $chunks_jobs[] = new FinalNotificationJob($notification_data);
                    $organization_id = $this->data['organization_id'] ?? "";
                    $this->dispatchJobsToFifoQueue($chunks_jobs, $organization_id, $organization_id);
                }
            }

        } catch (\Exception $e) {
            Log::error($e);
        }

    }


    /**
     * @param array $data
     * @return void
     */
    function send_notification_for_export(array $data = []): void
    {
        $filename = $this->data['filename'] ?? null;
        $url = route('download', ['filename' => $filename]);

        $details = [
            'subject' => 'Your export CSV file is ready to download',
            'greeting' => 'Hi ' . ($data['user']->fname ?? null),
            'body' =>
                "Your ( "
                . $data['data_required']["export_type"]
                . " ) Export CSV"
                . " with version <b>"
                . $data["version_name"]
                . "</b> is generated successfully"
                . "<br><br>Please copy and paste the below URL into your web browser: <br><a href='" . $url . "'>" . $url . "</a><br>",
            'thanks' => 'Thank you for using ' . request()->getHttpHost(),
            'actionText' => 'Download CSV File',
            'actionURL' => $url,
            'user_id' => $data['user']->id ?? null,
            'organization_id' => $data['organization_id'] ?? null,
        ];

        $notifier = new ApimioNotification($details);
        $data['user']->notify($notifier);
    }


    function get_notification_for_export(array $data = []): array
    {
        $filename = $this->data['filename'] ?? null;
        $url = route('download', ['filename' => $filename]);

        $details = [
            'subject' => 'Your export CSV file is ready to download',
            'greeting' => 'Hi ' . ($data['user']->fname ?? null),
            'body' =>
                "Your ( "
                . $data['data_required']["export_type"]
                . " ) Export CSV"
                . " with version <b>"
                . $data["version_name"]
                . "</b> is generated successfully"
                . "<br><br>Please copy and paste the below URL into your web browser: <br><a href='" . $url . "'>" . $url . "</a><br>",
            'thanks' => 'Thank you for using ' . request()->getHttpHost(),
            'actionText' => 'Download CSV File',
            'actionURL' => $url,
            'user_id' => $data['user']->id ?? null,
            'organization_id' => $data['organization_id'] ?? null,
        ];

        $data['details'] = $details;
        $data['user_id'] = $data['user']->id ?? null;
        return $data;
    }


    public function products_filters($products = null, $data = [])
    {
        if ($products && !$products->isEmpty()) {
            $products->load([
                "versions"
            ]);

            if (isset($data['request_data']['version']) && $products) {
                $products = $products->filter(function ($product) use ($data) {
                    return $product->versions()->where('versions.id', $data['request_data']['version'])->exists();
                });
            }


            if (isset($data['data_required']['filter_query'])) {
                $filter_query = $data['data_required']['filter_query'] ?? [];

                if (isset($filter_query['filter_export_productIds']) && $filter_query['filter_export_productIds'] != "all" && $products) {
                    $filter_query['filter_export_productIds'] = explode(',', $filter_query['filter_export_productIds']);
                    $products = $products->whereIn('id', $filter_query['filter_export_productIds']);
                }

                if (isset($filter_query['filter_total_product_array']) && $filter_query['filter_total_product_array'] != null){
                    $filters = new ProductFilter();
                    // Check if products is a collection
                    if ($products instanceof Collection) {
                        $productIds = $products->pluck('id')->toArray();
                        $productsQuery = Product::whereIn('id', $productIds);
                        $filters->apply_filters($productsQuery, json_decode($filter_query['filter_total_product_array'], true));
                        $products = $productsQuery->get();
                    } else {
                        // If it's already a query builder, just apply the filters
                        $filters->apply_filters($products, json_decode($filter_query['filter_total_product_array'], true));
                        $products = $products->get();
                    }
                }
            }

        }
        $products->load([
            "brands",
            "categories",
            "inviteVendor",
            "versions",
            "channels",
            "variants",
            "files",
        ]);

        return $products;
    }


    function get_products_chunk_array($products, $product_obj, $data = [])
    {
        $final_products = array();
        $products = $this->products_filters($products, $data);

        if ($products->isEmpty()) {
            return;
        }

        foreach ($products as $product) {
            try {
                $this->product = $product_obj->product_fetch($product);


//                $this->product->load(['inventories' => function($query) use ($data) {
//                    if (isset($data['request_data']['location'])) {
//                        $query->where('location_id', $data['request_data']['location']);
//                    }
//                    if (isset($data['request_data']['organization_id'])) {
//                        $query->where('organization_id', $data['request_data']['organization_id']);
//                    }
//                }]);


                $this->product->load(['variants' => function ($query) use ($data) {
                    $query->with(["inventories" => function ($q) use ($data) {
                        $q->where('organization_id', $data['request_data']['organization_id']);
                    }]);
                }]);


                // this method is for apimio products data
                $mapping = new MappingFieldController();
                $data['input_array'] = $mapping->fetch_apimio_products($this->product, true, $data);
                $data['output_array'] = $data['output_array'] ?? [];
                $data_required = $data['data_required'];
                $data_required['product_status'] = $product->status ?? 0;
                $data_required['sync'] = true;
                $data_required['template_method_type'] = $data['request_data']['template_method_type'];
                $data_required['request_data'] = $data['request_data'];
                $product_item = $mapping->mapping_view($data['input_array'], $data['output_array'], $data_required);
            } catch (\Exception $e) {
                throw new \Exception($e->getMessage());
            }

            $final_products['nodes'] = collect($final_products['nodes'] ?? []);
            $final_products['final_csv_heading'] = collect($final_products['final_csv_heading'] ?? []);

            $final_products['nodes'] = $final_products['nodes']->merge($product_item['nodes']);
            $final_products['final_csv_heading'] = $final_products['final_csv_heading']
                ->merge($product_item['final_csv_heading'])
                ->unique();
        }

//        $final_products['final_csv_heading'] = $final_products['final_csv_heading']->unique()->values();
        $this->save_products_in_csv($final_products, $data);
    }


    function save_products_in_csv(&$final_product, $data = [])
    {
        if (isset($final_product['nodes']) && isset($final_product['final_csv_heading'])) {
            $disk = Storage::disk('s3');
            $heading_array = $final_product['final_csv_heading']->toArray();
            $filename = $data['filename'] ?? null;
            if ($disk->exists($filename)) {
                $newFile = (new ProductsExport($final_product['nodes'], $heading_array))->download("my_temp_file.xlsx");
                $new_array = (new ProductsImport())->toCollection($newFile->getFile());
                if (isset($new_array[0])) {
                    $existingFile = (new ProductsImport())->toCollection($filename , 's3');
                    $final_heading = [];
                    if ($existingFile[0][0]){
                        $final_heading = $existingFile[0][0]->keys();
                        $final_heading = $final_heading
                            ->merge($heading_array)
                            ->unique();
                        $final_heading = $final_heading->toArray();
                    }else{
                        $final_heading = $heading_array;
                    }
                    $existingFile = $existingFile[0]->merge($new_array[0]);
                    Excel::store(new ProductsExport($existingFile, $final_heading), $filename, 's3');
                }
            } else {
                Excel::store(new ProductsExport($final_product['nodes'], $heading_array), $filename, 's3');
            }
        }
    }


}
