<?php

namespace App\Http\Controllers\Api\Marketing;

use App\Http\Controllers\Controller;
use GuzzleHttp\Client;
use App\Services\Marketing\Hubspot\Hubspot;
use Illuminate\Http\Request;
use App\Http\Requests\Hubspot\HubspotRequest;
use App\User;

class HubspotController extends Controller
{
    private $client;

    public function __construct()
    {
        $this->middleware("auth:sanctum");
        $apiKey = env("HUBSPOT_ACCESS_TOKEN");
        $this->client = new Client([
            'base_uri' => 'https://api.hubapi.com/',
            'headers' => [
                'Authorization' => "Bearer " . $apiKey,
                'Content-Type' => 'application/json',
            ],
        ]);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        //add params
        // $response = $this->client->get("crm/v3/objects/contacts", [
        //     'query' => [
        //         'limit' => 10,
        //     ],
        // ]);

        // $contact = json_decode($response->getBody(), true);
        // return $contact;
        return response()->json([
            'message' => 'list',
        ],405);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(HubspotRequest $request)
    {
        $user = User::findOrFail($request->get('user_id'));
        $source = $this->getSignupSource($user);

        $contactId = $this->createContact($user, $source);
        $dealId = $this->createDeal($user);
        $this->associateContactAndDeal($contactId, $dealId);

        return response()->json([
            'message' => 'Contact and deal created successfully in Hubspot.',
        ]);
    }


    /**
     * Display the specified resource.
     */
    public function show(Request $request, $id)
    {
        try {
            $user = $request->user();

            $user = get_class($user) == User::class
                ? $user
                : $user->users()->first();
                
            $contact  = $this->getHubspotUser($user);
          

            // return $contact['id'];
            return response()->json([
                'message' => 'Hubspot record retrieved successfully',
                'data' => isset($contact),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Hubspot record retrieved successfully',
                'data' => isset($contact)
            ],404);
        }
    }
    /**
     * Update the specified resource in storage.
     */
    public function update(HubspotRequest $request, $id)
    {
       try
       { 
        $user = auth()->user();

        $user = get_class($user) == User::class
            ? $user
            : $user->users()->first();


        $this->updateContact($user, $request->validated());
        $contact  = $this->getHubspotUser($user);

        return response()->json([
            'message' => 'Hubspot record updated successfully',
            'data' => $contact,
        ]);}
        catch (\Exception $e) {
            return response()->json([
                'message' => 'Hubspot record update failed'.$e->getMessage(),
                'data' => $request->validated()
            ],404);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        // $hubspot = $request->user()->hubspot()->findOrFail($id);
        // $hubspot->delete();

        //     return response()->json([
        //         'message' => 'Hubspot record deleted successfully',
        //     ]);
        // }
        return response()->json([
            'message' => 'delete not allowed',
            'data' => $id
        ],405);
    }
    

    
       /**
     * get a Hubspot contact.
     */
    private function getHubspotUser($user){
        $response = $this->client->get("crm/v3/objects/contacts/{$user->email}?idProperty=email");

        return json_decode($response->getBody(), true);
    }


    /**
     * Create a Hubspot contact and return its ID.
     */
    private function createContact(User $user, string $source): string
    {
        $response = $this->client->post("crm/v3/objects/contacts", [
            'json' => [
                'properties' => [
                    'firstname' => $user->fname,
                    'lastname' => $user->lname,
                    'email' => $user->email,
                    'phone' => $user->phone,
                    'email_verified' => true,
                    'hubspot_owner_id' => "430008351", // Replace with dynamic owner ID if needed
                    'signup_source' => $source,
                    'hs_marketable_status' => true,
                ],
            ],
        ]);

        $contact = json_decode($response->getBody(), true);
        return $contact['id'];
    }

    /**
     * Create a Hubspot deal and return its ID.
     */
    private function createDeal(User $user): string
    {
        $response = $this->client->post("crm/v3/objects/deals", [
            'json' => [
                'properties' => [
                    'amount' => "50",
                    'hubspot_owner_id' => "430008351", // Owner id for Sarah
                    'dealname' => $user->fname,
                    'dealstage' => "appointmentscheduled",
                    'pipeline' => "default",
                ],
            ],
        ]);

        $deal = json_decode($response->getBody(), true);
        return $deal['id'];
    }

    /**
     * Associate a contact with a deal.
     */
    private function associateContactAndDeal(string $contactId, string $dealId): void
    {
        $this->client->put("crm-associations/v1/associations", [
            'json' => [
                'fromObjectId' => $contactId,
                'toObjectId' => $dealId,
                'category' => 'HUBSPOT_DEFINED',
                'definitionId' => 4,
            ],
        ]);
    }

    /**
     * Update a Hubspot contact property.
     */
    private function updateContact(User $user, array $properties): void
    {
        $this->client->patch("crm/v3/objects/contacts/{$user->email}?idProperty=email", [
            'json' => [
                'properties' => $properties,
            ],
        ]);
    }

    /**
     * Get the signup source of the user.
     */
    private function getSignupSource(User $user): string
    {
        if (!empty($user->google_id)) {
            return 'google';
        } elseif (!empty($user->shopify_shop_id)) {
            return 'shopify';
        }
        return 'manual';
    }
}
