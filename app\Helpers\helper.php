<?php

use App\Models\Notification\ErrorLog;
use Illuminate\Support\Facades\Log;

function formatEmailPhone($data, $start_value = null, $end_value = null) {
    $start = substr($data,0, $start_value);

    if ($end_value && $end_value != 0)
        $end = substr($data,  -$end_value);
    else
        $end='';

    $values_sum = $start_value + $end_value;
    if (strlen($data) > $values_sum) {
        $size = strlen($data) - $values_sum;
        for ($i=0; $i < $size; $i++) {
            $start .= '*';
        }
        isset($end_value) ? $start .=$end : 0;
        return $start;
    } else {
        return $data;
    }

}

