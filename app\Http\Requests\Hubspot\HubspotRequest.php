<?php

namespace App\Http\Requests\Hubspot;

use Illuminate\Foundation\Http\FormRequest;

class Hu<PERSON>potRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'goals' => ['nullable', 'string'],
            'product_category' => ['required', 'string', 'max:255'],
            'challenges' => ['nullable', 'string'],
            'current_role' => ['nullable', 'string', 'max:255'],
            'purpose_for_pim' => ['nullable', 'string', 'max:500'],
            'company_description' => ['nullable', 'string', 'max:500'],
            'number_of_products' => ['nullable', 'string'],
        ];
    }
}
