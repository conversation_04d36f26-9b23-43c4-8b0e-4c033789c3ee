<?php

namespace App\Http\Controllers\v2\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use App\User;
use Illuminate\Foundation\Auth\RegistersUsers;
use App\Http\Requests\Auth\RegisterRequest;
use Illuminate\Support\Facades\Validator;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Lunaweb\RecaptchaV3\Facades\RecaptchaV3;
use Inertia\Inertia;


class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except(["createAccount"]);
    }




    /**
     * Create a new user instance after a valid registration.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function createAccount(RegisterRequest $request)
    {
        try {
            if (App::environment('testing')) {
                $score = RecaptchaV3::verify($request->get('g-recaptcha-response'), 'register');
                if ($score < 0.5) {
                    return back()->withErrors('main', 'You are most likely a bot');
                }
            }
        } catch (\Exception $e) {

            Log::error($e);
        }

        DB::beginTransaction();
        try {
            $data =  $request->validated();

            //check if user is already register with google or not
            $existingUser = User::where('email', $data['email'])->whereNotNull('google_id')->whereNull('password')->first();
            if ($existingUser) {
                return Inertia::render('auth/ManualLinking', [
                    'existingUser' => $existingUser->only(['id', 'email', 'fname', 'lname', 'google_id']),
                    'formData' => $data,
                    'csrf_token' => csrf_token()
                ]);
            }

            $user = new User();
            $user->fname = $data['fname'];
            $user->lname = $data['lname'];
            $user->email = $data['email'];
            $user->phone = $data['phone'];
            $user->password = Hash::make($data['password']);
            $user->save();

            DB::commit();


            auth()->login($user, true);
            $user->sendEmailVerificationNotification();
            return redirect()->route('v2.verify')->with(['email' => $user->email]);
        } catch (\Exception $e) {
            Log::error($e);
            DB::rollBack();
            return back()->withErrors('message', 'Something went wrong. Please try again later.');
        }
    }
}
