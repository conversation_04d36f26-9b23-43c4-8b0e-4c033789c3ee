<?php

namespace App\Jobs\Shopify;

use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use App\Models\Product\Product;
use App\Classes\Mapping\Conversion;
use App\Models\Product\FileProduct;
use Illuminate\Support\Facades\Log;
use App\Classes\Shopify\ArrayCreate;
use App\Models\Notification\ErrorLog;
use App\Models\Channel\ChannelProduct;
use App\Models\Channel\ChannelVersion;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\Channel\ChannelProductStatus;
use App\Classes\Shopify\ConvertToShopifyArray;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Apimio\MappingConnectorPackage\models\Template;
use Apimio\MappingConnectorPackage\Http\Controllers\MappingFieldController;


class SyncSingleProduct implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels ,Batchable ;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public array $data)
    {
        //
    }

    /**
     * Execute the job.
     *
     */
    public function handle()
    {
        $channel_id = $this->data['channel_id'];
        $template_ids = $this->data['template_ids'];
        $organization_id = $this->data['organization_id'];
        $product_id = $this->data['product_id'];

        $excludeFiles = FileProduct::whereHas('channelFileProducts', function ($query) use ($channel_id) {
                            $query->where('channel_id', $channel_id);
                        })
            ->where('product_id',$product_id)
            ->pluck('file_id');

        $channel_version = ChannelVersion::query()
            ->where('channel_id' ,$channel_id)
            ->first();

        $products_obj = new Product();

            $products = $products_obj->with(
                [
                    "brands",
                    "categories.channel"=>function($q) use ($channel_id){
                        $q->where('channel_id',$channel_id)
                            ->where('store_connect_type','shopify');
                    },
                    "vendors",
                    "channels"=>function($q) use ($channel_id){
                        $q->where('channel_id',$channel_id);
                    },
                    "variants" => function($q) use ($channel_version){
                        $q->with('settings')->where('version_id',$channel_version->version_id);
                    },
                    "ChannelVariants"=> function($query) use($channel_id){
                        $query->with('variant.file')->where(["channel_id" => $channel_id , "store_connect_type" => "shopify"])
                        ->where("store_connect_image_id" ,'!=', null);
                    },
                    'versions'=>function($q) use ($channel_version){
                        $q->where('versions.id',$channel_version->version_id);
                    },
                    "files.channelFile" => function($query) use($channel_id){
                        $query->where(["channel_id" => $channel_id , "store_connect_type" => "shopify"]);
                    }
                ])
                ->where('id',$product_id)
                ->get();


        if (is_iterable($products)) {
            $i = 0;
            foreach ($products as $product) {
                $products[$i] = $products_obj->product_fetch($product);
                $i++;
            }
        }

        Log::channel('shopify')->info('main-product');

//        Log::channel('shopify')->info($products);
        $mapping = new MappingFieldController();
        // this method is for apimio products data
         $data_required = [
            'organization_id' => $organization_id,
            'output_type' => 'shopify',
            'template_method_type' => 'shopify', //its only contain ('import','export','shopify','clone','other')
            'sync' => true,  //true or false
            'template_id' => empty($template_ids) ? null: $template_ids[0], // template array
            'redirect_url_route' => 'shopify.redirection', //it should be post method
            'versions' => Conversion::fetch_all_version_in_array(),
            'catalogs' => Conversion::fetch_all_catalog_in_array(),
            'locations' => Conversion::fetch_all_location_in_array(),
            'default_template_mapping' => (new ArrayCreate($channel_id,$organization_id, $channel_version->id ?? null))->default_shopify_template_mapping(),
             'products' => $products,
         ];

        // left array for apimio
        $data['left_array'] = Template::apimio_mapping_array([], false);

        // right array for shopify
//        $data['right_array'] = $mapping->fetch_right_array('shopify');
        $data['right_array'] = (new ArrayCreate($channel_id,$organization_id))->getDefinitions()->mainArray();

         $shopify_array =  $mapping->mapping_view($data['left_array'],$data['right_array'],$data_required);

        if($shopify_array){

            (( new ConvertToShopifyArray([
                'product'=>$products,
                'channel_id'=>$channel_id,
                'organization_id'=>$organization_id,
                'shopify_array'=>$shopify_array['nodes']
            ])
            )->convert()->create(function($error) use ($products ,$organization_id,$channel_id ){
                // if any error occurs the below code will save it in db table.
                Log::channel('shopify')->error($error);
                $error_array = [
                    'organization_id' => $organization_id,
                    'description' => $error['main'],
                    'type' => 'Shopify Syncing',
                    'link' => route('products.edit', $products[0]->id),
                    'link_text' => 'View Product',
                    'status' => 'error',
                ];

                $error = new ErrorLog();
                $error->setData($error_array)->store(function ($error) {
                    Log::channel('shopify')->info('Error in saving ErrorLogs.');
                    Log::channel('shopify')->error($error);
                }, function () {
                });

                // if error occur the status will change to 0
                (new ChannelProduct())->changeProductSyncStatus(['channel_id'=>$channel_id,'product_ids'=>[$products[0]->id],'status'=>0]);

            },function() use ($products ,$channel_id ){
                (new ChannelProduct())->changeProductSyncStatus(['channel_id'=>$channel_id,'product_ids'=>[$products[0]->id],'status'=>1]);
                Log::channel('shopify')->info('Product sync successfully.');
            } ));
        }else{
            Log::channel('shopify')->error('Template array not get.');
        }
    }

}
