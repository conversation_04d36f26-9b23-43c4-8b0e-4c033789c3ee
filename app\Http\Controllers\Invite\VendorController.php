<?php

namespace App\Http\Controllers\Invite;

use App\Http\Controllers\Controller;
use App\Models\Channel\Channel;
use App\Models\Invite\ChannelInvite;
use App\Models\Invite\Invite;
use App\Models\Organization\Organization;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\Product\Vendor;
use App\Models\Product\Category;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Gate;
use Exception;


class VendorController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        $this->middleware(["activeOrganization"])->only("checkpoint");
    }

    public function index(Request $request)
    {
        $data["vendors"] = Invite::query()->vendor()->orderBy("type")
            ->with(['organization_sender_without_scope.users_without_scope',"organization_sender.users"]);
        if ($request->filled('q') ) {
                $data["vendors"] = $data["vendors"]->where(function($q) use($request){
                    $q->where("email", "LIKE", "%{$request->q}%")
                        ->orWhereHas("organization_sender_without_scope.users_without_scope" ,
                            function ($query) use ($request){
                                $query->where("email","LIKE", "%{$request->q}%");}
                        );
                });
        }
         $data["vendors"] = $data["vendors"]->get();

        return view("invite.vendor.index")->with("data", $data);
    }

    public function create()
    {
        $channels = Channel::orderBy("id", "DESC")->get();
        return view("invite.vendor.create", ["channels" => $channels]);

    }

    public function store(Request $request)
    {
        $response = Gate::inspect('create', Vendor::class);
        if ($response->allowed()) {
            $invite = new Invite();

            return $invite->set_data($request->all())->set_user(Auth::user())->store(
            // error function
                function ($errors) {
                    return back()->withInput()->withErrors($errors);
                },
                // success function
                function ($obj) {
                    return Redirect::route("vendor.show", $obj->id)->withSuccess("Vendor added successfully.");
                }
            );
        } else {
            return \redirect(route('dashboard'))->withErrors(['main' => $response->message()]);
        }
    }

    //Find Vendor using id and open it in create form with Vendor data
    public function edit($id)
    {
        try {
            $data["vendor"] = Invite::with('channels_without_scope')->findOrFail($id);
            $channels = Channel::orderBy("id", "DESC")->get();
            $data["products"] = $data["vendor"]->get_products();
            return view('invite.vendor.create', ["channels" => $channels])->with("data", $data);
        } catch (Exception $e) {
            log:
            error($e);
            return Redirect::route("vendor.index")->withErrors(['main' => "Something went wrong."]);
        }
    }


    //Sends Data to Vendor invites and save new data
    public function update(Request $request)
    {
        if (Invite::find($request->id)) {
            return (new Invite())->set_data($request->all())->set_user(auth::user())
                ->store(
                    function ($errors) {
                        return back()->withErrors($errors)->withInput();
                    },
                    function () {
                        return redirect()->route('vendor.index')->withSuccess('Vendor updated successfully.');
                    });
        } else {
            return Redirect::route("vendor.index", $request->id)->withErrors(['main' => "Vendor ID does not Exist."]);
        }
    }

    public function show($id)
    {

        try {
            $data["vendor"] = Invite::vendor()->with('channels_without_scope')->findOrFail($id);
            $data["products"] = $data["vendor"]->get_products();
            $organization = Invite::query()
                ->withoutGlobalScopes()
                ->where("id", $id)
                ->with('organization_sender_without_scope.users_without_scope')
                ->get()->first();
            $vendors = new \stdClass();
            if ($organization->get_status() == 4) {
                $vendors = $data["vendor"];
            } elseif (($organization->get_status() == 1 || $organization->get_status() == 2 || $organization->get_status() == 5) && $organization->email != auth()->user()->email)  {
                if($organization->email == auth()->user()->email){
                    $vendors->fname = $organization->organization_sender_without_scope->users_without_scope->first()->fname ?? '';
                    $vendors->lname = $organization->organization_sender_without_scope->users_without_scope->first()->lname ?? '';
                    $vendors->phone = $organization->organization_sender_without_scope->users_without_scope->first()->phone ?? '';
                    $vendors->email = $organization->organization_sender_without_scope->users_without_scope->first()->email ?? '';
                }
                else{
                $vendors->fname = $organization->user_sender_without_scope()->pluck("fname")->first() ?? '';
                $vendors->lname = $organization->user_sender_without_scope()->pluck("lname")->first() ?? '';
                $vendors->phone = $organization->user_sender_without_scope()->pluck("phone")->first() ?? '';
                $vendors->email = $organization->user_sender_without_scope()->pluck("email")->first()?? '';
                }
            } else {
                $vendors = $organization->organization_sender_without_scope()->first()->users_without_scope()->first();
            }
            return view('invite.vendor.show', ["data" => $data, "vendor" => $organization, "vendors" => $vendors]);
        } catch (Exception $e) {
            return back()->withErrors(['main' => 'Something went wrong, Please try again']);
        }
    }

    public function clone($invite_id, $product_id)
    {
        $invite = Invite::withoutGlobalScopes()->with('channels_without_scope')->findOrFail($invite_id);

        //get the channel ids of invitation
        $invite_channel_ids = $invite->channels_without_scope()->pluck('channel_id');

        //get product data
        $product_obj = $invite->get_product_by_id($product_id, $invite_channel_ids,auth::user());

        //get channel id of product to save channel product status
        $channel_ids = $product_obj->channels()->pluck('channel_id');

        // get cloned product id
        $cloned_product_id = $invite->get_product_id([
            'product_channel_id' => $channel_ids[0],
            'product_id' => $product_id,
        ]);


                $products = new Product();

                $products->convert_apimio_product_to_array(
                    function ($error) {
                        return back()->withErrors(['main' => $error]);
                    },
                    function ($product_data) use ($products, $product_id, $channel_ids, $cloned_product_id) {

                        //add id of the clone product in array of product

                        if ($cloned_product_id != 0)
                            $product_data = $product_data + ['id' => $cloned_product_id];

                        if (!isset($product_data['template_method_type']))
                            $product_data['method_type'] = "clone";

                        return $products->convert_to_product(
                            function ($error) {
                                return back()->withErrors($error);
                            },
                            function ($obj) use ($products, $product_id, $channel_ids) {
                                //on success of saving product, save that in which channel it belongs to.
                                if (sizeof($obj->channels) > 0) {
                                    $products->save_product_status([
                                        'cloned_product_channel_ids' => $obj->channels()->pluck('channel_id'),
                                        'channel_ids' => $channel_ids,
                                        'cloned_product_id' => $obj->id,
                                        'organization_id' => $obj->organization_id,
                                        'type' => 'clone',
                                        'product_id' => $product_id
                                    ]);
                                }
                                return back()->withSuccess('Product Clone Successfully.');
                            },
                            $product_data,
                            [
                                'save_to' => auth()->user()->organization_id ?? null,
                                'check_from' => $invite_channel_ids ?? null
                            ]);
                    },
                    $product_obj
                );
                $category = Category::get()->all();
                foreach ($category as $category){
                    if(isset($category->category_id)){
                        $parent  = Category::withoutGlobalScopes()->where("id",$category->category_id)->get()->first();
                        db::beginTransaction();
                        $category->category_id = Category::where("name" , $parent->name)->get()->first()->id;
                        $category->save();
                        db::commit();
                    }
                }



        return back();


    }


    public function AcceptVendor(Request $request) {
        $invite = Invite::withoutGlobalScopes()->findOrFail($request->id);
        DB::beginTransaction();
        try {
            $invite->channels()->sync($request->channel_ids);
            $invite->is_accepted = 1;
            $invite->is_declined = 0;
            $invite->organization_id_receiver = Auth::user()->organization_id;
            $invite->save();
            DB::Commit();
            return Redirect::back()->withSuccess("Success");
        }
        catch (\Exception $e) {
            DB::rollback();
            dd($e);
            return back()->withErrors(["main" => 'Something went wrong. Please try again later.']);
        }

    }
}
