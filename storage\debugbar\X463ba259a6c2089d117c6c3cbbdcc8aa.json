{"__meta": {"id": "X463ba259a6c2089d117c6c3cbbdcc8aa", "datetime": "2025-05-27 07:49:26", "utime": **********.386179, "method": "GET", "uri": "/products/import/apply-template?template_id=22", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748332165.709659, "end": **********.386208, "duration": 0.676548957824707, "duration_str": "677ms", "measures": [{"label": "Booting", "start": 1748332165.709659, "relative_start": 0, "end": **********.27012, "relative_end": **********.27012, "duration": 0.5604608058929443, "duration_str": "560ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.270134, "relative_start": 0.5604748725891113, "end": **********.38622, "relative_end": 1.1920928955078125e-05, "duration": 0.11608600616455078, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 36496504, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x CSVMapping", "param_count": null, "params": [], "start": **********.378485, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/app.blade.phpCSVMapping", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "CSVMapping"}]}, "route": {"uri": "GET products/import/apply-template", "middleware": "web, check_billing, auth, verified, activeOrganization", "controller": "App\\Http\\Controllers\\Product\\ImportController@apply_template", "namespace": "App\\Http\\Controllers\\Product", "prefix": "/products", "where": [], "as": "import.apply.template", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FImportController.php&line=401\" onclick=\"\">app/Http/Controllers/Product/ImportController.php:401-467</a>"}, "queries": {"nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.007580000000000001, "accumulated_duration_str": "7.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.317436, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 55.013}, {"sql": "select * from `organizations` where `organizations`.`id` = '11' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 3) limit 1", "type": "query", "params": [], "bindings": ["11", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "check_billing", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\BillingMiddleware.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 86}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 86}], "start": **********.338062, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "check_billing:21", "source": {"index": 21, "namespace": "middleware", "name": "check_billing", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\BillingMiddleware.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FMiddleware%2FBillingMiddleware.php&line=21", "ajax": false, "filename": "BillingMiddleware.php", "line": "21"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 55.013, "width_percent": 17.81}, {"sql": "select count(*) as aggregate from `templates` where `id` = '22'", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 903}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 874}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 658}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 457}], "start": **********.350346, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 72.823, "width_percent": 7.124}, {"sql": "select * from `templates` where `templates`.`id` = '22' and `organization_id` = '11' limit 1", "type": "query", "params": [], "bindings": ["22", "11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 418}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.355296, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ImportController.php:418", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FImportController.php&line=418", "ajax": false, "filename": "ImportController.php", "line": "418"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 79.947, "width_percent": 9.499}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 3 and `organizations`.`id` = '11' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 3) limit 1", "type": "query", "params": [], "bindings": [3, "11", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Providers\\AppServiceProvider.php", "line": 51}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.364947, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:51", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Providers\\AppServiceProvider.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FProviders%2FAppServiceProvider.php&line=51", "ajax": false, "filename": "AppServiceProvider.php", "line": "51"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 89.446, "width_percent": 10.554}]}, "models": {"data": {"App\\Models\\Organization\\Organization": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}, "App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Apimio\\MappingConnectorPackage\\models\\Template": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fmapping-fields-package%2Fsrc%2Fmodels%2FTemplate.php&line=1", "ajax": false, "filename": "Template.php", "line": "?"}}}, "count": 4, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/products/import/apply-template?template_id=22\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "password_hash_web": "$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq", "organization_id": "11", "data": "array:11 [\n  \"input_array\" => array:2 [\n    \"array_name\" => \"CSV\"\n    \"nodes\" => array:1 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:4 [\n          \"Product Name\" => \"Product Name\"\n          \"SKU\" => \"SKU\"\n          \"Price\" => \"Price\"\n          \"Quantity\" => \"Quantity\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_input_array\" => array:1 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:4 [\n        0 => array:2 [\n          \"label\" => \"Product Name\"\n          \"value\" => \"Default,Product Name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"SKU\"\n          \"value\" => \"Default,SKU\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Price\"\n          \"value\" => \"Default,Price\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Quantity\"\n          \"value\" => \"Default,Quantity\"\n        ]\n      ]\n    ]\n  ]\n  \"file_path\" => \"mapping_fields/upload/json/1748332117_datafile.csv\"\n  \"file_url\" => \"https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1748332117_datafile.csv\"\n  \"data_required\" => array:9 [\n    \"template_method_type\" => \"import\"\n    \"output_type\" => \"Apimio\"\n    \"sync\" => false\n    \"redirect_url_route\" => \"products.import\"\n    \"organization_id\" => \"11\"\n    \"versions\" => array:1 [\n      8 => \"en-US\"\n    ]\n    \"catalogs\" => array:1 [\n      8 => \"Tanzayb Store\"\n    ]\n    \"import_action\" => \"3\"\n    \"selected_template\" => array:9 [\n      \"temp_id\" => 22\n      \"temp_name\" => \"testing123\"\n      \"created_at\" => Illuminate\\Support\\Carbon @1748331835 {#2468\n        #endOfTime: false\n        #startOfTime: false\n        #constructedObjectId: \"00000000000009a40000000000000000\"\n        #localMonthsOverflow: null\n        #localYearsOverflow: null\n        #localStrictModeEnabled: null\n        #localHumanDiffOptions: null\n        #localToStringFormat: null\n        #localSerializer: null\n        #localMacros: null\n        #localGenericMacros: null\n        #localFormatFunction: null\n        #localTranslator: null\n        #dumpProperties: array:3 [\n          0 => \"date\"\n          1 => \"timezone_type\"\n          2 => \"timezone\"\n        ]\n        #dumpLocale: null\n        #dumpDateProperties: null\n        date: 2025-05-27 07:43:55.0 UTC (+00:00)\n      }\n      \"payload\" => array:1 [\n        \"data\" => array:40 [\n          0 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Handle\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => array:1 [\n              0 => \"Default,handle\"\n            ]\n            \"id\" => \"fe16f7d9-05c5-421a-8a12-ecfeeba7d07b\"\n          ]\n          1 => array:5 [\n            \"from\" => array:1 [\n              0 => \"Default,Title\"\n            ]\n            \"with_formula\" => \"split\"\n            \"to\" => array:2 [\n              0 => \"General,product_name\"\n              1 => \"Default,brand\"\n            ]\n            \"id\" => \"6705fb52-01b0-407f-955d-992099143c3f\"\n            \"with\" => \"32\"\n          ]\n          2 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Body (HTML)\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"cb32e4a7-5b2d-45cd-9d0f-93257e60c86d\"\n          ]\n          3 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Vendor\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => array:1 [\n              0 => \"Default,vendor\"\n            ]\n            \"id\" => \"85a3d130-d45a-412b-9210-0b5f732a5896\"\n          ]\n          4 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Type\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"5be8b95a-e97e-4a3a-ac81-18c7f97c55c9\"\n          ]\n          5 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Tags\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => array:1 [\n              0 => \"SEO,seo_keyword\"\n            ]\n            \"id\" => \"bf741402-0ad8-419e-afcc-6d020a92da69\"\n          ]\n          6 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Published\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"693ed288-7891-46cc-8800-6e8c131481fd\"\n          ]\n          7 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Option1 Name\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"cd0a5f3a-0273-49bc-968e-3e9c0b229ef9\"\n          ]\n          8 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Option1 Value\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"c7bf20c8-4f47-446f-94d8-fa8666b95ba7\"\n          ]\n          9 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Option2 Name\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"7f3351c0-d6bc-4183-aa2a-b79402001132\"\n          ]\n          10 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Option2 Value\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"59f17836-b613-4dcc-aebc-d1a24512472c\"\n          ]\n          11 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Option3 Name\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"6066071c-bd03-414e-9bf2-a17e19e1b3f1\"\n          ]\n          12 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Option3 Value\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"6d8b6000-28fe-473e-a340-982ed136f69e\"\n          ]\n          13 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant SKU\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"65ff55a6-3c9a-42fb-be0f-5e5a5f217180\"\n          ]\n          14 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Grams\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"100d4c68-49ed-4975-85c3-7cb616b34aec\"\n          ]\n          15 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Inventory Tracker\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"9f367dfc-1f55-4ab7-bbc7-c6ddeae72148\"\n          ]\n          16 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Inventory Qty\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"59e8b96b-8d17-4c5e-b848-9c812646fd15\"\n          ]\n          17 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Inventory Policy\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"aaad8b73-a2c6-4745-b815-a18b63060d89\"\n          ]\n          18 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Fulfillment Service\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"8d2e46c4-be66-4e20-98bd-a903e00f0e10\"\n          ]\n          19 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Price\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"d4ed4b7e-23c6-43d8-b35a-02bb4bb8a42e\"\n          ]\n          20 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Compare At Price\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"e9c6058e-d3ac-43c3-8d2d-074d62e2e257\"\n          ]\n          21 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Requires Shipping\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"03c015ba-f450-45e7-b0dc-932bdbf8fe45\"\n          ]\n          22 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Taxable\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"6d34cf53-17e7-4b40-98a8-c179946ec769\"\n          ]\n          23 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Barcode\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"7a9ad80a-f2f4-4d15-8374-62f7607eff9e\"\n          ]\n          24 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Image Src\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"2258a13a-f9b1-4730-bc9f-61ea436d11c9\"\n          ]\n          25 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Image Position\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"66679437-46fd-4382-a400-3368f59ea350\"\n          ]\n          26 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Image Alt Text\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"9c13d19d-fb94-4e20-a54e-05270aeaf283\"\n          ]\n          27 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Gift Card\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"8909e053-6392-4b52-8e7b-b952297a451f\"\n          ]\n          28 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,SEO Title\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => array:1 [\n              0 => \"SEO,seo_title\"\n            ]\n            \"id\" => \"a5a5f706-64dd-4c5a-89e9-c8f699a5b0b0\"\n          ]\n          29 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,SEO Description\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => array:1 [\n              0 => \"SEO,seo_description\"\n            ]\n            \"id\" => \"2ace1bb8-0be5-4890-af5d-dc205dfd3386\"\n          ]\n          30 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / Google Product Category\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"1dcd32fe-9a93-44b7-9120-1304a2ad1d05\"\n          ]\n          31 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / Gender\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"a7ea99aa-29f7-4415-bda7-73d125fbc8fe\"\n          ]\n          32 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / Age Group\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"9d2e5a47-6752-4d0e-8d72-226f711141e2\"\n          ]\n          33 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / MPN\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"c0a09c14-605b-4c71-a1bb-4b2a8852cf27\"\n          ]\n          34 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / AdWords Grouping\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"ae56d0c9-b5df-4fbc-8df6-44d97dcb9d2e\"\n          ]\n          35 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / AdWords Labels\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"df3c757c-31da-415c-80ae-8bab629391c5\"\n          ]\n          36 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / Condition\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"21981b1e-0913-4a3a-a24b-e73c68f4873c\"\n          ]\n          37 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / Custom Product\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"0161d6d0-03b0-421a-b54a-c83346bcfc1d\"\n          ]\n          38 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / Custom Label 0\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"76620d95-3ded-49aa-832d-2fdd09858a89\"\n          ]\n          39 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / Custom Label 1\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"d1c6b48d-2a9d-426a-9fed-f459aa37719a\"\n          ]\n        ]\n      ]\n      \"catalog\" => array:1 [\n        0 => \"8\"\n      ]\n      \"version\" => \"8\"\n      \"type\" => \"import\"\n      \"export_type\" => null\n      \"product_status\" => 1\n    ]\n  ]\n  \"import_action\" => 3\n  \"apimio_attributes_required\" => array:2 [\n    \"all_families\" => []\n    \"all_attributes\" => array:6 [\n      0 => array:2 [\n        \"id\" => 1\n        \"name\" => \"single line text\"\n      ]\n      1 => array:2 [\n        \"id\" => 2\n        \"name\" => \"number\"\n      ]\n      2 => array:2 [\n        \"id\" => 3\n        \"name\" => \"multi line text\"\n      ]\n      3 => array:2 [\n        \"id\" => 7\n        \"name\" => \"measurement\"\n      ]\n      4 => array:2 [\n        \"id\" => 9\n        \"name\" => \"json\"\n      ]\n      5 => array:2 [\n        \"id\" => 11\n        \"name\" => \"url\"\n      ]\n    ]\n  ]\n  \"template_attributes\" => []\n  \"output_array\" => array:2 [\n    \"array_name\" => \"Apimio\"\n    \"nodes\" => array:6 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:5 [\n          \"handle\" => \"Product Identifier\"\n          \"file\" => \"Product Images\"\n          \"vendor\" => \"Vendor\"\n          \"brand\" => \"Brand\"\n          \"categories\" => \"Category\"\n        ]\n      ]\n      1 => array:2 [\n        \"name\" => \"General\"\n        \"attributes\" => array:2 [\n          \"product_name\" => \"Product Name\"\n          \"description\" => \"Description\"\n        ]\n      ]\n      2 => array:2 [\n        \"name\" => \"Variant\"\n        \"attributes\" => array:11 [\n          \"sku\" => \"SKU\"\n          \"name\" => \"Name\"\n          \"file\" => \"Image\"\n          \"price\" => \"Price\"\n          \"compare_at_price\" => \"Compare at Price\"\n          \"cost_price\" => \"Cost Price\"\n          \"barcode\" => \"UPC / Barcode\"\n          \"weight\" => \"Weight\"\n          \"weight_unit\" => \"Weight Unit\"\n          \"track_quantity\" => \"Track Quantity\"\n          \"continue_selling\" => \"Continue Selling\"\n        ]\n      ]\n      3 => array:2 [\n        \"name\" => \"Variant Option\"\n        \"attributes\" => array:6 [\n          \"option1_name\" => \"Option 1 Name\"\n          \"option1_value\" => \"Option 1 Value\"\n          \"option2_name\" => \"Option 2 Name\"\n          \"option2_value\" => \"Option 2 Value\"\n          \"option3_name\" => \"Option 3 Name\"\n          \"option3_value\" => \"Option 3 Value\"\n        ]\n      ]\n      4 => array:2 [\n        \"name\" => \"Inventory -> Tanzayb Store\"\n        \"attributes\" => array:1 [\n          8 => \"Tanzayb Store Warehouse\"\n        ]\n      ]\n      5 => array:2 [\n        \"name\" => \"SEO\"\n        \"attributes\" => array:4 [\n          \"seo_url\" => \"URL Slug\"\n          \"seo_title\" => \"SEO Title\"\n          \"seo_description\" => \"SEO Description\"\n          \"seo_keyword\" => \"Tags\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_output_array\" => array:6 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:5 [\n        0 => array:2 [\n          \"label\" => \"Product Identifier\"\n          \"value\" => \"Default,handle\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Product Images\"\n          \"value\" => \"Default,file\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Vendor\"\n          \"value\" => \"Default,vendor\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Brand\"\n          \"value\" => \"Default,brand\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Category\"\n          \"value\" => \"Default,categories\"\n        ]\n      ]\n    ]\n    1 => array:3 [\n      \"label\" => \"General\"\n      \"title\" => \"General\"\n      \"options\" => array:2 [\n        0 => array:2 [\n          \"label\" => \"Product Name\"\n          \"value\" => \"General,product_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Description\"\n          \"value\" => \"General,description\"\n        ]\n      ]\n    ]\n    2 => array:3 [\n      \"label\" => \"Variant\"\n      \"title\" => \"Variant\"\n      \"options\" => array:11 [\n        0 => array:2 [\n          \"label\" => \"SKU\"\n          \"value\" => \"Variant,sku\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Name\"\n          \"value\" => \"Variant,name\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Image\"\n          \"value\" => \"Variant,file\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Price\"\n          \"value\" => \"Variant,price\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Compare at Price\"\n          \"value\" => \"Variant,compare_at_price\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Cost Price\"\n          \"value\" => \"Variant,cost_price\"\n        ]\n        6 => array:2 [\n          \"label\" => \"UPC / Barcode\"\n          \"value\" => \"Variant,barcode\"\n        ]\n        7 => array:2 [\n          \"label\" => \"Weight\"\n          \"value\" => \"Variant,weight\"\n        ]\n        8 => array:2 [\n          \"label\" => \"Weight Unit\"\n          \"value\" => \"Variant,weight_unit\"\n        ]\n        9 => array:2 [\n          \"label\" => \"Track Quantity\"\n          \"value\" => \"Variant,track_quantity\"\n        ]\n        10 => array:2 [\n          \"label\" => \"Continue Selling\"\n          \"value\" => \"Variant,continue_selling\"\n        ]\n      ]\n    ]\n    3 => array:3 [\n      \"label\" => \"Variant Option\"\n      \"title\" => \"Variant Option\"\n      \"options\" => array:6 [\n        0 => array:2 [\n          \"label\" => \"Option 1 Name\"\n          \"value\" => \"Variant Option,option1_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Option 1 Value\"\n          \"value\" => \"Variant Option,option1_value\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Option 2 Name\"\n          \"value\" => \"Variant Option,option2_name\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Option 2 Value\"\n          \"value\" => \"Variant Option,option2_value\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Option 3 Name\"\n          \"value\" => \"Variant Option,option3_name\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Option 3 Value\"\n          \"value\" => \"Variant Option,option3_value\"\n        ]\n      ]\n    ]\n    4 => array:3 [\n      \"label\" => \"Inventory -> Tanzayb Store\"\n      \"title\" => \"Inventory -> Tanzayb Store\"\n      \"options\" => array:1 [\n        0 => array:2 [\n          \"label\" => \"Tanzayb Store Warehouse\"\n          \"value\" => \"Inventory -> Tanzayb Store,8\"\n        ]\n      ]\n    ]\n    5 => array:3 [\n      \"label\" => \"SEO\"\n      \"title\" => \"SEO\"\n      \"options\" => array:4 [\n        0 => array:2 [\n          \"label\" => \"URL Slug\"\n          \"value\" => \"SEO,seo_url\"\n        ]\n        1 => array:2 [\n          \"label\" => \"SEO Title\"\n          \"value\" => \"SEO,seo_title\"\n        ]\n        2 => array:2 [\n          \"label\" => \"SEO Description\"\n          \"value\" => \"SEO,seo_description\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Tags\"\n          \"value\" => \"SEO,seo_keyword\"\n        ]\n      ]\n    ]\n  ]\n  \"mapping_data\" => array:40 [\n    0 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Handle\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Default,handle\"\n      ]\n      \"id\" => \"fe16f7d9-05c5-421a-8a12-ecfeeba7d07b\"\n    ]\n    1 => array:5 [\n      \"from\" => array:1 [\n        0 => \"Default,Title\"\n      ]\n      \"with_formula\" => \"split\"\n      \"to\" => array:2 [\n        0 => \"General,product_name\"\n        1 => \"Default,brand\"\n      ]\n      \"id\" => \"6705fb52-01b0-407f-955d-992099143c3f\"\n      \"with\" => \"32\"\n    ]\n    2 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Body (HTML)\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"cb32e4a7-5b2d-45cd-9d0f-93257e60c86d\"\n    ]\n    3 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Vendor\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Default,vendor\"\n      ]\n      \"id\" => \"85a3d130-d45a-412b-9210-0b5f732a5896\"\n    ]\n    4 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Type\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"5be8b95a-e97e-4a3a-ac81-18c7f97c55c9\"\n    ]\n    5 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Tags\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_keyword\"\n      ]\n      \"id\" => \"bf741402-0ad8-419e-afcc-6d020a92da69\"\n    ]\n    6 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Published\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"693ed288-7891-46cc-8800-6e8c131481fd\"\n    ]\n    7 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Option1 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"cd0a5f3a-0273-49bc-968e-3e9c0b229ef9\"\n    ]\n    8 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Option1 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"c7bf20c8-4f47-446f-94d8-fa8666b95ba7\"\n    ]\n    9 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Option2 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"7f3351c0-d6bc-4183-aa2a-b79402001132\"\n    ]\n    10 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Option2 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"59f17836-b613-4dcc-aebc-d1a24512472c\"\n    ]\n    11 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Option3 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"6066071c-bd03-414e-9bf2-a17e19e1b3f1\"\n    ]\n    12 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Option3 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"6d8b6000-28fe-473e-a340-982ed136f69e\"\n    ]\n    13 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant SKU\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"65ff55a6-3c9a-42fb-be0f-5e5a5f217180\"\n    ]\n    14 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Grams\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"100d4c68-49ed-4975-85c3-7cb616b34aec\"\n    ]\n    15 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Tracker\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"9f367dfc-1f55-4ab7-bbc7-c6ddeae72148\"\n    ]\n    16 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Qty\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"59e8b96b-8d17-4c5e-b848-9c812646fd15\"\n    ]\n    17 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Policy\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"aaad8b73-a2c6-4745-b815-a18b63060d89\"\n    ]\n    18 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Fulfillment Service\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"8d2e46c4-be66-4e20-98bd-a903e00f0e10\"\n    ]\n    19 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"d4ed4b7e-23c6-43d8-b35a-02bb4bb8a42e\"\n    ]\n    20 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Compare At Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"e9c6058e-d3ac-43c3-8d2d-074d62e2e257\"\n    ]\n    21 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Requires Shipping\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"03c015ba-f450-45e7-b0dc-932bdbf8fe45\"\n    ]\n    22 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Taxable\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"6d34cf53-17e7-4b40-98a8-c179946ec769\"\n    ]\n    23 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Barcode\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"7a9ad80a-f2f4-4d15-8374-62f7607eff9e\"\n    ]\n    24 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Src\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"2258a13a-f9b1-4730-bc9f-61ea436d11c9\"\n    ]\n    25 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Position\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"66679437-46fd-4382-a400-3368f59ea350\"\n    ]\n    26 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Alt Text\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"9c13d19d-fb94-4e20-a54e-05270aeaf283\"\n    ]\n    27 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Gift Card\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"8909e053-6392-4b52-8e7b-b952297a451f\"\n    ]\n    28 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,SEO Title\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_title\"\n      ]\n      \"id\" => \"a5a5f706-64dd-4c5a-89e9-c8f699a5b0b0\"\n    ]\n    29 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,SEO Description\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_description\"\n      ]\n      \"id\" => \"2ace1bb8-0be5-4890-af5d-dc205dfd3386\"\n    ]\n    30 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Google Product Category\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"1dcd32fe-9a93-44b7-9120-1304a2ad1d05\"\n    ]\n    31 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Gender\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"a7ea99aa-29f7-4415-bda7-73d125fbc8fe\"\n    ]\n    32 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Age Group\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"9d2e5a47-6752-4d0e-8d72-226f711141e2\"\n    ]\n    33 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / MPN\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"c0a09c14-605b-4c71-a1bb-4b2a8852cf27\"\n    ]\n    34 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / AdWords Grouping\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"ae56d0c9-b5df-4fbc-8df6-44d97dcb9d2e\"\n    ]\n    35 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / AdWords Labels\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"df3c757c-31da-415c-80ae-8bab629391c5\"\n    ]\n    36 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Condition\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"21981b1e-0913-4a3a-a24b-e73c68f4873c\"\n    ]\n    37 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Product\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"0161d6d0-03b0-421a-b54a-c83346bcfc1d\"\n    ]\n    38 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 0\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"76620d95-3ded-49aa-832d-2fdd09858a89\"\n    ]\n    39 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 1\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"d1c6b48d-2a9d-426a-9fed-f459aa37719a\"\n    ]\n  ]\n]", "success": "Template applied successfully"}, "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f0287c5-e0a1-4062-8d93-b2b945a77cbe\" target=\"_blank\">View in Telescope</a>", "path_info": "/products/import/apply-template", "status_code": "<pre class=sf-dump id=sf-dump-1818775203 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1818775203\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-923835367 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>template_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">22</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-923835367\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-568690392 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-568690392\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2027860638 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1317 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlBnUFZnSUIxd2RabTBsbnNiNVpmTUE9PSIsInZhbHVlIjoibEYxTENta2l3WE5hdnQ2UGVMeE9KU2lZdEE4YitHM1dsNlpJMDRpTUROUUtuc2FhU2l6Qnl4aklkYXZpK0l5WXhCT0g3Yi9UTE5XUXlpWUtUVFM4OXNkTG5BYnRtRlk2cUtuOUdKVC9NYmpQRmNSSWFCL20wSEVBekE0WVQ4L1dpZ21xVkE2bmJDblVGdWtZYmdhenprZ1J4NzFFZjFHWEs4NDhGM0l6ZkJ5RGJSRVAwVTFpSUFjNE5jOGdXY2JLcU02VWRjc2lwZWQxRzBHNlZSUFEydERmdW5CN3UvdGJwT2VkMDIwd08xQT0iLCJtYWMiOiJiYmM1ZTEzODhjYjA3MzUyMDE3Zjc0ZTI0YWE3N2RjNjUxYmJjOTgzOWZkYjJiNWI0ZTIzNWU0MjdiZjFlOTBhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik1qYy8zVzF5Ny9nTXhHbEdOa3NmclE9PSIsInZhbHVlIjoiNjVGa1kybDNEQ1o3aXgxYkxwVUt3NU05Ym8wZlZpc1pPOTIyNlljNS9xNWxvT2NaeitTZnhjVzlGSU5YQzVobDZzVld0Z3czMS9nemNDd0M3RTNTZmJkRHpaSUpuenh3ZVp4UE5scmx6VXhUQStsT25sQlZQR0dSWHk5WnlBTTUiLCJtYWMiOiIxZjNiNDZjZDg1ZmVhNjZhYjQyYTBhZmM4NTkzZDRjZjQ4MzJiZmNkOWRkNWUxMjc3NzNjZDczY2EwZjIyMTdiIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6Ilp1eDJhNHdzb2x0ZkZqdThyenpKR2c9PSIsInZhbHVlIjoiM2RVS1hsRTk1dnp5YmRXWWtxRTczclNQaTJtUGJBa2Y1RzNUUTVpQ1Nmdko0K3YxV2w1TUkxWDJiOXp2VUs1QXJPR2czTit0QldGWGs1ZEhERmdkZE9DYi9VSVowL1RadjJQMUdVaFZqNEpSVVRLbE10SjVxb1B5ZmlqelRGM3kiLCJtYWMiOiI5MjExMWFkYWE3ZjZkY2ZhYzUwMzVhM2RkNmFiYWQxZmI1YmQzYmNkMjljNWE5MjczZTgxN2U4ODJmNmFhMDRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2027860638\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1846440994 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">3|lyOnN9rFaNXD362RGbrGZMM746BZDyLiYeGFkfAHYF0FfGnJZdaE7RGsFJsg|$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k1WJWB6ZnSJcQ4dYcV3CZVMdsUzKOM46xMgpx7dI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1846440994\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1331799106 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 27 May 2025 07:49:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>vary</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">X-Inertia</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkttU0QwTlF1OHMvblJMRHlnUkxZeXc9PSIsInZhbHVlIjoibmRqakhnTzdxS3g4cE12b1BUWFF3SzFGZWJRbnc0Z3ZHWkJxdXlUYWtDbEhUYlpYWUZmM242TW1vSjZjVzJuK2tTbU5BZzdla1FiS3ZKbnN4Q1ZSUm5ib0JKRFBNU1JwOFVPTmlBeTdmVnRUbjBZVXM4UnFLOEpIaXFWMVo3TGQiLCJtYWMiOiJkYzk5MTg2NzVjM2FlZjc1ZjFjMzhhZWVmYTU3ZjNjNjU1MmFjNjE2MTM3Y2RiYjlhNDQwZDIwM2ZhZmE5MDI1IiwidGFnIjoiIn0%3D; expires=Tue, 27 May 2025 09:49:26 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6ImJZS2t6Q1hwbEs5NzBSaXFYRm1GY1E9PSIsInZhbHVlIjoiM256Q3Z2bWorM3kyMVdqOCtRZDV5TDZ4WGJIVVJjT21yUlFuNDBXTkdUbnI5aTZaQm9lN2NZQzM3ZnhXOFBjTm5FLzdQbWp0ajdRR2pPdjQ3dGZuN3BtVmNib25uQnl5dk1SSkZYNHlDVE5rNyt0R0dJZVIrWmF3S1V0dDdvMG8iLCJtYWMiOiJkNDgxZGZlYjRlMzI2MDhhYWU5NjlkMmRhMTYzMWZkNDliMWEwNTdkYTc0MDQ3NjNlMzY5MGI1NjFiMWY1MTE5IiwidGFnIjoiIn0%3D; expires=Tue, 27 May 2025 09:49:26 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkttU0QwTlF1OHMvblJMRHlnUkxZeXc9PSIsInZhbHVlIjoibmRqakhnTzdxS3g4cE12b1BUWFF3SzFGZWJRbnc0Z3ZHWkJxdXlUYWtDbEhUYlpYWUZmM242TW1vSjZjVzJuK2tTbU5BZzdla1FiS3ZKbnN4Q1ZSUm5ib0JKRFBNU1JwOFVPTmlBeTdmVnRUbjBZVXM4UnFLOEpIaXFWMVo3TGQiLCJtYWMiOiJkYzk5MTg2NzVjM2FlZjc1ZjFjMzhhZWVmYTU3ZjNjNjU1MmFjNjE2MTM3Y2RiYjlhNDQwZDIwM2ZhZmE5MDI1IiwidGFnIjoiIn0%3D; expires=Tue, 27-May-2025 09:49:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6ImJZS2t6Q1hwbEs5NzBSaXFYRm1GY1E9PSIsInZhbHVlIjoiM256Q3Z2bWorM3kyMVdqOCtRZDV5TDZ4WGJIVVJjT21yUlFuNDBXTkdUbnI5aTZaQm9lN2NZQzM3ZnhXOFBjTm5FLzdQbWp0ajdRR2pPdjQ3dGZuN3BtVmNib25uQnl5dk1SSkZYNHlDVE5rNyt0R0dJZVIrWmF3S1V0dDdvMG8iLCJtYWMiOiJkNDgxZGZlYjRlMzI2MDhhYWU5NjlkMmRhMTYzMWZkNDliMWEwNTdkYTc0MDQ3NjNlMzY5MGI1NjFiMWY1MTE5IiwidGFnIjoiIn0%3D; expires=Tue, 27-May-2025 09:49:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1331799106\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1461003091 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"67 characters\">http://localhost:8000/products/import/apply-template?template_id=22</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq</span>\"\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>input_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">CSV</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>Product Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>SKU</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>Price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>Quantity</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_input_array</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Product Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Default,SKU</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,Price</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Default,Quantity</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"50 characters\">mapping_fields/upload/json/1748332117_datafile.csv</span>\"\n    \"<span class=sf-dump-key>file_url</span>\" => \"<span class=sf-dump-str title=\"100 characters\">https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1748332117_datafile.csv</span>\"\n    \"<span class=sf-dump-key>data_required</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>template_method_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n      \"<span class=sf-dump-key>output_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>sync</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>redirect_url_route</span>\" => \"<span class=sf-dump-str title=\"15 characters\">products.import</span>\"\n      \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n      \"<span class=sf-dump-key>versions</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>8</span> => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>catalogs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>8</span> => \"<span class=sf-dump-str title=\"13 characters\">Tanzayb Store</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>selected_template</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>temp_id</span>\" => <span class=sf-dump-num>22</span>\n        \"<span class=sf-dump-key>temp_name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">testing123</span>\"\n        \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\Carbon @1748331835\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Carbon @1748331835</span> {<a class=sf-dump-ref>#2468</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000009a40000000000000000</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n          <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Tuesday, May 27, 2025\n- 00:05:31.410631 from now\nDST Off\">2025-05-27 07:43:55.0 UTC (+00:00)</span>\n        </samp>}\n        \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:40</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Handle</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">fe16f7d9-05c5-421a-8a12-ecfeeba7d07b</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,Title</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"5 characters\">split</span>\"\n              \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6705fb52-01b0-407f-955d-992099143c3f</span>\"\n              \"<span class=sf-dump-key>with</span>\" => \"<span class=sf-dump-str title=\"2 characters\">32</span>\"\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Body (HTML)</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">cb32e4a7-5b2d-45cd-9d0f-93257e60c86d</span>\"\n            </samp>]\n            <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Vendor</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">85a3d130-d45a-412b-9210-0b5f732a5896</span>\"\n            </samp>]\n            <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Type</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">5be8b95a-e97e-4a3a-ac81-18c7f97c55c9</span>\"\n            </samp>]\n            <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Tags</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">bf741402-0ad8-419e-afcc-6d020a92da69</span>\"\n            </samp>]\n            <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Published</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">693ed288-7891-46cc-8800-6e8c131481fd</span>\"\n            </samp>]\n            <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option1 Name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">cd0a5f3a-0273-49bc-968e-3e9c0b229ef9</span>\"\n            </samp>]\n            <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option1 Value</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">c7bf20c8-4f47-446f-94d8-fa8666b95ba7</span>\"\n            </samp>]\n            <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option2 Name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">7f3351c0-d6bc-4183-aa2a-b79402001132</span>\"\n            </samp>]\n            <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option2 Value</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">59f17836-b613-4dcc-aebc-d1a24512472c</span>\"\n            </samp>]\n            <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option3 Name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6066071c-bd03-414e-9bf2-a17e19e1b3f1</span>\"\n            </samp>]\n            <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option3 Value</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6d8b6000-28fe-473e-a340-982ed136f69e</span>\"\n            </samp>]\n            <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Variant SKU</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">65ff55a6-3c9a-42fb-be0f-5e5a5f217180</span>\"\n            </samp>]\n            <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Grams</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">100d4c68-49ed-4975-85c3-7cb616b34aec</span>\"\n            </samp>]\n            <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Inventory Tracker</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f367dfc-1f55-4ab7-bbc7-c6ddeae72148</span>\"\n            </samp>]\n            <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Variant Inventory Qty</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">59e8b96b-8d17-4c5e-b848-9c812646fd15</span>\"\n            </samp>]\n            <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Inventory Policy</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">aaad8b73-a2c6-4745-b815-a18b63060d89</span>\"\n            </samp>]\n            <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Variant Fulfillment Service</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">8d2e46c4-be66-4e20-98bd-a903e00f0e10</span>\"\n            </samp>]\n            <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Price</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d4ed4b7e-23c6-43d8-b35a-02bb4bb8a42e</span>\"\n            </samp>]\n            <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Compare At Price</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">e9c6058e-d3ac-43c3-8d2d-074d62e2e257</span>\"\n            </samp>]\n            <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Requires Shipping</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">03c015ba-f450-45e7-b0dc-932bdbf8fe45</span>\"\n            </samp>]\n            <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Taxable</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6d34cf53-17e7-4b40-98a8-c179946ec769</span>\"\n            </samp>]\n            <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Barcode</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">7a9ad80a-f2f4-4d15-8374-62f7607eff9e</span>\"\n            </samp>]\n            <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Image Src</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2258a13a-f9b1-4730-bc9f-61ea436d11c9</span>\"\n            </samp>]\n            <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Position</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">66679437-46fd-4382-a400-3368f59ea350</span>\"\n            </samp>]\n            <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Alt Text</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9c13d19d-fb94-4e20-a54e-05270aeaf283</span>\"\n            </samp>]\n            <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Gift Card</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">8909e053-6392-4b52-8e7b-b952297a451f</span>\"\n            </samp>]\n            <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,SEO Title</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">a5a5f706-64dd-4c5a-89e9-c8f699a5b0b0</span>\"\n            </samp>]\n            <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,SEO Description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2ace1bb8-0be5-4890-af5d-dc205dfd3386</span>\"\n            </samp>]\n            <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">Default,Google Shopping / Google Product Category</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">1dcd32fe-9a93-44b7-9120-1304a2ad1d05</span>\"\n            </samp>]\n            <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Google Shopping / Gender</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">a7ea99aa-29f7-4415-bda7-73d125fbc8fe</span>\"\n            </samp>]\n            <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Age Group</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9d2e5a47-6752-4d0e-8d72-226f711141e2</span>\"\n            </samp>]\n            <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Google Shopping / MPN</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">c0a09c14-605b-4c71-a1bb-4b2a8852cf27</span>\"\n            </samp>]\n            <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">Default,Google Shopping / AdWords Grouping</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ae56d0c9-b5df-4fbc-8df6-44d97dcb9d2e</span>\"\n            </samp>]\n            <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / AdWords Labels</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">df3c757c-31da-415c-80ae-8bab629391c5</span>\"\n            </samp>]\n            <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Condition</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">21981b1e-0913-4a3a-a24b-e73c68f4873c</span>\"\n            </samp>]\n            <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Product</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">0161d6d0-03b0-421a-b54a-c83346bcfc1d</span>\"\n            </samp>]\n            <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 0</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">76620d95-3ded-49aa-832d-2fdd09858a89</span>\"\n            </samp>]\n            <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 1</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d1c6b48d-2a9d-426a-9fed-f459aa37719a</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>catalog</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>8</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>version</span>\" => \"<span class=sf-dump-str>8</span>\"\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n        \"<span class=sf-dump-key>export_type</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>product_status</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>import_action</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>apimio_attributes_required</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>all_families</span>\" => []\n      \"<span class=sf-dump-key>all_attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">single line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">multi line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">measurement</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>template_attributes</span>\" => []\n    \"<span class=sf-dump-key>output_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>handle</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>categories</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>compare_at_price</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>cost_price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>barcode</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>weight_unit</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>track_quantity</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>continue_selling</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>option1_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>option1_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>option2_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>option2_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>option3_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>option3_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>8</span> => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>seo_url</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>seo_keyword</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_output_array</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,categories</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">General,description</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,name</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,compare_at_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Variant,cost_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant,barcode</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant,weight</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant,weight_unit</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Variant,track_quantity</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,continue_selling</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option1_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option1_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option2_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option2_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option3_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option3_value</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Inventory -&gt; Tanzayb Store,8</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">SEO,seo_url</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>mapping_data</span>\" => <span class=sf-dump-note>array:40</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Handle</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">fe16f7d9-05c5-421a-8a12-ecfeeba7d07b</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,Title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"5 characters\">split</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6705fb52-01b0-407f-955d-992099143c3f</span>\"\n        \"<span class=sf-dump-key>with</span>\" => \"<span class=sf-dump-str title=\"2 characters\">32</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Body (HTML)</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">cb32e4a7-5b2d-45cd-9d0f-93257e60c86d</span>\"\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Vendor</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">85a3d130-d45a-412b-9210-0b5f732a5896</span>\"\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Type</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">5be8b95a-e97e-4a3a-ac81-18c7f97c55c9</span>\"\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Tags</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">bf741402-0ad8-419e-afcc-6d020a92da69</span>\"\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Published</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">693ed288-7891-46cc-8800-6e8c131481fd</span>\"\n      </samp>]\n      <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option1 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">cd0a5f3a-0273-49bc-968e-3e9c0b229ef9</span>\"\n      </samp>]\n      <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option1 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">c7bf20c8-4f47-446f-94d8-fa8666b95ba7</span>\"\n      </samp>]\n      <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option2 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">7f3351c0-d6bc-4183-aa2a-b79402001132</span>\"\n      </samp>]\n      <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option2 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">59f17836-b613-4dcc-aebc-d1a24512472c</span>\"\n      </samp>]\n      <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option3 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6066071c-bd03-414e-9bf2-a17e19e1b3f1</span>\"\n      </samp>]\n      <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option3 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6d8b6000-28fe-473e-a340-982ed136f69e</span>\"\n      </samp>]\n      <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Variant SKU</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">65ff55a6-3c9a-42fb-be0f-5e5a5f217180</span>\"\n      </samp>]\n      <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Grams</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">100d4c68-49ed-4975-85c3-7cb616b34aec</span>\"\n      </samp>]\n      <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Inventory Tracker</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f367dfc-1f55-4ab7-bbc7-c6ddeae72148</span>\"\n      </samp>]\n      <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Variant Inventory Qty</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">59e8b96b-8d17-4c5e-b848-9c812646fd15</span>\"\n      </samp>]\n      <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Inventory Policy</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">aaad8b73-a2c6-4745-b815-a18b63060d89</span>\"\n      </samp>]\n      <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Variant Fulfillment Service</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">8d2e46c4-be66-4e20-98bd-a903e00f0e10</span>\"\n      </samp>]\n      <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d4ed4b7e-23c6-43d8-b35a-02bb4bb8a42e</span>\"\n      </samp>]\n      <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Compare At Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">e9c6058e-d3ac-43c3-8d2d-074d62e2e257</span>\"\n      </samp>]\n      <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Requires Shipping</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">03c015ba-f450-45e7-b0dc-932bdbf8fe45</span>\"\n      </samp>]\n      <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Taxable</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6d34cf53-17e7-4b40-98a8-c179946ec769</span>\"\n      </samp>]\n      <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Barcode</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">7a9ad80a-f2f4-4d15-8374-62f7607eff9e</span>\"\n      </samp>]\n      <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Image Src</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2258a13a-f9b1-4730-bc9f-61ea436d11c9</span>\"\n      </samp>]\n      <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Position</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">66679437-46fd-4382-a400-3368f59ea350</span>\"\n      </samp>]\n      <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Alt Text</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9c13d19d-fb94-4e20-a54e-05270aeaf283</span>\"\n      </samp>]\n      <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Gift Card</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">8909e053-6392-4b52-8e7b-b952297a451f</span>\"\n      </samp>]\n      <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,SEO Title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">a5a5f706-64dd-4c5a-89e9-c8f699a5b0b0</span>\"\n      </samp>]\n      <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,SEO Description</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2ace1bb8-0be5-4890-af5d-dc205dfd3386</span>\"\n      </samp>]\n      <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">Default,Google Shopping / Google Product Category</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">1dcd32fe-9a93-44b7-9120-1304a2ad1d05</span>\"\n      </samp>]\n      <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Google Shopping / Gender</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">a7ea99aa-29f7-4415-bda7-73d125fbc8fe</span>\"\n      </samp>]\n      <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Age Group</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9d2e5a47-6752-4d0e-8d72-226f711141e2</span>\"\n      </samp>]\n      <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Google Shopping / MPN</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">c0a09c14-605b-4c71-a1bb-4b2a8852cf27</span>\"\n      </samp>]\n      <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">Default,Google Shopping / AdWords Grouping</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ae56d0c9-b5df-4fbc-8df6-44d97dcb9d2e</span>\"\n      </samp>]\n      <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / AdWords Labels</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">df3c757c-31da-415c-80ae-8bab629391c5</span>\"\n      </samp>]\n      <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Condition</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">21981b1e-0913-4a3a-a24b-e73c68f4873c</span>\"\n      </samp>]\n      <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Product</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">0161d6d0-03b0-421a-b54a-c83346bcfc1d</span>\"\n      </samp>]\n      <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 0</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">76620d95-3ded-49aa-832d-2fdd09858a89</span>\"\n      </samp>]\n      <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 1</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d1c6b48d-2a9d-426a-9fed-f459aa37719a</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Template applied successfully</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1461003091\", {\"maxDepth\":0})</script>\n"}}