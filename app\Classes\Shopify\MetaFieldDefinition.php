<?php

namespace App\Classes\Shopify;

use Exception;
use App\Models\Product\Family;
use App\Models\Product\Attribute;
use App\Classes\Facades\UnitFacade;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\Channel\ShopifyChannel;
use App\Models\Notification\ErrorLog;
use Illuminate\Database\Eloquent\HigherOrderBuilderProxy;
use Ghazniali95\ShopifyConnector\App\Classes\Services\MetaFieldDefinitionService;

class MetaFieldDefinition
{
    public $base_url = null;
    /**
     * @var int
     */
    protected $channel_id;
    /**
     * @var int
     */
    protected $organization_id;
    /**
     * @var HigherOrderBuilderProxy|mixed
     */
    protected $access_token;
    /**
     * @var array
     */
    protected $attributes;
    /**
     * @var array|mixed
     */
    protected $data;

    /**
     * @param int $channel_id
     * @param int $organization_id
     */
    public function __construct(int $channel_id, int $organization_id)
    {
        $this->channel_id = $channel_id;
        $this->organization_id = $organization_id;
        $this->createBaseUrl();
    }

    /**
     * @return void
     */
    public function createBaseUrl(){
        $shopify_channel=ShopifyChannel::query()
            ->where('channel_id',$this->channel_id)
            ->first();
        $this->access_token  = $shopify_channel->access_token;
        $this->base_url = 'https://'.$shopify_channel->shop.'/admin/api/2021-10/graphql.json';
    }

    /**
     * @return string
     */
    private function definitionQuery(): string
    {
        return "{
                  metafieldDefinitions(first: 250, ownerType: PRODUCT) {
                  edges {
                      node {
                            id
                            name
                            namespace
                            key
                            description
                            type {
                              name
                            }
                             validations {
                                    name
                                    type
                                    value
                                }
                           }
                      }
                  }
                }";
    }

    /**
     * @return $this|void
     */
    public function getDefinitions(){
        try{
             $MetaFieldDefinitionService = new MetaFieldDefinitionService($this->channel_id);
             $response = $MetaFieldDefinitionService->getAll(['limit' => 250 , 'ownerType' => 'PRODUCT']);
            if($response){
                $this->data = $response;
                return $this;
            }else{
                $data =$response;
                Log::channel('shopify')->info('GET META FIELDS DEFINITIONS ERROR.');
                Log::channel('shopify')->error($data);
            }
        }catch(Exception $exception){
            $data = [
                'organization_id' => $this->organization_id,
                'description' => "There is an error while fetching metafield definition.<br>  {$exception->getMessage()}",
                'type' => 'shopify',
                'link' => '#',
                'link_text' => 'View Product',
                'status' => 'error',
            ];

            $error = new ErrorLog();
            $error->setData($data)->store(function ($error) {
                Log::channel('shopify')->info('Error in saving ErrorLogs.');
                Log::channel('shopify')->error($error);
            }, function () {
            });
            Log::channel('shopify')->info('GET META FIELDS DEFINITIONS ERROR.');
            Log::channel('shopify')->error($exception);
        }
    }


    /**
     * @param $validations
     * @return array
     */
    private function setTextValidationArray($validations): array
    {
        $attributes = array();
        foreach ($validations  as $validation){
            if($validation['name'] == 'min'){
                $attributes['min'] = $validation['value'];
            }
            if($validation['name'] == 'max'){
                $attributes['max'] = $validation['value'];
            }
            if($validation['name'] == 'regex'){
                $attributes['regular_expression'] = $validation['value'];
            }
            if($validation['name'] == 'choices'){
                $options = json_decode($validation['value']);
                foreach($options as $option){
                    $attributes['attribute_options'][]['name']=$option;
                }

            }
        }
        return $attributes;
    }

    /**
     * @param $validations
     * @return array
     */
    private function setMeasurementValidationArray($validations): array
    {
        $attributes = array();
        foreach ($validations  as $validation){
            $value = json_decode($validation['value'] , true);
            if($validation['name'] == 'min'){
                $attributes['min_unit'] = UnitFacade::convert($value['unit']);
                $attributes['min'] = $value['value'];
            }
            if($validation['name'] == 'max'){
                $attributes['max_unit'] = UnitFacade::convert($value['unit']);
                $attributes['max'] = $value['value'];
            }
        }
        return $attributes;
    }

    /**
     * @param $data
     * @return array
     */
    private function getAttributeType($data): array
    {
        $type = $data['type']['name'];
        $attributes = array();

        if($type == 'boolean') {
            $attributes['type'] = 'boolean';
            $attributes['attribute_type_id'] = 10;
            $attributes['value_type'] = 'single';
        }
        elseif($type == 'money'){
            $attributes['type'] = 'price';
            $attributes['attribute_type_id'] = 2;
            $attributes['value_type'] = 'single';
        }
        elseif($type == 'color' || $type == 'list.color') {
            $attributes['attribute_type_id'] = 12;
            $attributes['type'] = 'color';
            if($type == 'list.color'){
                $attributes['value_type'] = 'list';
            }else{
                $attributes['value_type'] = 'single';
            }
        }

        elseif($type == 'date' || $type == 'list.date'){
            $attributes['attribute_type_id'] = 5;
            $attributes['type'] = 'date';
            if($type == 'list.date'){
                $attributes['value_type'] = 'list';
            }else{
                $attributes['value_type'] = 'single';
            }

            foreach ($data['validations']  as $validation){
                if($validation['name'] == 'min'){
                    $attributes['start_date'] = $validation['value'];
                }
                if($validation['name'] == 'max'){
                    $attributes['end_date'] = $validation['value'];
                }
            }

        }

        elseif($type == 'date_time' || $type == 'list.date_time' )  {
            $attributes['attribute_type_id'] = 5;
            $attributes['type'] = 'date_and_time';
            if($type == 'list.date_time'){
                $attributes['value_type'] = 'list';
            }else{
                $attributes['value_type'] = 'single';
            }

            foreach ($data['validations']  as $validation){
                if($validation['name'] == 'min'){
                    $attributes['start_date_time'] = $validation['value'];
                }
                if($validation['name'] == 'max'){
                    $attributes['end_date_time'] = $validation['value'];
                }
            }
        }

        elseif($type == 'json') {
            $attributes['type'] = 'json';
            $attributes['attribute_type_id'] = 9;
            $attributes['value_type'] = 'single';
        }

        elseif($type == 'rating' || $type == 'list.rating' ) {
            $attributes['type'] = 'rating';
            $attributes['attribute_type_id'] = 8;
            if($type == 'list.rating'){
                $attributes['value_type'] = 'list';
            }else{
                $attributes['value_type'] = 'single';
            }

            foreach ($data['validations']  as $validation){
                if($validation['name'] == 'scale_min'){
                    $attributes['min'] = $validation['value'];
                }
                if($validation['name'] == 'scale_max'){
                    $attributes['max'] = $validation['value'];
                }
                if($validation['name'] == 'regex'){
                    $attributes['regular_expression'] = $validation['value'];
                }
            }
        }

        elseif($type == 'single_line_text_field' || $type == 'list.single_line_text_field' ) {

            if($type == 'list.single_line_text_field'){
                $attributes['value_type'] = 'list';
            }else{
                $attributes['value_type'] = 'single';
            }
            $validation_attributes = $this->setTextValidationArray($data['validations']);
            if(isset($validation_attributes['attribute_options'])){
                $attributes['type']="choices";
                $attributes['attribute_type_id'] = 4;
            }else{
                $attributes['attribute_type_id'] = 1;
            }
            $attributes = array_merge($attributes,$validation_attributes);
        }

        elseif($type == 'multi_line_text_field') {
            $attributes['attribute_type_id'] = 3;
            $attributes['value_type'] = 'single';
            $validation_attributes = $this->setTextValidationArray($data['validations']);
            $attributes = array_merge($attributes,$validation_attributes);
        }

        elseif($type == 'number_decimal' || $type == 'list.number_decimal') {
            $attributes['attribute_type_id'] = 2;
            $attributes['type'] = 'decimal';
            if($type == 'list.number_decimal'){
                $attributes['value_type'] = 'list';
            }else{
                $attributes['value_type'] = 'single';
            }

            foreach ($data['validations']  as $validation){
                if($validation['name'] == 'min'){
                    $attributes['min'] = $validation['value'];
                }
                if($validation['name'] == 'max'){
                    $attributes['max'] = $validation['value'];
                }
                if($validation['name'] == 'max_precision'){
                    $attributes['max_number_precision'] = $validation['value'];
                }
            }
        }

        elseif($type == 'number_integer' || $type == 'list.number_integer') {
            $attributes['attribute_type_id'] = 2;
            if($type == 'list.number_integer'){
                $attributes['value_type'] = 'list';
            }else{
                $attributes['value_type'] = 'single';
            }
            $attributes['type'] = 'integer';
            foreach ($data['validations']  as $validation){
                if($validation['name'] == 'min'){
                    $attributes['min'] = $validation['value'];
                }
                if($validation['name'] == 'max'){
                    $attributes['max'] = $validation['value'];
                }
            }
        }

        elseif($type == 'url' || $type == 'list.url') {
            $attributes['attribute_type_id'] = 11;
            if($type == 'list.url'){
                $attributes['value_type'] = 'list';
            }else{
                $attributes['value_type'] = 'single';
            }
        }

        elseif($type == 'volume' || $type == 'list.volume') {
            $attributes['attribute_type_id'] = 7;
            $attributes['type'] = 'volume';
            if($type == 'list.volume'){
                $attributes['value_type'] = 'list';
            }else{
                $attributes['value_type'] = 'single';
            }

            $validation_attributes = $this->setMeasurementValidationArray($data['validations']);
            $attributes = array_merge($attributes,$validation_attributes);

        }


        elseif($type == 'weight' || $type == 'list.weight') {
            $attributes['attribute_type_id'] = 7;
            $attributes['type'] = 'weight';
            if($type == 'list.weight'){
                $attributes['value_type'] = 'list';
            }else{
                $attributes['value_type'] = 'single';
            }

            $validation_attributes = $this->setMeasurementValidationArray($data['validations']);
            $attributes = array_merge($attributes,$validation_attributes);

        }

        elseif($type == 'dimension' || $type == 'list.dimension'){
            $attributes['attribute_type_id'] = 7;
            $attributes['type'] = 'dimension';
            if($type == 'list.dimension'){
                $attributes['value_type'] = 'list';
            }else{
                $attributes['value_type'] = 'single';
            }

            $validation_attributes = $this->setMeasurementValidationArray($data['validations']);
            $attributes = array_merge($attributes,$validation_attributes);
        }

        elseif($type == "file_reference" || $type == "list.file_reference"){
            //currently we are not saving files in database
//            $attributes['attribute_type_id'] = 6;
//
//            if($type == 'list.file_reference'){
//                $attributes['value_type'] = 'list';
//            }else{
//                $attributes['value_type'] = 'single';
//            }
//            foreach ($data['validations']  as $validation){
//                $value = json_decode($validation['value']);
//                if(in_array("GenericFile", $value)){
//                    $attributes['type'] = 'all_files';
//                }else{
//                    $attributes['type'] = 'specific_files';
//                    $attributes['file_choice'] = $value;
//                }
//            }
        }

        else{
            $attributes['attribute_type_id'] = 1;
            $attributes['value_type'] = 'single';
        }
        return $attributes;
    }

    /**
     * @return $this
     */
    public function getAttributeArray(): MetaFieldDefinition
    {
        $data = $this->data;
        $attributes = array();

        foreach ($data['data']['metafieldDefinitions']['edges'] as $attr){
            $array =  (explode("/",$attr['node']['id']));
            $shopify_id = end($array);
            $attributes1['name'] = $attr['node']['name'];
            $attributes1['shopify_id'] = $shopify_id;
            $attributes1['channel_id'] = $this->channel_id;
            $attributes1['description'] = $attr['node']['description'];
            $attributes1['key'] = $attr['node']['key'];
            $attributes1['attribute_set_name'] = $attr['node']['namespace'];
            $attribute_types = $this->getAttributeType($attr['node']  );

            //merge arrays
            $attributes[] = array_merge($attributes1, $attribute_types);

        }
        $this->attributes = $attributes;

        return $this;
    }

    /**
     * @return string
     */
    public function store(): string
    {
        Log::channel('shopify')->info("metafield definitions");
       try {
        foreach ($this->attributes as $attributes) {
            if (isset($attributes['attribute_type_id'])) {
                $family = Family::query()
                    ->where('name', $attributes['attribute_set_name'])
                    ->where('organization_id',$this->organization_id)
                    ->first();

                $attribute = new Attribute();

                if ($family) {
                    $attributes['attribute_family'][] = $family->id;

                    //if attribute is already saved in database or not
                    $existing_attribute = $attribute->query()
                        ->where([
                            'name' => $attributes['name'],
                            'attribute_type_id' => $attributes['attribute_type_id'],
                            'organization_id' => $this->organization_id,
                            'handle' => $attributes['key'],
                        ])
                        ->whereHas('families', function ($q) use ($family) {
                            $q->where('family_id', $family->id);
                        })
                        ->first();

                    if ($existing_attribute) {
                        $attributes['id'] = $existing_attribute->id;
                    }
                }

                    $attribute->set_data($attributes)->set_organization($this->organization_id)->store(
                    // when error
                        function ($error) {
                            Log::channel('shopify')->info('SAVE META-FIELD ATTRIBUTE Error');
                            Log::channel('shopify')->error($error);
                            return $error;
                        },
                        // when success
                        function () {
                            Log::channel('shopify')->info('SAVE META-FIELD ATTRIBUTE');
                        });
            }
        }
        return "success";
       } catch (\Exception $e) {
        $data = [
            'organization_id' => $this->organization_id,
            'description' => "There is an error while saving metafield definitions.<br>  {$e->getMessage()}",
            'type' => 'shopify',
            'link' => '#',
            'link_text' => 'View Product',
            'status' => 'error',
        ];

        $error = new ErrorLog();
        $error->setData($data)->store(function ($error) {
            Log::channel('shopify')->info('Error in saving ErrorLogs.');
            Log::channel('shopify')->error($error);
        }, function () {
        });
       }

    }

    /**
     * @return string
     */
    public function saveAttributeSet(): string
    {
        try {
            foreach ($this->attributes as $attribute){

                $attribute_set = [];
                $family = new Family();

                //check if family already exists or not
                $existing_family = $family->query()
                    ->where([
                        'name'=>$attribute['attribute_set_name'],
                        'organization_id'=>$this->organization_id,
                    ])
                    ->first();

                if($existing_family){
                    $attribute_set['id'] = $existing_family->id;
                }

                $attribute_set['organization_id'] = $this->organization_id;
                $attribute_set['name'] = $attribute['attribute_set_name'];

                if (isset($attribute_set['id'])) {
                    $attribute = $family->find($attribute_set['id']);
                } else {
                    $attribute = $family;
                }

                if(isset($attribute_set["organization_id"])) {
                    $attribute->organization_id = $attribute_set["organization_id"];
                }

                $attribute->name = $attribute_set['name'];
                $attribute->save();


    /*            $family->set_data($attribute_set)->store(
                // when error
                    function ($error) {
                        Log::channel('shopify')->info('CREATE ATTRIBUTE SET FROM META FIELD ATTRIBUTES ERROR');
                        Log::channel('shopify')->error($error);
                    },
                    // when success
                    function () {
                    });*/
            }

            info('CREATE ATTRIBUTE SET FROM META FIELD ATTRIBUTES SUCCESS');
            return 'success';
        } catch (\Exception $e) {
            $data = [
                'organization_id' => $this->organization_id,
                'description' => "There is an error while setting META FIELD ATTRIBUTES definition.<br>  {$e->getMessage()}",
                'type' => 'shopify',
                'link' => '#',
                'link_text' => 'View Product',
                'status' => 'error',
            ];

            $error = new ErrorLog();
            $error->setData($data)->store(function ($error) {
                Log::channel('shopify')->info('Error in saving ErrorLogs.');
                Log::channel('shopify')->error($error);
            }, function () {
            });
        }

    }
}
