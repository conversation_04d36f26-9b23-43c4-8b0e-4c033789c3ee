<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class SettingController extends Controller
{
    public function updateSettingsToggle(Request $request)
    {
        $organizationId = Auth::user()->organization_id;
        foreach ($request->input('variant_settings') as $setting) {
            $keyFormat = str_replace(' ', '_', strtolower($setting['key']));
            $settingInstance = Setting::where('key', $keyFormat)
                ->where('organization_id', $organizationId)
                ->first();

            if ($settingInstance) {
                $settingInstance->update(['value' => $setting['value']]);
            } else {
                Log::error("message", [
                    'message' => "Setting with key {$setting['key']} not found for organization with id {$organizationId}"
                ]);
            }
        }
        return response()->json(['message' => 'Settings updated successfully.']);
    }
}
