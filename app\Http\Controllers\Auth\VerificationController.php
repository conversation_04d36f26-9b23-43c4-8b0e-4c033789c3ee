<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\VerifiesEmails;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Inertia\Response;

class VerificationController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Email Verification Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling email verification for any
    | user that recently registered with the application. Emails may also
    | be re-sent if the user didn't receive the original email message.
    |
    */

    use VerifiesEmails;

    /**
     * Where to redirect users after verification.
     *
     * @var string
     */
    protected $redirectTo = 'onboarding';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('updateHubspotEmailVerification');
        $this->middleware('signed')->only('verify');
        $this->middleware('throttle:6,1')->only('verify', 'resend');
    }

    public function show(Request $request)
    {
        return $request->user()->hasVerifiedEmail()
            ? redirect($this->redirectPath())
            : redirect()->route('verify');
    }

    public function verifiedUserRedirect()
    {
        // Redirect if the user is already verified
        if (auth()->user()->hasVerifiedEmail()) {
            return redirect()->route('dashboard'); // Redirect to dashboard or a different page
        }
    }

    public function renderVerify(): Response
    {
        return Inertia::render('auth/Verify', [
            'user' => auth()->user(),
        ]);
    }
}
