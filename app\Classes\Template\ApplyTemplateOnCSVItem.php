<?php

namespace App\Classes\Template;

use App\Traits\ImportExport\Formulas;
use Illuminate\Support\Facades\Log;
use Psr\Log\InvalidArgumentException;

class ApplyTemplateOnCSVItem
{
    use Formulas;

    /**
     * Convert item from csv with template formulas.
     *
     * @param array $item
     * @param array $formulas
     * @return array
     * */
    public function ConvertItemWithFormula(array $item, array $formulas) : array {
        try {
            $result = [];

            foreach ($formulas as $formula) {
                $formula["from"] = array_filter($formula["from"]);
                $formula["to"] = array_filter($formula["to"]);

                if(!empty($formula["from"]) && !empty($formula["to"])) {
                    $result = array_merge($result, $this->createItem($item, $formula));
                }
            }
            return $result;
        }
        catch (InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }

    /**
     * Create item with single formula.
     *
     * @param  array  $item
     * @param  array  $formula
     * @return array
     */
    public function createItem(array $item, array $formula) : array {
        if(!isset($formula["from"]) && !isset($formula["with_formula"]) && !isset($formula["to"])) {
            throw new InvalidArgumentException("Arguments not supplied to perform formula.");
        }

        $result = [];

        // trim all array keys
        $keys = array_map(function ($q) {
            return trim($q);
        }, array_keys($item));
        $item = array_combine($keys, array_values($item));
        foreach ($formula["from"] as $from) {

            if(isset($from)) {
                $result[] = [
                    "key" => $from,
                    "value" => $item[$from]
                ];
            }
        }


        $formula_obj = $this
            ->from($result)
            ->withFormula($formula["with_formula"]);

        if(isset($formula["with"])) {
            if(!empty($formula["with"])) {
                $formula_obj = $formula_obj->with($formula["with"]);
            }
            else {
                $formula_obj = $formula_obj->with("");
            }
        }
        if(isset($formula["replace"])) {
            if(!empty($formula["replace"])) {
                $formula_obj = $formula_obj->remove($formula["replace"]);
            }
            else {
                $formula_obj = $formula_obj->remove("");
            }
        }



        return $formula_obj->to(array_values($formula["to"]));
    }
}
