<?php

namespace App\Classes;

class Unit
{

    Private Static $KILOGRAMS = ['kg','kilogram','kilograms'];
    Private Static $GRAMS = ['g','gram','grams'];
    Private Static $POUND = ['lb','pound','pounds'];
    Private Static $MILLILITER = ['ml','milliliter','milliliters'];
    Private Static $CENTILITER = ['cl','centiliter','centiliters'];
    Private Static $LITER = ['l','liter','liters'];
    Private Static $FLUID_OUNCE = ['fl oz','fluid ounce','oz','fl_oz','fluid_ounce','fluid_ounces'];
    Private Static $CUBIC_METER = ['cubic metre','cubic meter','cubic_metre','cubic_meter','cubic_meters','m^3','m**3' , "cu m"];
    Private Static $PINT = ['pt','pint' ,'pints' , 'pt.'];
    Private Static $QUART = ['qt','qt.','quart','quarts'];
    Private Static $GALLON = ['gal','gal.','gallon', 'gallons'];
    Private Static $IMP_GALLON = ['imp_gal','imp_gal.','imp gal', 'imperial_gallons', 'imperial_gallon'];
    Private Static $IMP_PINT = ['imp_pt','imp_pt.','imp pt', 'imperial_pints', 'imperial_pint'];
    Private Static $IMP_QUART = ['imp_qt','imp_qt.','imp qt', 'imperial_quarts', 'imperial_quart'];
    Private Static $IMP_OUNCE = ['imp fl oz','imperial fluid ounce','imp_fl_oz','imperial_fluid_ounce','imperial_fluid_ounces'];
    Private Static $FEET = ['ft.', 'ft','feet', 'foot'];
    Private Static $YARD = ['yd.', 'yd','yard', 'yards'];
    Private Static $MILLIMETER = ['mm.', 'mm','millimeter', 'millimeters','millimetre','millimetres'];
    Private Static $CENTIMETER = ['cm.', 'cm','centimeter', 'centimeters','centimetre','centimetres'];
    Private Static $METER = ['m.', 'm','meter', 'meters','metre','metres'];
    Private Static $INCHES = ['in','inch','inches'];


    private Static $APIMIO_UNITS = [ 'kg' => 1000 , 'lb' => 453.592 , 'oz' => 28.3495 , 'g' => 1 ];

    public Static $VOLUME_UNITS = [
        'ml' => 'Milliliter',
        'cl' => 'Centiliter',
        'l' => 'Liter',
        'us_fl_oz' => 'Fluid Ounce',
        'm3' => 'Cubic Meter',
        'us_pt' => 'Pint',
        'us_qt' => 'Quart',
        'us_gal' => 'Gallon',
        'imp_gal' => 'Imperial Gallon',
        'imp_pt' => 'Imperial Pint',
        'imp_qt' => 'Imperial Quart',
        'imp_fl_oz' => 'Imperial Fluid Ounce'
    ];

    public Static $WEIGHT_UNITS = [
        'kg' => 'Kilogram',
        'lb' => 'Pound',
        'oz' => 'Ounce',
        'g' => 'Gram'
    ];

    public Static $DIMENSION_UNITS = [
        'ft' => 'Feet',
        'yd' => 'Yard',
        'cm' => 'Centimeter',
        'mm' => 'Millimeter',
        'm' => 'Meter',
        'in' => 'Inch'
    ];


    public function get_units($units = ['volume','weight','dimension']) {
        $units_array = [];
        foreach($units as $unit){
            if($unit == 'volume'){
                $units_array['volume'] = self::$VOLUME_UNITS;
            }
            elseif($unit == 'weight'){
                $units_array['weight'] = self::$WEIGHT_UNITS;
            }
            elseif($unit == 'dimension'){
                $units_array['dimension'] = self::$DIMENSION_UNITS;
            }
        }
        return $units_array;
    }

    public function convert(string $value) {

        $value = strtolower($value);
        if(in_array($value, self::$GRAMS)){
            return "g";
        }
        elseif(in_array($value, self::$POUND)){
            return "lb";
        }
        elseif(in_array($value, self::$KILOGRAMS)){
            return "kg";
        }
        elseif(in_array($value, self::$MILLILITER)){
            return "ml";
        }
        elseif(in_array($value, self::$CENTILITER)){
            return "cl";
        }
        elseif(in_array($value, self::$LITER)){
            return "l";
        }
        elseif(in_array($value, self::$FLUID_OUNCE)){
            return "us_fl_oz";
        }
        elseif(in_array($value, self::$CUBIC_METER)){
            return "m3";
        }
        elseif(in_array($value, self::$PINT)){
            return "us_pt";
        }
        elseif(in_array($value, self::$QUART)){
            return "us_qt";
        }
        elseif(in_array($value, self::$GALLON)){
            return "us_gal";
        }
        elseif(in_array($value, self::$IMP_GALLON)){
            return "imp_gal";
        }
        elseif(in_array($value, self::$IMP_PINT)){
            return "imp_pt";
        }
        elseif(in_array($value, self::$IMP_QUART)){
            return "imp_qt";
        }
        elseif(in_array($value, self::$IMP_OUNCE)){
            return "imp_fl_oz";
        }
        elseif(in_array($value, self::$FEET)){
            return "ft";
        }
        elseif(in_array($value, self::$YARD)){
            return "yd";
        }
        elseif(in_array($value, self::$CENTIMETER)){
            return "cm";
        }
        elseif(in_array($value, self::$MILLIMETER)){
            return "mm";
        }
        elseif(in_array($value, self::$METER)){
            return "m";
        }
        elseif(in_array($value, self::$INCHES)){
            return "in";
        }
    }

    public function convert_value(array $unit) {
        $g_weight = $this->unit_converted_to_g($unit);
        $to_unit = $unit['to_unit'] ?? 'g';
        if (in_array($to_unit, array_keys(self::$APIMIO_UNITS))){
            $g_weight = (float)$g_weight / (float)self::$APIMIO_UNITS[$to_unit];
        }

        return $g_weight;
    }


    private function unit_converted_to_g($unit){
        $value = $unit['value'] ?? 0;
        $from_unit =  $unit['from_unit'] ?? 'g';

        $gram_weight = $value;
        if (in_array($from_unit, array_keys(self::$APIMIO_UNITS))){
            $gram_weight = (float)$value * (float)self::$APIMIO_UNITS[$from_unit];
        }

        return $gram_weight;
    }
    public static function formatUnitKey($key){
        $formattedKey = str_replace(['us_', '_', 'm3'], ['', ' ', 'm³'], $key);
            return $formattedKey;
    }

}
