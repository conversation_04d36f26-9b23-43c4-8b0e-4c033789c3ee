<?php

namespace App\Jobs\Import;

use App\Classes\ImportExport;
use App\Traits\AWSSetting;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class StoreProductsChunkArray implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels, AWSSetting;

    public $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public $file_url)
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->data = $this->decodeS3Json($this->file_url);
       // info($this->data);
        $import_export_obj = new ImportExport();
        $import_export_obj->import_csv_products_chunk($this->data['chunk'],$this->data['converted_arrays'],$this->data['attributes_with_values'], $this->data['all_data']);
    }
}
