<?php

namespace App\Classes\Bulk;

use Apimio\MappingConnectorPackage\traits\Formulas;
use App\Classes\Facades\UnitFacade;
use App\Classes\ProductFilter;
use App\Classes\Unit;
use App\Events\ChannelUpdateStatusEvent;
use App\Events\Product\CalculateScore;
use App\Jobs\Bulk\BulkAssignChunkArray;
use App\Models\BatchProgress;
use App\Models\Channel\Channel;
use App\Models\Filter;
use App\Models\Invite\Invite;
use App\Models\Product\AttributeFamily;
use App\Models\Product\AttributeFamilyProductVersion;
use App\Models\Product\Brand;
use App\Models\Product\Category;
use App\Models\Product\Family;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\ProductVariantSetting;
use App\Notifications\ApimioNotification;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

class BulkEdit
{
    protected $data;
    use Formulas;


    /**
     * Set data bulk editing.
     *
     *
     * @param array
     *
     */
    public function set_data(array $data)
    {
        $this->data = $data;
        return $this;
    }


    public function products_bulk_assign($error_callback, $success_callback)
    {
        $product_obj = new Product();

        $chunks_jobs = [];
        $jobable = false;

        $batch_data = $this->data;

        try {
            $product_obj->fetch(
                function ($product) {
                    if (isset($this->data['required_data']['organization_id'])) {
                        $product = $product->where('organization_id', $this->data['required_data']['organization_id']);
                    } else {
                        Log::warning("Organization_id not found in bulk assign product fetch method");
                    }
                    if ($this->data['products'] == "all") {
                        if ($this->data['filter_products']){
                            $filters = new ProductFilter();
                            $filters->apply_filters($product , json_decode($this->data['filter_products'], true));
                        }
                        return $product;
                    } else {
                        $this->data['products'] = explode(',', $this->data['products']);
                        return $product->whereIn('id', $this->data['products']);
                    }
                }
            )->chunk(
                50,
                function ($products) use ($product_obj, &$jobable, &$chunks_jobs) {

                    //////////////////////////////////////below code is for without queue testing do not remove this code//////////////////////////////////
                    //                    $this->set_products_chunk_attributes($products);


                    ///////////// product chunks (JOB BATCHING) //////////////
                    $jobable = true;
                    $chunks_jobs[] = new BulkAssignChunkArray($products, $this->data);
                    ///////////// (JOB BATCHING END) //////////////

                }
            );


            //if job able is true then start batching
            if ($jobable) {
                if (isset($batch_data['required_data']['batch'])) {
                }
                $batch = Bus::findBatch($batch_data['required_data']['batch']);
                if (isset($batch)) {
                    $batch->add($chunks_jobs);
                }
            }

            return $success_callback("success");
        } catch (\Exception $e) {
            Log::error("Issue found in products_bulk_assign with ERROR :" . $e->getMessage());
            return $error_callback($e->getMessage());
        }
    }


    function send_notification_for_bulk_assign($data): void
    {
        Log::debug("BulkAssign Queue for user " . $data['required_data']['user']->id);
        $body_message = "Your bulk assign queue processed successfully. Please wait while if processing is in under progress.";
        $url = url(route('products.index'));
        $details = [
            'subject' => 'Bulk Assign Queue.',
            'greeting' => 'Hi ' . $data['required_data']['user']->fname,
            'body' => $body_message,
            'thanks' => 'Thank you for using ' . request()->getHttpHost(),
            'actionText' => 'View',
            'actionURL' => $url,
            'user_id' => $data['required_data']['user']->id,
            'organization_id' => $data['required_data']['organization_id'],
            'batch' => $data['required_data']['batch']
        ];

        $notifier = new ApimioNotification($details);

        //un-comment if you want to stop notification in email
        //$notifier->only_db_notify(true);
        $data['required_data']['user']->notify($notifier);
    }


    public function set_products_chunk_attributes($products)
    {
        $assign_flag = $this->data['required_data']['assign_flag'];
        $selected_version_id = $this->data['required_data']['version'];
        $organization_id = $this->data['required_data']['organization_id'];

        try {
            foreach ($products as $product) {
                $product_obj = new Product();
                $product = $product_obj->product_fetch($product);
                if (isset($this->data['attributes'])) {
                    foreach ($this->data['attributes'] as $attribute_key => $attribute_values) {

                        try {
                            // categories
                            if ($attribute_key == "categories") {
                                $attached_categories = $product->categories->pluck('id')->toArray();

                                if ($assign_flag) {
                                    $Ids = array_diff($attribute_values, $attached_categories);
                                    $product->categories()->attach($Ids);
                                } else {
                                    $Ids = array_intersect($attribute_values, $attached_categories);
                                    $product->categories()->detach($Ids);
                                }
                            }

                            // brands
                            if ($attribute_key == "brands") {
                                $attached_brands = $product->brands->pluck('id')->toArray();

                                if ($assign_flag) {
                                    $Ids = array_diff($attribute_values, $attached_brands);
                                    $product->brands()->attach($Ids);
                                } else {
                                    $Ids = array_intersect($attribute_values, $attached_brands);
                                    $product->brands()->detach($Ids);
                                }
                            }


                            // vendors
                            if ($attribute_key == "vendors") {
                                $attached_vendors = $product->inviteVendor->pluck('id')->toArray();

                                if ($assign_flag) {
                                    $Ids = array_diff($attribute_values, $attached_vendors);
                                    $product->inviteVendor()->attach($Ids);
                                } else {
                                    $Ids = array_intersect($attribute_values, $attached_vendors);
                                    $product->inviteVendor()->detach($Ids);
                                }
                            }


                            // store
                            if ($attribute_key == "stores") {
                                $attached_channels = $product->channels->pluck('id')->toArray();
                                if ($assign_flag) {
                                    $Ids = array_diff($attribute_values, $attached_channels);
                                    $product->channels()->attach($Ids);
                                    (new Product())->UpdateOrCreateInventory($Ids, $product);

                                } else {
                                    $Ids = array_intersect($attribute_values, $attached_channels);
                                    $product->channels()->detach($Ids);
                                }
                            }


                            //attribute_sets //tags //weight
                            $version = $product->versions->where('id', $selected_version_id)->first();
                            if (isset($version->families)) {
                                //attribute_sets
                                if ($attribute_key == "attribute_sets") {
                                    $attached_attribute_sets = $version->families->pluck('id')->toArray();
                                    $Ids = null;
                                    $action = null;
                                    if ($assign_flag) {
                                        $Ids = array_diff($attribute_values, $attached_attribute_sets);
                                        $action = 'save';
                                    } else {
                                        $Ids = array_intersect($attribute_values, $attached_attribute_sets);
                                        $action = 'delete';
                                    }
                                    foreach ($Ids as $family_id) {
                                        $family_obj = Family::with('attributes')->where('organization_id', $organization_id)->where('id', $family_id)->get()->first();
                                        if ($family_obj) {
                                            if (count($family_obj->attributes) > 0) {
                                                $attribute_obj = $family_obj->attributes->first();
                                                $attribute_value_data = [
                                                    'attribute_id' => $attribute_obj->id,
                                                    'family_id' => $family_obj->id,
                                                    'version_id' => $selected_version_id,
                                                    'product_id' => $product->id,
                                                    'attribute_family_id' => $attribute_obj->pivotId,
                                                    'product_version_id' => $version->pivotId,
                                                    'value' => "",
                                                ];
                                                $this->attribute_family_product_version($attribute_value_data, $action);
                                            } else {
                                                Log::error("No attribute attached with selected family ID " . $family_id);
                                            }
                                        } else {
                                            Log::error("No family found on the given id : " . $family_id);
                                        }
                                    }
                                }


                                //tags
                                if ($attribute_key == "tags") {

                                    $seo_family = $version->families->where('name', 'SEO')->where('organization_id', $organization_id)->first();
                                    $seo_tags_attribute = $seo_family->attributes->where('handle', 'seo_keyword')->first();
                                    $attached_tags = $seo_tags_attribute->value->first();
                                    if (!is_null($attached_tags)) {
                                        $attached_tags_values = ($attached_tags->value != null) ? explode(',', $attached_tags->value) : [];
                                        $Ids = null;
                                        if ($assign_flag) {
                                            $Ids = array_diff(explode(',', $attribute_values), $attached_tags_values);
                                            $Ids = array_merge($Ids, $attached_tags_values);
                                        } else {
                                            $Ids = array_diff($attached_tags_values, explode(',', $attribute_values));
                                        }
                                        $attached_tags->value = implode(',', $Ids);
                                        $attached_tags->save();
                                    } else {
                                        if ($assign_flag) {
                                            $attribute_value_data = [
                                                'attribute_id' => $seo_tags_attribute->id,
                                                'family_id' => $seo_family->id,
                                                'version_id' => $selected_version_id,
                                                'product_id' => $product->id,
                                                'attribute_family_id' => $seo_tags_attribute->pivotId,
                                                'product_version_id' => $version->pivotId,
                                                'value' => $attribute_values,
                                            ];
                                            $this->attribute_family_product_version($attribute_value_data, 'save');
                                        }
                                    }
                                }

                                // weight, price, track_inventory, continue_selling
                                if (in_array($attribute_key, ['weight', 'price', 'track_quantity', 'continue_selling'])) {
                                    if ($attribute_values != null) {
                                        if (!$product->variants->isEmpty()) {
                                            $variants = $product->variants->where('version_id', $selected_version_id);
                                            foreach ($variants as $variant) {
                                                if ($assign_flag) {
                                                    if (preg_match("/[+\-*\/]/", $attribute_values)) {
                                                        // Ensure the attribute exists and is not null, set to 0 if null
                                                        $attribute_value = $variant->{$attribute_key} ?? 0;
                                                        $converted_value = $this->find_calculate_value($attribute_values, $attribute_value);
                                                    } else {
                                                        $converted_value = $attribute_values;
                                                    }

                                                } else {
                                                    $converted_value = 0;
                                                }

                                                // attaching or detaching track_quantity and continue_selling
                                                if (in_array($attribute_key, ['track_quantity', 'continue_selling'])) {
                                                    $settings = $variant->settings;

                                                    $setting_array = [
                                                        $attribute_key => $attribute_values
                                                    ];
                                                    if (!$assign_flag) {
                                                        $setting_array = [
                                                            $attribute_key => 0
                                                        ];
                                                    }
                                                    if ($variant->settings()->first() !== null) {
                                                        $variant->settings()->update($setting_array);
                                                    } else {
                                                        $variant->settings()->create($setting_array);
                                                    }
                                                }

                                                if (isset($converted_value) && !in_array($attribute_key, ['track_quantity', 'continue_selling'])) {
                                                    $variant->{$attribute_key} = $converted_value;
                                                    $variant->save();
                                                }
                                            }
                                        }
                                    }
                                }

                            } else {
                                Log::error("Version not found or version families not set.");
                            }
                        } catch (\Exception $e) {
                            throw new \Exception("Issue found in attribute assign loop for :" . $attribute_key . "ERROR :" . $e->getMessage());
                        }
                    }
                }

                // used for product update
                if ($product) {
                    event(new CalculateScore($product, isset($selected_version_id) ? [[$selected_version_id]] : []));
                    event(new ChannelUpdateStatusEvent(
                        product: $product,
                        isUpdated: true
                    ));
                }

            }
        } catch (\Exception $e) {
            throw new \Exception("Issue found in bulk assign attribute to product chunks with ERROR :" . $e->getMessage());
        }
    }


    /**
     * @param $attribute_value_data
     * @param $action
     * @return void
     */
    public function attribute_family_product_version($attribute_value_data, $action): void
    {
        if ($action == "save") {
            $attribute_family_product_version = new AttributeFamilyProductVersion();
            $attribute_family_product_version->attribute_id = $attribute_value_data['attribute_id'];
            $attribute_family_product_version->family_id = $attribute_value_data['family_id'];
            $attribute_family_product_version->version_id = $attribute_value_data['version_id'];
            $attribute_family_product_version->product_id = $attribute_value_data['product_id'];
            $attribute_family_product_version->attribute_family_id = $attribute_value_data['attribute_family_id'];
            $attribute_family_product_version->product_version_id = $attribute_value_data['product_version_id'];
            $attribute_family_product_version->value = $attribute_value_data['value'];
            $attribute_family_product_version->save();
        } else if ($action == "delete") {
            AttributeFamilyProductVersion::where('product_id', $attribute_value_data['product_id'])
                ->where('family_id', $attribute_value_data['family_id'])
                ->where('version_id', $attribute_value_data['version_id'])
                ->where('product_version_id', $attribute_value_data['product_version_id'])
                ->delete();
        }
    }















    // BULK EDIT APIMIO PRODUCTS

    /**
     * Get the default attributes for bulk editing.
     *
     * @return array The default attributes.
     */
    public function default_attributes()
    {
        $brands = Brand::all()->toArray();
        $vendors = Invite::all()->toArray();
        $categories = Category::all()->toArray();
        $stores = Channel::all()->toArray();


        return [
            "name" => "Default",
            "attributes" => [
                [
                    'name' => 'Product Identifier',
                    'handle' => 'handle',
                    'attribute_type_id' => 1,
                ],
                [
                    'name' => 'Brand',
                    'handle' => 'brand',
                    'attribute_type_id' => 'b14',
                    'sequence' => 4,
                    'attribute_options' => $brands ?? [],

                ],
                [
                    'name' => 'Vendor',
                    'handle' => 'vendor',
                    'attribute_type_id' => 'v14',
                    'sequence' => 5,
                    'attribute_options' => $vendors ?? [],

                ],
                [
                    'name' => 'Category',
                    'handle' => 'category',
                    'attribute_type_id' => 'c14',
                    'attribute_options' => $categories ?? [],
                ],
                [
                    'name' => 'Store',
                    'handle' => 'store',
                    'attribute_type_id' => 's14',
                    'attribute_options' => $stores ?? []
                ],
            ]
        ];
    }

    /**
     * Fetch bulk edit family attributes
     * @return array
     */
    public function fetch_bulk_edit_family_attributes(): array
    {
        try {
            $sequence_array = [
                "Default" => [
                    'handle' => 1,
                    'brand' => 4,
                    'vendor' => 5,
                    'category' => 6,
                    'store' => 7,
                    'variants' => 8,
                ],
                'General' => [
                    'product_name' => 2,
                    'description' => 3,
                ],
            ];

            $family_attributes[] = $this->default_attributes();
            $temp_family_attributes = Family::select('id', 'name')
                ->with(['attributes' => function ($query) {
                    $query->with('attribute_options')
                        ->select('attributes.id', 'attributes.attribute_type_id', 'attributes.name', 'attributes.handle', 'attributes.rules');
                }])
                ->where('organization_id', auth()->user()->organization_id)
                ->get()
                ->toArray();

            $family_attributes = array_merge($family_attributes, $temp_family_attributes);

            // Process attributes after fetching
            foreach ($family_attributes as &$family) {
                foreach ($family['attributes'] as &$attribute) {
                    if (isset($sequence_array[$family['name']][$attribute['handle']])) {
                        $attribute['sequence'] = $sequence_array[$family['name']][$attribute['handle']];
                    }
                }
            }
            return $family_attributes;
        } catch (\Exception $e) {
            // Handle the exception
            return ['error' => $e->getMessage()];
        }
    }


    /**
     * Default bulk filter
     *
     * @return array
     */
    public function bulk_filter()
    {
        $filter = Filter::where('type', 'bulk')->first();
        if ($filter) {
            $payload = $filter->payload;
        } else {
            $payload = [
                "Default" => [
                    "handle" => 'Product Identifier',
                    "category" => 'Category',
                    "store" => 'Store',
                ],
                "General" => [
                    "product_name" => 'Product Name',
                    "description" => 'Description',
                ]
            ];
        }

        return $payload;
    }


    // Function to group inventories by 'main_location_id' within variants and keep only one record per 'main_location_id'
    function groupAndFilterInventoriesByMainLocationId($variants)
    {
        foreach ($variants as &$variant) {
            $grouped = [];
            if ($variant['inventories']) {
                foreach ($variant['inventories'] as $inventory) {
                    $mainLocationId = $inventory['main_location_id'];
                    // Check if the key exists to ensure only the first record is kept
                    if (!isset($grouped[$mainLocationId])) {
                        $grouped[$mainLocationId] = $inventory;
                    }
                }
                $variant['inventories'] = $grouped;
            }
        }
        return $variants;
    }


    /**
     * Fetches bulk edit APIMIO products.
     *
     * @param array $data The data for bulk edit.
     * @return array|\Illuminate\Http\JsonResponse
     */
    public function fetch_bulk_edit_apimio_products($data)
    {
        $product_obj = new Product();
        $family_attributes = $this->fetch_bulk_edit_family_attributes();
        $bulk_filter = $this->bulk_filter();

        if (isset($data['productIds'])) {


            $products = $product_obj->fetch(function ($all_products) use ($data) {
                if ($data['productIds'] != "all") {
                    $all_products = $all_products->whereIn('id', $data['productIds'] ?? []);
                }

                $all_products = $all_products->select('id', 'sku')->with([
                    'file',
                    'versions' => function ($query) use ($data) {
                        if (isset($data['version_id'])) {
                            $query->where('versions.id', $data['version_id']);
                        } else {
                            $query->take(1); // Retrieve only one version if no specific version is set.
                        }
                    },
                    'variants' => function ($query) use ($data) {
                        if (isset($data['version_id'])) {
                            $query->where('version_id', $data['version_id']);
                        }
                        $query->with(['inventories' => function ($query) {
                            $query->select('inventories.id', 'inventories.location_id', 'inventories.available_quantity', 'inventories.variant_id');
                            $query->with(['location' => function ($query) {
                                $query->select('locations.id', 'locations.name'); // Specify the table name
                            }]);
                        }]);

                        $query->with(['settings']);
                    },
                ])->orderBy('id' , 'DESC');
                return $all_products->paginate(15, ['*'], 'page', $data['target_page']);
            });



            // Iterate through each product to select specific attributes
            $convertedProducts = array_map(function ($product) {
                $final_product_fetch_array['Default'] = [
                    'handle' => $product->sku ?? null,
                    'brand' => isset($product->brands) ? $product->brands->toArray() : [],
                    'vendor' => isset($product->inviteVendor) ? $product->inviteVendor->toArray() : [],
                    'category' => isset($product->categories) ? $product->categories->toArray() : [],
                    'store' => isset($product->channels) ? $product->channels->toArray() : [],
                    'file' => isset($product->file) ? $product->file->toArray() : [],
                    'product_id' => $product->id,
                    'variants' => isset($product->variants) ? $product->variants->toArray() : [],
                ];

                if ($final_product_fetch_array['Default']['variants']) {
                    $final_product_fetch_array['Default']['variants'] = $this->groupAndFilterInventoriesByMainLocationId($final_product_fetch_array['Default']['variants']);
                }

                if ($product->has('versions')) {
                    $versions = $product->versions;
                    foreach ($versions as $version) {
                        if (isset($version->families)) {
                            $families = $version->families;
                            foreach ($families as $family) {
                                $final_product_fetch_array[$family->name] = array_reduce($family->attributes->toArray(), function ($carry, $attribute) {
                                    $carry[$attribute['handle']] = $attribute;
                                    return $carry;
                                }, []);
                            }
                        }
                    }
                }

                return $final_product_fetch_array;
            }, $products->items());

            return [
                'products' => $convertedProducts,
                'attributes' => $family_attributes,
                'filter_attributes' => $bulk_filter,
                'data_required' => [
                    'current_page' => $products->currentPage(),
                    'total_pages' => $products->lastPage(),
                    'productIds' => $data['productIds'],
                    'version_id' => $data['version_id'],
                    'target_page' => null,
                    'units' => UnitFacade::get_units(),
                    'default_unit' => auth()->user()?->user_organization()?->units ?? null,
                ]
            ];
        } else {
            return response()->json([
                'error' => 'Products are not selected.'
            ], 400);
        }

    }


    public function bulk_edit_products_save($data)
    {

        if (isset($data['products'])) {
            foreach ($data['products'] as $product_key => $product) {

                $product_obj = Product::findOrFail($product_key);
                $product_version = $product_obj->versions()->where('version_id', $data['data_required']['version_id'])->first();


                foreach ($product as $family_name => $family) {

                    //if default family then sync the data according to relation wise
                    if ($family_name == "Default") {
                        foreach ($family as $attribute_key => $attribute) {
                            $attribute = array_filter($attribute);
                            if ($attribute_key == "brand") {
                                $product_obj->brands()->sync($attribute);
                            }else if ($attribute_key == "vendor") {
                                $product_obj->inviteVendor()->sync($attribute);
                            } else if ($attribute_key == "category") {
                                $product_obj->categories()->sync($attribute);
                            } else if ($attribute_key == "store") {
                                $product_obj->channels()->sync($attribute);
                            } else if ($attribute_key == "variants") {
                                foreach ($attribute as $variant) {
                                    $inventories = $variant['inventories'] ?? [];
                                    $settings = $variant['settings'] ?? [];
                                    unset($variant['inventories'], $variant['settings']);
                                    $variant_obj = $product_obj->variants()
                                        ->where('version_id', $data['data_required']['version_id'])
                                        ->where('id', $variant['id'])
                                        ->first();
                                    $variant_obj?->update($variant);

                                    if ($inventories) {
                                        foreach ($inventories as $inventory) {
                                            $inventory_obj = $variant_obj->inventories()->where('id', $inventory['id'])->first();
                                            if (isset($inventory_obj)) {
                                                $inventory_obj->available_quantity = $inventory['available_quantity'] ?? 0;
                                                $inventory_obj->save();
                                            }
                                        }
                                    }



                                    if ($variant_obj->settings()->first() !== null) {
                                        $variant_obj->settings()->update($settings);
                                    } else {
                                        $variant_obj->settings()->create($settings);
                                    }



//                                    if ($settings) {
//                                        $product_setting_obj = ProductVariantSetting::find($settings['id'] ?? null);
//
//                                        if (!$product_setting_obj){
//
//                                        }
//
//
//                                        if ($product_setting_obj) {
//                                            if (isset($settings['track_quantity'])) {
//                                                $product_setting_obj->track_quantity = $settings['track_quantity'];
//                                            }
//                                            if (isset($settings['continue_selling'])) {
//                                                $product_setting_obj->continue_selling = $settings['continue_selling'];
//                                            }
//                                            $product_setting_obj->save();
//                                        }
//                                    }
                                }
                            }
                        }

                        continue;
                    }

                    foreach ($family as $attribute_key => $attribute) {

                        $attribute_family = AttributeFamily::findOrFail($attribute_key);

                        //delete the previous data
                        AttributeFamilyProductVersion::query()
                            ->where('product_version_id', $product_version->pivotId ?? null)
                            ->where('attribute_family_id', $attribute_key ?? null)
                            ->delete();

                        // Define the additional keys and values
                        $additionalKeysValues = [
                            "family_id" => $attribute_family->family_id ?? null,
                            "attribute_id" => $attribute_family->attribute_id ?? null,
                            "version_id" => $product_version->id,
                            "product_id" => $product_obj->id ?? null,
                            "attribute_family_id" => $attribute_key,
                            "product_version_id" => $product_version->pivotId ?? null,
                        ];

                        if (array_key_exists('value' , $attribute)) {
                            $attribute = [$attribute];
                        }


                        $attribute = array_map(function ($subArray) use ($additionalKeysValues) {
                            return array_merge($subArray, $additionalKeysValues);
                        }, $attribute);


                        foreach ($attribute as $attribute_value) {
                            AttributeFamilyProductVersion::create($attribute_value);
                        }


                    }
                }

                if ($product_obj) {
                    event(new CalculateScore($product_obj, isset($data['data_required']['version_id']) ? [[$data['data_required']['version_id']]] : []));
                    event(new ChannelUpdateStatusEvent(
                        product: $product_obj,
                        isUpdated: true
                    ));
                }


            }
        }

        if (isset($data['data_required']['target_page'])) {
            return $this->fetch_bulk_edit_apimio_products($data['data_required']);
        } else {
            return response()->json([
                'success' => 'Products are updated successfully.'
            ]);
        }
    }
}
