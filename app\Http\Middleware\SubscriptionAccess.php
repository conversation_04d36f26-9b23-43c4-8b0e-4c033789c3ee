<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;

class SubscriptionAccess
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @param string $module
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $module)
    {
        if (Gate::denies('SubscriptionAccess', $module)) {
            // Redirect or show an error if access is denied
            abort(403, 'You do not have access to this module.');
        }

        return $next($request);
    }
}
