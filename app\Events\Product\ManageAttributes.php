<?php

namespace App\Events\Product;

use App\Models\Product\Product;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ManageAttributes
{

    public $version = null;

    /**
     * Create a new event instance.
     *
     * @param Product $product
     * @param array $data
     * @param object $version
     */
    public function __construct(Public Product $product,public array $data, $version)
    {
        $this->version = $product->versions()->where('version_id',$version)->first();

    }
    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
