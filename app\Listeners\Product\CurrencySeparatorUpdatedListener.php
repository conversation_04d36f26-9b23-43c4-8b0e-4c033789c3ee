<?php

namespace App\Listeners\Product;

use App\Events\Product\CurrencySeparatorUpdated;

class CurrencySeparatorUpdatedListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\Product\CurrencySeparatorUpdated  $event
     * @return void
     */
    public function handle(CurrencySeparatorUpdated $event)
    {
        //
    }
}
