<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin\Admin;
use App\Models\Channel\Channel;
use App\Models\Invite\Invite;
use App\Models\Organization\File;
use App\Models\Organization\Organization;
use App\Models\Organization\OrganizationUser;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Traits\Organization\CountTrait;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;


class AdminController extends Controller
{
    use CountTrait;
    public function dashboard(Request $request)
    {
        $data = array();
        $data['users'] = User::query();
        if ($request->login_method == 'google')
        {
            $data['users'] =  $data['users']->whereNotNull('google_id');
        }
        elseif ($request->login_method == 'shopify')
        {
            $data['users'] =  $data['users']->whereNotNull('shopify_shop_id');
        }
        elseif ($request->login_method == 'verified')
        {
            $data['users'] = $data['users']->whereNotNull('email_verified_at');
        }
        elseif ($request->login_method == 'non-verified')
        {
            $data['users'] = $data['users']->whereNull('email_verified_at');
        }

        if ($request->filled('q'))
        {
            $data['users'] = $data['users']->where(function ($query) use($request) {
                return $query->where('email','like', '%' . $request->get('q') . '%')
                    ->orWhere('fname','like', '%' . $request->get('q') . '%');
            });
        }

        if ($request->filled('r'))
        {
            $data['users'] = $data['users']->where(function ($query) use ($request) {
                return  $query->where('email','not like', '%' . $request->get('r') . '%')
                    ->orWhere('fname',' not like', '%' . $request->get('r') . '%');
            });

        }
        $data['users'] = $data['users']->orderBy('id','desc')->paginate(50);

        foreach ($data['users'] as $user) {
            $user->organizations_count = OrganizationUser::where('user_id',$user->id)->count();
            $organization_ids = OrganizationUser::where('user_id',$user->id)->pluck('organization_id')->toArray();
            $user->login_type = isset($user->google_id) ? 'Google' : (isset($user->shopify_shop_id) ? 'Shopify' : 'Manual');
            foreach ($organization_ids as $organization_id) {
                $organization = Organization::where('id',$organization_id)->first();
                if ($organization) {
                    $user->products_count += $organization->count_products();
                }
            }
        }
        return view('admin.dashboard',compact('data'));
    }
    public function count_all_data($id)
    {
        $organization = Organization::where('id',$id)->first();
        $data['channel_count'] = Channel::where('organization_id',$organization->id)->count();

    }
    public function deleteUser($id) {
        DB::beginTransaction();
        try {
            $user =  User::where('id',$id)->findOrFail($id);
            $organization_users = OrganizationUser::where('user_id',$user->id)->get();
            foreach ($organization_users as $organization_user) {
                $organizations = Organization::where('id',$organization_user->organization_id)->get();
                foreach ($organizations as $organization) {
                    Product::where('organization_id',$organization->id)->delete();
                    $subscription_id = DB::table('subscriptions')->where('organization_id',$organization->id)->value('id');
                    DB::table('subscription_items')->where('subscription_id',$subscription_id)->delete();

                   //Deleting files from the directory
                    $photos = File::withoutGlobalScope('organization_id')->where('organization_id',$organization->id)->get();
                    foreach ($photos as $photo) {
                        $path_parts = pathinfo($photo->link);
                        $file = $path_parts['basename'];
                        if(Storage::exists('/public/gallery/'.$file)){
                            Storage::delete('/public/gallery/'.$file);
                        }
                    }
                    $organization->delete();
                }
            }
            $user->delete();
            DB::commit();
            return redirect()->back()->withSuccess('User deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withErrors(['main',$e->getMessage()]);
        }

    }

    public function filterUsers(Request $request)
    {
        if ($request->login_method == 'google') {
            $data['users'] = User::where('google_id', '!=', null)->get();
        } elseif ($request->login_method == 'shopify') {
            $data['users'] = User::where('shopify_shop_id', '!=', null)->get();
        } else {
            $data['users'] = User::where('shopify_shop_id', null)->where('google_id', null)->get();
        }
        return view('admin.dashboard',compact('data'));
    }

    public function switchAccount($id)
    {
        try {
            $user = User::findorFail($id);
            Auth::login($user);
            return redirect(route('dashboard'))->withSuccess('Switched to ' . ucfirst($user->fname) . ' successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['main' => $e->getMessage()]);
        };
    }

    public function showOrganizations($id)
    {
        $admin = new Admin();
        $organizations =  $admin->show_organizations($id);
        return view('admin.organization.view',compact('organizations'));
    }

    public function deleteOrganizations($id)
    {
        $admin = new Admin();
        $admin->deleteOrganization($id);
        return redirect()->back()->withSuccess('Organization deleted successfully');
    }

    public function showInvitations($id)
    {
        $invitations = new Admin();
        $all_invitations = $invitations->all_invitations($id);
        return view('admin.invitations.view',compact('all_invitations'));

    }

    /**
     * update the seo family attribute from seo-keyword to seo_url
     * @return string
     */

    public function changeAttributeName(): string
    {
        $org = \App\Models\Organization\Organization::all();
        foreach($org as $o){
            \App\Models\Product\Attribute::query()->withoutGlobalScopes()
                ->where(['organization_id'=>$o->id,'handle'=>'seo_keyword'])
                ->update([
                    'handle'=>'seo_url',
                    'name'=>'SEO URL'
                ]);
        }
        return 'done';
    }
}
