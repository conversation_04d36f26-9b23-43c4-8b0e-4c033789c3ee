<?php

namespace App\Http\Controllers\Product;

use Apimio\MappingConnectorPackage\Http\Controllers\MappingFieldController;
use Apimio\MappingConnectorPackage\models\Template;
use App\Classes\Export\ExportToCsv;
use App\Classes\Mapping\Conversion;
use App\Http\Controllers\Controller;
use App\Jobs\ExportProducts;
use App\Models\Channel\Channel;
use App\Models\Product\Attribute;
use App\Models\Product\Family;
use App\Models\Product\Version;
use App\Traits\AWSSetting;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;

class ExportController extends Controller
{
    use AWSSetting;
    public function __construct()
    {
        $this->middleware(['auth', 'verified', "activeOrganization"]);
    }

    public function export_step1()
    {
        return view('products.export.exportOne');
    }

    /**
     * fetch apimio heading, attributes and display export step 2
     *
     * @param Request $request
     *
     */
    public function export_step2(Request $request)
    {
        // create array data for export mapping
        $mapping = new MappingFieldController();




        //testing script
        // $product_obj = new Product();
        // $product_first = $product_obj->fetch(function ($product) {
        //     return $product->limit(1)->first();
        // });
        // $product = $product_obj->product_fetch($product_first);
        // testing script end



        // this method is for apimio products data
        // only send families for fetching heading of attributes
        // $data['families'] = Family::with('attributes')->get();
        // $data['left_array'] = $mapping->fetch_apimio_products(null, true, $data);

        $data['left_array'] = Template::apimio_mapping_array([], false);
        $data['right_array'] = [
            "array_name" => "Export",
            "nodes" => []
        ];


        // $all_catalogs = Channel::all();
        // $location_array = array();
        // foreach ($all_catalogs as $catalog) {
        //     $all_locations = $catalog->locations()->get();
        //     foreach ($all_locations as $location) {
        //         $location_array[$catalog->id][$location->id] = $location->name;
        //     }
        // }


        $filters = [];

        if($request->has('filter')){
            $filters = $request->filter ?? [];
        }


        $data_required = [
            'organization_id' => auth()->user()->organization_id,
            'output_type' => 'export',
            'template_method_type' => 'export', //its only contain ('import','export','shopify','clone','other')
            'sync' => false,
            'redirect_url_route' => 'products.export', //it should be post method
            'versions' => Conversion::fetch_all_version_in_array(),
            'catalogs' => Conversion::fetch_all_catalog_in_array(),
            // 'locations' => $location_array,
            'filter_query' => $filters ?? [],

            //testing product
            // 'products' => $product,

        ];

        return $mapping->mapping_view($data['left_array'], $data['right_array'], $data_required);
    }



    /**
     * fetch apimio heading, attributes and display export step 3
     *
     * @param Request $request
     *
     */
    public function export_step3(Request $request)
    {
        try {
            $filter_query = $request['filter_query'];
            $heading_attributes = Family::with(['attributes'])->get();
            $apimio_attributes = collect([
                'sku' => 'SKU',
                'files' => 'Media',
                'vendors' => 'Vendor',
                'brands' => 'Brand',
                'categories' => 'Category'
            ]);

            $variant_attributes = Attribute::where('attribute_type_id', 4)->get();
            if (!isset($variant_attributes)) {
                $variant_attributes = "";
            }


            if (isset($request->template)) {
                if ($request->template == 'shopify_template') {
                    $export_csv_obj = new ExportToCsv();
                    $template_obj = $export_csv_obj->shopify_default_template(
                        function ($error) {
                            return $error;
                        },
                        function ($success) {
                            return $success;
                        }
                    );
                } elseif ($request->template == 'magento_template') {
                    $export_csv_obj = new ExportToCsv();
                    $template_obj = $export_csv_obj->magento_default_template(
                        function ($error) {
                            return $error;
                        },
                        function ($success) {
                            return $success;
                        }
                    );
                } else {
                    $template_obj = Template::find($request->template);
                }
            }





            if (isset($request->create_template)) {
                $template_obj = null;
            }


            $template_attributes = array();
            $template = "";
            if (!empty($template_obj)) {
                $template = json_decode($template_obj->payload, true);
                $version = Version::find($template_obj->version_id);
                $channel = Channel::find($template_obj->channel_id);
                $template_attributes = [
                    'version_id' => isset($version->id) ? $version->id : null,
                    'channel_id' => isset($channel->id) ? $channel->id : null,
                    'name' => isset($template_obj->name) ? $template_obj->name : null,
                    'id' => isset($template_obj->id) ? $template_obj->id : null,
                    'variant_type' => isset($template['temp_create']) ? $template['temp_create'] : @$template['variant']['variant_type'],
                    'last_used' => $template_obj->updated_at
                ];
            }

            return View::make(
                'products.export.exportThree',
                compact('heading_attributes', 'apimio_attributes', 'template_attributes', 'template', 'filter_query', 'variant_attributes')
            );
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->withErrors(['main' => ['Something went wrong. Please try again']]);
        }
    }



    /**
     * fetch apimio mapping rows for csv export
     *
     *
     *
     */
    public function export_csv(Request $request)
    {
        try {

            $validator = Validator::make($request->all(), [
                // 'file_path' => 'required',
                'nodes' => 'required',
                'organization_id' => 'required',
            ]);
            if($validator->fails()){
                return back()->withErrors($validator)->withInput();
            }

            // if (!file_exists(Storage::disk('public')->path($request->file_path))) {
            //     $validator->errors()->add('file_path', 'Your product data json file is not exist.');
            //     return $validator->errors();
            // }

            $data = session()->get('data');
            $data['request_data'] = $request->all();
            $data['data_required']['export_type'] = $request->export_type ?? null;

            // $location = Location::findOrfail($data['request_data']['location'] ?? null);

            $version_name = Version::find($data['request_data']['version']);
            $filename = "";
            if (isset($data['data_required']['export_type']) && $data['data_required']['export_type'] != null) {
                $filename .= $data['data_required']['export_type'] . "_";
            }
            $filename .= $version_name->name ?? "without_version";
            // $filename .= "_" . $location->name ?? "without_location";
            $filename .= "_products_" . Carbon::now();
            $filename = Str::slug($filename, '_') . ".xlsx";
            $custom_request_array = [
                'version_name' => $version_name->name ?? null,
                'filename' => 'temp_files/' . $filename,
                'user' => Auth::user(),
                'organization_id' => auth()->user()->organization_id,
            ];



//            if (isset($data['request_data']['temp_status']) && $data['request_data']['temp_status'] == "on") {
//                $template_obj = new Template();
//                $template = $template_obj->set_data($data['request_data'])->store(function ($tem_obj) {
//                    return $tem_obj;
//                }, function ($error) {
//                    return $error;
//                });
//            }

            $template_obj = new MappingFieldController();
            $template_response = $template_obj->save_template($request);
            $response_data = $template_response->getData();
            if ($template_response->getStatusCode() == 200) {
                if ($response_data->status != 'success') {
                    return response()->json($response_data, 500);
                }
            } else {
                return response()->json($response_data, 500);
            }



            //////////////////////////////////////below code is for without queue testing do not remove this code//////////////////////////////////
//                                $exportcsv = new ExportToCsv();
//                                $csv_array_heading = "";
//                                $final_products_collection = $exportcsv->export_to_csv(array_merge($data, $custom_request_array),
//                                    function ($error){
//                                        Log::error($error);
//                                    },
//                                    function ($success,$heading) use (&$csv_array_heading){
//                                        $csv_array_heading = $heading;
//                                        return $success;
//                                    });
//                                dd($final_products_collection,$csv_array_heading);



            if (!isset($data['request_data']['save_template'])) {
                $data = array_merge($data, $custom_request_array);
                if (env('CUSTOM_QUEUE_WORKER') == 'local') {
                    ExportProducts::dispatch($data);
                }
                else{
                    $jobs = [];
                    $jobs[] = new ExportProducts($data);
                    $this->dispatchJobsToFifoQueue($jobs, $data['organization_id'] . '-main', $data['organization_id']);
                }
            }

            if (isset($request->save_template)) {
                $msg = 'Template saved successfully.';
            } else {
                $msg = 'Your Export CSV is on its way. Please check your email to download your file.';
            }
            $responseData = [
                'message' => $msg,
                'data' => $data, // Add your data here
            ];
            return response()->json($responseData, 200);
        } catch (\Exception $e) {
            Log::error("Main export products controller error  : " . $e);
            return Redirect::route("products.index")->withErrors("Something went wrong please try again later.");
        }
    }
}
