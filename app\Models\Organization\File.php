<?php

namespace App\Models\Organization;

use App\Jobs\saveFile;
use App\Models\Channel\ChannelFileProduct;
use App\Models\Interfaces\ModelInterface;
use App\Models\Product\FileProduct;
use App\Models\Product\Product;
use App\Scopes\UserSpecificScope;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File as F;
use Illuminate\Support\Facades\Validator;
use App\Rules\ImageDimension;
use DB;

class File extends Model
{

    private const
        MAX_IMG_SIZE_ALLOWED    = 12,               // in kilobytes
        MAX_VIDEO_SIZE_ALLOWED  = 12,               // in kilobytes
        MAX_FILE_SIZE_ALLOWED   = 12;               // in kilobytes

    private
        $files                  = null,
        $urls                   = null,
        $allowed_image_ext      = ['jpg', 'jpeg', 'png', 'gif'],
        $allowed_video_ext      = ['mp4', 'wmv'],
        $allowed_file_ext       = ['txt', 'pdf', 'csv', 'xlsx'];

    protected $fillable = ['organization_id','name','link','ext','type','width','height','size','should_sync'];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [];

    /**
     * Constructor for storing data and applying post logic
     * to organization structure.
     *
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public static function boot()
    {
        parent::boot();

        static::addGlobalScope('organization_id', function (Builder $builder) {
            if(Auth::check()) {
                $builder->where("organization_id", Auth::user()->organization_id);
            }
        });

        static::creating(function($model)
        {
            if (Auth::check()) {
                $model->organization_id = Auth::user()->organization_id??Auth::user()->id;
            }
        });

        static::deleted(function($photo){
            $path_parts = pathinfo($photo->link);

            $file = $path_parts['basename'];
            if(Storage::exists('/public/gallery/'.$file)){
                Storage::delete('/public/gallery/'.$file);
            }
            if(Storage::disk('s3')->exists('/images/'.$file)) {
                Storage::disk('s3')->delete('images/'.$file);
            }
        });
    }


    /**
     * Set data attribute.
     *
     *
     * @param array
     * @return $this
     */
    public function set_files(array $files)
    {
        $this->files = $files;
        return $this;
    }

    /**
     * Get data attribute.
     *
     * @return $this
     */
    public function get_files() {
        return $this->files;
    }

    /**
     * Set data attribute.
     *
     *
     * @param array
     * @return $this
     */
    public function set_downloadable_files(array $urls)
    {
        $this->urls = $urls;
        return $this;
    }

    /**
     * Set organization id.
     *
     *
     * @param int
     * @return void
     */
    public function set_organization(int $organization_id)
    {
        $this->organization_id = $organization_id;
        return $this;
    }

    /**
     * Get data attribute.
     *
     * @return $this
     */
    public function get_downloadable_files() {
        return $this->urls;
    }

    public function rules() {
        if ($this->files['type'] == 'file') {
            return [
                'file.*' => ['required', 'mimes:pdf,doc,csv,xlsx,txt', new ImageDimension()],
            ];
        } elseif ($this->files['type'] == 'product_image') {
            return [
                'file.*' => ['required', 'mimes:jpg,jpeg,webp,png,svg,git,bmp,ico', new ImageDimension()],
            ];
        } else {
            return [
                'file.*' => ['required', new ImageDimension()],
            ];
        }
    }

    public function validation() {
        $validator = Validator::make($this->files, $this->rules());
        return $validator;
    }

    public function store($error_callback, $success_callback)
    {
        $validator = $this->validation();

        if($validator->fails()) {
            return $error_callback($validator->errors());
        }

        foreach ($this->files['file'] as $file) {
            $data = $this->save_file($file);
            $product_id = $this->files['product_id'];
            $data->products()->attach($product_id,['uploaded_for'=>$this->files['type']]); 
        }
        return $success_callback($data->id);
    }

    public function download_through_url($url) {
        try {
            $contents = file_get_contents($url);
        }
        catch (\Exception $e) {
            return null;
        }
        $name = substr($url, strrpos($url, '/') + 1);
        $filesname = time() . "." . $name;
        $ext = explode(".", $name);

        Storage::disk('public')->put('/gallery/'.$filesname, $contents);
        $size1 = Storage::size('public/gallery/'.$filesname);
        $sizeInKb=round($size1 / 1024, 2);
        $file_extension = isset($ext[1]) ? $ext[1] : null;
        $obj = $this;
        if($this->organization_id)
            $obj->organization_id = $this->organization_id ;
        $obj->link =url('/storage/gallery/'.$filesname);
        $obj->ext = $file_extension;
        // TODO: get image width and height.
        //        $obj->width = $size[0];
//        $obj->height = $size[1];
        $obj->size = $sizeInKb;
        if (in_array($file_extension, $this->allowed_image_ext) == 'true') {
            $obj->type = 'img';
        } elseif (in_array($file_extension, $this->allowed_video_ext) == 'true') {
            $obj->type = 'video';
        } elseif (in_array($file_extension, $this->allowed_file_ext) == 'true') {
            $obj->type = 'file';
        }
        if($obj->save())
        {
            return $obj;
        }
        else
        {
            if(Storage::exists('/public/gallery/'.$filesname)){
                Storage::delete('/public/gallery/'.$filesname);
            }
        }
        return $obj;
    }

    // TODO: downloadable images functionality through url.
    public function save_file($file) {
        $filesname = time() .rand(10000000,99999999) ."_" . $file->getClientOriginalName();
        $file_extension = pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION);
        $size =  getimagesize($file);

        // TODO: s3 and public should be dynamic
        $image = Storage::disk('public')->put("/gallery/" . $filesname, F::get($file));
        // $url = Storage::disk(':s3')->url($filename );
        $size1 = Storage::size('public/gallery/'.$filesname);
        $sizeInKb=round($size1 / 1024, 2);
        $obj = $this;
        $obj->link =url('/storage/gallery/'.$filesname);
        $obj->ext = $file_extension;
        $obj->width = $size[0];
        $obj->height = $size[1];
        $obj->size = $sizeInKb;
        if (in_array($file_extension, $this->allowed_image_ext) == 'true') {
            $obj->type = 'img';
        } elseif (in_array($file_extension, $this->allowed_video_ext) == 'true') {
            $obj->type = 'video';
        } elseif (in_array($file_extension, $this->allowed_file_ext) == 'true') {
            $obj->type = 'file';
        }
        if($obj->save())
        {
            return $obj;
        }
        else
        {
            if(Storage::exists('/public/gallery/'.$filesname)){
                Storage::delete('/public/gallery/'.$filesname);
            }
        }
        return null;

    }

    public static function get_img_status($file)
    {
        $result = 0;

        // first rule is to have image dimension around 1080 x 1080
        if(($file->width > 950 && $file->width < 1100) && ($file->height > 950 && $file->height < 1100))
            $result += 1;

        // second rule is to have size less than 200kb
        if($file->size < 201)
            $result++;
        // third rule is to have extension as jpg
        if(strtolower($file->ext) == "jpg" || strtolower($file->ext) == "webp" )
            $result++;

        if($result == 3) {
            return "approve";
        }
        else if($result == 2) {
            return "warning";
        }
        else {
            return "error";
        }
    }

    public static function get_completeness_score($files) {
        $score = 0;
        if(is_iterable($files)) {
            foreach ($files as $file) {
                $status = self::get_img_status($file);
                if($status == "approve")
                    $score += 1;
                elseif($status == "warning")
                    $score += 0.5;
            }
            $score = (100*$score)/(count($files) < 1 ? 1 : count($files));
        }
        else {
            $status = self::get_img_status($files);
            if($status == "approve")
                $score += 1;
            elseif($status == "warning")
                $score += 0.5;

            $score = (100*$score)/1;
        }

        return round($score);
    }

    public function delete_file($id)
    {
        $file =  $this->where('id' ,$id)->first();
        if ($file)
        {
            $file->delete();
            return true;
        } else {
            return null;
        }
    }

    public function get_quality_stats($limit = 100) {
        $approve = 0;
        $warning = 0;
        $error = 0;

        $files = $this->where("type", "img")->limit($limit)->get();

        foreach ($files as $file) {
            $status = $this->get_img_status($file);
            if($status == "approve") {
                $approve++;
            }
            else if($status == "warning") {
                $warning++;
            }
            else if($status == "error") {
                $error++;
            }
        }

        $total = $approve + $warning + $error;

        if($total > 0) {
            return [
                "approve" => (int) round(($approve*100)/$total),
                "warning" => (int) round(($warning*100)/$total),
                "error" => (int) round(($error*100)/$total)
            ];
        }

        return [
            "approve" => $approve,
            "warning" => $warning,
            "error" => $error
        ];
    }


    //================== relationships -=======================//

    public function products()
    {
        return $this->belongsToMany(Product::class, 'file_product')->withPivot('uploaded_for')->withTimestamps();
    }

    public function channelFile()
    {
        return $this->hasOneThrough(ChannelFileProduct::class, FileProduct::class, 'file_id', 'file_product_id');
    }
}















