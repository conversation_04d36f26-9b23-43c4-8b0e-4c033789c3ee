<?php

namespace App\Http\Controllers\Api\BrandsPortal;

use Illuminate\Http\Request;
use Carbon\Carbon;
use App\Models\Product\Version;
use App\Models\Product\Product;
use App\Models\BrandsPortal;
use App\Models\Product\Template;
use App\Http\Controllers\Controller;
use App\Http\Controllers\ExportCsvApiRecordController;
use App\Http\Resources\PortalProductResource;
use App\Traits\Api\ApiResponseTrait;
use App\Traits\Api\BrandPortal\ApiResponseFilterTrait;
use Illuminate\Database\Eloquent\Builder;
use App\Notifications\ApimioNotification;
use Illuminate\Support\Str;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Apimio\MappingConnectorPackage\models\Template as ExportTemplate;
use App\Jobs\Export\GetProductIdsChunkArray;

class BrandsPortalProductsController extends Controller
{
    use ApiResponseTrait, ApiResponseFilterTrait;
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * list all products of brand portal
     */
    public function store(Request $request)
    {
        try {
            // Validate the request parameters
            $request->validate([
                'paginate' => 'integer|min:1|max:255',
                'page' => 'integer|min:1',
                'filters' => 'nullable|array',
            ]);
            $brandPortalId = urldecode(decrypt($request->get('id')));

            $products = Product::whereHas('channels', function (Builder $query) use ($brandPortalId) {
                $query->whereIn('channels.id', function ($subQuery) use ($brandPortalId) {
                    $subQuery->select('channel_id')
                        ->from('brands_portal_channel')
                        ->where('brands_portal_id', $brandPortalId);
                });
            });

            //apply filters
            $products = $this->filter($products, $request);

            // Get the paginated data
            $paginatedProducts = $products->paginate($request->get("paginate") ?? 100, ['*'], 'page', $request->get("page") ?? 1);

            // Prepare pagination details
            $pagination = $this->formatPagination($paginatedProducts);

            // Return the response with pagination details

            return response(
                [
                    'message' =>            'Brand portals retrieved successfully',
                    'brandPortals' =>   PortalProductResource::collection($paginatedProducts) ,
                    'pagination' => $pagination
                ]
            );
        } catch (\Exception $e) {
            return response(
                [
                    'message' => 'Failed to retrieve products',
                    'error' => $e->getMessage()
                ],
                $e->getCode()!=0? $e->getCode() : 500
            );
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        try {
            $product  = Product::findOrFail($id);
            return $this->successResponse('Product retrieved successfully', new PortalProductResource($product), 200);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve product', 404, $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
      //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }


    /**
     * Export the products
     */
    public function export(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'template_id' => 'required|array',
            'product_ids' => 'nullable|array',
            'filters' => 'nullable|array',
        ]);
        $brand_portal_id_encode = $request->get('brand_portal_id');
        $brand_portal_id = (int) urldecode(decrypt($brand_portal_id_encode));
        //export data
        $brand_portal = BrandsPortal::findOrFail($brand_portal_id);


        if (!($request->product_ids)) {
            //use filters to retireve products
            $all_products =  Product::withoutGlobalScopes()->select('id')->where('organization_id', $brand_portal->organization_id);
            $product_ids = $this->filter($all_products, $request)->get()->pluck('id')->toArray();
        } else {
            $product_ids = $request->product_ids;
        }
        $templates = Template::whereIn('id', $request->template_id)->get();

        $default_versions = $brand_portal->channels->first()->versions->pluck('name', 'id')->toArray();
        $product_obj = new Product();
        $data = $this->setDataArrayForExportPackage($brand_portal->organization_id, $brand_portal->store, $default_versions, $templates);

        //without jobs and queue for testing
        //     // Get the paginated data
        //     $chunk_products = $products->get();
        // }
        // $export_csv_obj = new ExportToCsv();
        // $export_csv_obj->get_products_chunk_array($chunk_products, $product_obj, $data);

        //create job that sends csv in email
        $this->setjobs($product_ids, $product_obj, $data, $request->email, $brand_portal_id_encode);

        // create record of export
        foreach ($request->template_id as $templateId) {
            $dataRecord = [
                'email' => $request->email,
                'brands_portal_id' => $brand_portal_id,
                'template_id' => $templateId,
            ];

            $exportRecordController = new ExportCsvApiRecordController();
            $request = new Request($dataRecord);
            $exportRecord = $exportRecordController->store($request);
        }
        return response(
            [
                'message' => 'Export started successfully to your email',
            ],
        );
    }

    //create job for all products and send email
    private function setjobs($productIds, $productObj, $data, $email, $hash)
    {
        $jobable = false;
        $totalVariantsLimit  = 30;
        $chunksJobs = [];

        Product::whereIn('id', $productIds)
            ->withCount('variants')
            ->chunk(15, function ($products) use ($data, $productObj, &$chunksJobs, &$jobable, $totalVariantsLimit) {
                $this->processProductChunk($products, $data, $productObj, $chunksJobs, $jobable, $totalVariantsLimit);
            });

        // Create a serializable notifiable object
        $notifiable = new \App\Classes\BrandPortal\EmailNotifiable($email);
        $data['user'] = $notifiable;
        //if job able is true then start batching
        if ($jobable) {
            $batch = Bus::batch($chunksJobs)->then(function (Batch $batch) use ($data, $hash) {
                info('Batch finished');
                // $this->send_email_for_export1($data);
                $filename = $data['filename'] ?? null;
                $url = route('public.download', ['filename' => $filename, 'portal' => $hash]);

                $details = [
                    'subject' => 'Your export CSV file is ready to download',
                    'greeting' => 'Hi ',
                    'body' =>
                    "Your ("
                        . $data['data_required']["output_type"]
                        . ") Export CSV"
                        . " with version <b>"
                        . "</b> is generated successfully"
                        . "<br><br>Please copy and paste the below URL into your web browser: <br><a href='" . $url . "'>" . $url . "</a><br>",
                    'thanks' => 'Thank you for using ' . request()->getHttpHost(),
                    'actionText' => 'Download CSV File',
                    'actionURL' => $url,
                ];

                $notifier = new ApimioNotification($details);
                $notifier->only_mail_notify(true);
                $data['user']->notify($notifier);
            })->dispatch();
        } else {
            return $this->errorResponse('No products found for export', 500);
        }
    }


    //create chunks of products
    private function processProductChunk($products, $data, $productObj, &$chunksJobs, &$jobable, $totalVariantsLimit)
    {
        $totalCount = 0;
        $tempProductIds = [];
        $totalChunksProductIds = [];

        foreach ($products as $product) {
            $totalCount += $product->variants_count;
            if ($totalCount > $totalVariantsLimit) {
                $totalChunksProductIds[] = $tempProductIds;
                $tempProductIds = [$product->id];
                $totalCount = $product->variants_count;
            } else {
                $tempProductIds[] = $product->id;
            }
        }

        if (!empty($tempProductIds)) {
            $totalChunksProductIds[] = $tempProductIds;
        }

        $this->enqueueJobsForProductIds($products, $totalChunksProductIds, $data, $productObj, $chunksJobs, $jobable);
    }

    // create jobs for each chunk of product ids
    private function enqueueJobsForProductIds($products, $totalChunksProductIds, $data, $productObj, &$chunksJobs, &$jobable)
    {
        foreach ($totalChunksProductIds as $productIds) {
            $chunkProducts = $products->whereIn('id', $productIds);
            if ($chunkProducts->isNotEmpty()) {
                $jobable = true;
                $chunksJobs[] = new GetProductIdsChunkArray([
                    'products' => $chunkProducts,
                    'product_obj' => $productObj,
                    'data' => $data,
                ]);
            }
        }
    }

    //set array for export package by nadir
    private function setDataArrayForExportPackage($organization_id, $stores, $default_version, $templates)
    {
        $data['left_array'] =

            $data['right_array'] = [
                "array_name" => "Export",
                "nodes" => []
            ];

        $template =  $templates->first();

        $data_required = [
            'organization_id' => $organization_id,
            'output_type' => 'export',
            'template_method_type' => 'export',
            'export_type' => $template->export_type ?? 'custom',
            'sync' => true,
            'template_id' => $template->id ?? null,
            'versions' => $default_version, //todo:: id=>name
            'catalogs' => $stores,

        ];
        $request_data = $this->fetch_template_in_array($templates->first());



        $data = [
            'left_array' => ExportTemplate::apimio_mapping_array([], false),
            'right_array' => [
                "array_name" => "Export",
                "nodes" => []
            ],
            'data_required' => $data_required,
            'request_data' =>  $request_data,
        ];

        //set file
        $filename = $this->setfilename($data);
        $data['filename'] = 'temp_files/' . $filename;
        return $data;
    }

    // fetch template in array
    function fetch_template_in_array($template_obj)
    {
        $template_attributes = array();
        if ($template_obj != null) {
            $template = json_decode($template_obj->payload, true);
            $template_attributes = [
                'organization_id' => isset($template_obj->organization_id) ? $template_obj->organization_id : null,
                'version' => isset($template_obj->version_id) ? json_decode($template_obj->version_id) : null,
                'catalog' => isset($template_obj->channel_id) ?  json_decode($template_obj->channel_id) : null,
                'temp_name' => isset($template_obj->name) ? $template_obj->name : null,
                'temp_id' => isset($template_obj->id) ? $template_obj->id : null,
                'export_type' => isset($template_obj->export_type) ? $template_obj->export_type : null,

                'template_method_type' => isset($template_obj->type) ? $template_obj->type : null,

                'nodes' => isset($template) ? $template : null
            ];
        }


        return $template_attributes;
    }

    //set unique xlsx file name acc to export type , version and date
    private function setfilename($data)
    {
        $filename = "";
        if (isset($data['data_required']['export_type']) && $data['data_required']['export_type'] != null) {
            $filename .= $data['data_required']['export_type'] . "_";
        }
        $version_name = Version::find($data['request_data']['version']);
        $filename .= $version_name->name ?? "without_version";
        $filename .= "_products_" . Carbon::now();
        $filename = Str::slug($filename, '_') . ".csv";
        return $filename;
    }
}
