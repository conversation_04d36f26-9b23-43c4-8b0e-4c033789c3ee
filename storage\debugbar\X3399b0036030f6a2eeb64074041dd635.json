{"__meta": {"id": "X3399b0036030f6a2eeb64074041dd635", "datetime": "2025-05-27 07:53:20", "utime": **********.779694, "method": "GET", "uri": "/products/import/apply-template?template_id=23", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.148817, "end": **********.779721, "duration": 0.630903959274292, "duration_str": "631ms", "measures": [{"label": "Booting", "start": **********.148817, "relative_start": 0, "end": **********.671939, "relative_end": **********.671939, "duration": 0.5231218338012695, "duration_str": "523ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.671952, "relative_start": 0.5231349468231201, "end": **********.779724, "relative_end": 2.86102294921875e-06, "duration": 0.1077718734741211, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 36634072, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x CSVMapping", "param_count": null, "params": [], "start": **********.779648, "type": "", "hash": "CSVMapping", "render_count": 1, "name_original": "CSVMapping"}]}, "route": {"uri": "GET products/import/apply-template", "middleware": "web, check_billing, auth, verified, activeOrganization", "controller": "App\\Http\\Controllers\\Product\\ImportController@apply_template", "namespace": "App\\Http\\Controllers\\Product", "prefix": "/products", "where": [], "as": "import.apply.template", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FImportController.php&line=401\" onclick=\"\">app/Http/Controllers/Product/ImportController.php:401-467</a>"}, "queries": {"nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.005840000000000001, "accumulated_duration_str": "5.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.719165, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 53.596}, {"sql": "select * from `organizations` where `organizations`.`id` = '11' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 3) limit 1", "type": "query", "params": [], "bindings": ["11", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "check_billing", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\BillingMiddleware.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 86}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 86}], "start": **********.7375958, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "check_billing:21", "source": {"index": 21, "namespace": "middleware", "name": "check_billing", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\BillingMiddleware.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FMiddleware%2FBillingMiddleware.php&line=21", "ajax": false, "filename": "BillingMiddleware.php", "line": "21"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 53.596, "width_percent": 13.185}, {"sql": "select count(*) as aggregate from `templates` where `id` = '23'", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 903}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 874}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 658}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 457}], "start": **********.7501788, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 66.781, "width_percent": 10.103}, {"sql": "select * from `templates` where `templates`.`id` = '23' and `organization_id` = '11' limit 1", "type": "query", "params": [], "bindings": ["23", "11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 418}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.754652, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ImportController.php:418", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 418}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FImportController.php&line=418", "ajax": false, "filename": "ImportController.php", "line": "418"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 76.884, "width_percent": 10.103}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 3 and `organizations`.`id` = '11' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 3) limit 1", "type": "query", "params": [], "bindings": [3, "11", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Providers\\AppServiceProvider.php", "line": 51}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.762403, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:51", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Providers\\AppServiceProvider.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FProviders%2FAppServiceProvider.php&line=51", "ajax": false, "filename": "AppServiceProvider.php", "line": "51"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 86.986, "width_percent": 13.014}]}, "models": {"data": {"App\\Models\\Organization\\Organization": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}, "App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Apimio\\MappingConnectorPackage\\models\\Template": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fmapping-fields-package%2Fsrc%2Fmodels%2FTemplate.php&line=1", "ajax": false, "filename": "Template.php", "line": "?"}}}, "count": 4, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/products/import/apply-template?template_id=22\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "password_hash_web": "$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq", "organization_id": "11", "data": "array:11 [\n  \"input_array\" => array:2 [\n    \"array_name\" => \"CSV\"\n    \"nodes\" => array:1 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:228 [\n          \"Categories\" => \"Categories\"\n          \"Collection label\" => \"Collection label\"\n          \"Product name\" => \"Product name\"\n          \"SKU\" => \"SKU\"\n          \"GTIN\" => \"GTIN\"\n          \"Color\" => \"Color\"\n          \"Material\" => \"Material\"\n          \"Multiple consumer\" => \"Multiple consumer\"\n          \"Multiple required\" => \"Multiple required\"\n          \"Colli amount\" => \"Colli amount\"\n          \"Adjustment options\" => \"Adjustment options\"\n          \"Anti slip\" => \"Anti slip\"\n          \"Armrest Height\" => \"Armrest Height\"\n          \"Armrest Height (Unit)\" => \"Armrest Height (Unit)\"\n          \"With armrests\" => \"With armrests\"\n          \"Width armrest\" => \"Width armrest\"\n          \"Width armrest (Unit)\" => \"Width armrest (Unit)\"\n          \"Adjustable backrest\" => \"Adjustable backrest\"\n          \"With backrests\" => \"With backrests\"\n          \"[baseColor]\" => \"[baseColor]\"\n          \"Material base\" => \"Material base\"\n          \"Base type\" => \"Base type\"\n          \"Battery amount\" => \"Battery amount\"\n          \"Battery info\" => \"Battery info\"\n          \"Body size\" => \"Body size\"\n          \"Body size (Unit)\" => \"Body size (Unit)\"\n          \"Cabinet type\" => \"Cabinet type\"\n          \"Cable Color\" => \"Cable Color\"\n          \"Material cable\" => \"Material cable\"\n          \"Suitable for number of candles\" => \"Suitable for number of candles\"\n          \"CE Marking\" => \"CE Marking\"\n          \"Max. watt CFL\" => \"Max. watt CFL\"\n          \"Max. watt CFL (Unit)\" => \"Max. watt CFL (Unit)\"\n          \"Chair type\" => \"Chair type\"\n          \"Coat rack type\" => \"Coat rack type\"\n          \"HS code\" => \"HS code\"\n          \"HS code U.S.A.\" => \"HS code U.S.A.\"\n          \"Colour Fastness\" => \"Colour Fastness\"\n          \"Connection voltage\" => \"Connection voltage\"\n          \"Connection voltage (Unit)\" => \"Connection voltage (Unit)\"\n          \"Includes MDF\" => \"Includes MDF\"\n          \"Sofa type\" => \"Sofa type\"\n          \"Country of Origin\" => \"Country of Origin\"\n          \"Cover detachable\" => \"Cover detachable\"\n          \"Product description Google\" => \"Product description Google\"\n          \"Dimmable\" => \"Dimmable\"\n          \"Dining chair type\" => \"Dining chair type\"\n          \"Door type\" => \"Door type\"\n          \"Drawer amount\" => \"Drawer amount\"\n          \"[drawerIncluded]\" => \"[drawerIncluded]\"\n          \"Embargo date\" => \"Embargo date\"\n          \"Energy Class\" => \"Energy Class\"\n          \"Ergonomic\" => \"Ergonomic\"\n          \"Mounting plate/foot height\" => \"Mounting plate/foot height\"\n          \"Mounting plate/foot height (Unit)\" => \"Mounting plate/foot height (Unit)\"\n          \"Fire retardant\" => \"Fire retardant\"\n          \"Fitting\" => \"Fitting\"\n          \"Flask amount\" => \"Flask amount\"\n          \"Foam density\" => \"Foam density\"\n          \"Furniture finish\" => \"Furniture finish\"\n          \"Garden chair type\" => \"Garden chair type\"\n          \"Gross weight in kg\" => \"Gross weight in kg\"\n          \"Height Adjustable\" => \"Height Adjustable\"\n          \"[hockerIncluded]\" => \"[hockerIncluded]\"\n          \"Hook amount\" => \"Hook amount\"\n          \"Suitable for Indoor/Outdoor\" => \"Suitable for Indoor/Outdoor\"\n          \"IP Value\" => \"IP Value\"\n          \"Kelvin\" => \"Kelvin\"\n          \"Kelvin (Unit)\" => \"Kelvin (Unit)\"\n          \"Diameter lamp shade\" => \"Diameter lamp shade\"\n          \"Diameter lamp shade (Unit)\" => \"Diameter lamp shade (Unit)\"\n          \"Lamp shade Height\" => \"Lamp shade Height\"\n          \"Lamp shade Height (Unit)\" => \"Lamp shade Height (Unit)\"\n          \"Lamp shade Length\" => \"Lamp shade Length\"\n          \"Lamp shade Length (Unit)\" => \"Lamp shade Length (Unit)\"\n          \"Width shade\" => \"Width shade\"\n          \"Width shade (Unit)\" => \"Width shade (Unit)\"\n          \"Max. watt LED\" => \"Max. watt LED\"\n          \"Max. watt LED (Unit)\" => \"Max. watt LED (Unit)\"\n          \"Leg Distance\" => \"Leg Distance\"\n          \"Leg Distance (Unit)\" => \"Leg Distance (Unit)\"\n          \"Leg height\" => \"Leg height\"\n          \"Leg height (Unit)\" => \"Leg height (Unit)\"\n          \"Light color\" => \"Light color\"\n          \"Light direction\" => \"Light direction\"\n          \"Lighting control\" => \"Lighting control\"\n          \"Light source amount\" => \"Light source amount\"\n          \"Light Source Included\" => \"Light Source Included\"\n          \"Light Source Lifespan\" => \"Light Source Lifespan\"\n          \"Light Source Lifespan (Unit)\" => \"Light Source Lifespan (Unit)\"\n          \"Lumen per light source\" => \"Lumen per light source\"\n          \"Lumen per light source (Unit)\" => \"Lumen per light source (Unit)\"\n          \"Light source removable\" => \"Light source removable\"\n          \"max. loadable weight\" => \"max. loadable weight\"\n          \"max. loadable weight (Unit)\" => \"max. loadable weight (Unit)\"\n          \"Logistic height in cm\" => \"Logistic height in cm\"\n          \"Logistic length in cm\" => \"Logistic length in cm\"\n          \"Logistic width in cm\" => \"Logistic width in cm\"\n          \"\"Long description (English United States\" => \"\"Long description (English United States\"\n          \"Dealer login)\"\" => \"Dealer login)\"\"\n          \"\"Long description (Dutch Netherlands\" => \"\"Long description (Dutch Netherlands\"\n          \"Maintenance advice\" => \"Maintenance advice\"\n          \"Martindale\" => \"Martindale\"\n          \"Material body light\" => \"Material body light\"\n          \"Material composition\" => \"Material composition\"\n          \"Material cushion\" => \"Material cushion\"\n          \"Material carpet\" => \"Material carpet\"\n          \"Material shade\" => \"Material shade\"\n          \"Meta title (English United States)\" => \"Meta title (English United States)\"\n          \"Meta title (Dutch Netherlands)\" => \"Meta title (Dutch Netherlands)\"\n          \"Mirror type\" => \"Mirror type\"\n          \"Length of mounting plate/foot\" => \"Length of mounting plate/foot\"\n          \"Length of mounting plate/foot (Unit)\" => \"Length of mounting plate/foot (Unit)\"\n          \"Width of mounting plate/feet\" => \"Width of mounting plate/feet\"\n          \"Width of mounting plate/feet (Unit)\" => \"Width of mounting plate/feet (Unit)\"\n          \"Netto Weight Article\" => \"Netto Weight Article\"\n          \"Netto Weight Article (Unit)\" => \"Netto Weight Article (Unit)\"\n          \"Net weight in kg\" => \"Net weight in kg\"\n          \"Office chair type\" => \"Office chair type\"\n          \"Other dimensions\" => \"Other dimensions\"\n          \"Outer Packaging Gross Volume\" => \"Outer Packaging Gross Volume\"\n          \"Packaging Gross Weight (in KG)\" => \"Packaging Gross Weight (in KG)\"\n          \"GTIN Outer Packaging\" => \"GTIN Outer Packaging\"\n          \"Outer Packaging Width\" => \"Outer Packaging Width\"\n          \"Outer Packaging Length\" => \"Outer Packaging Length\"\n          \"Outer Packaging Height\" => \"Outer Packaging Height\"\n          \"Packing net weight of solid wood in kg\" => \"Packing net weight of solid wood in kg\"\n          \"Packaging net weight of paper in kg\" => \"Packaging net weight of paper in kg\"\n          \"Packaging net weight plastic in kg\" => \"Packaging net weight plastic in kg\"\n          \"Packing net weight of gel/gep wood in kg\" => \"Packing net weight of gel/gep wood in kg\"\n          \"Packing net weight styrofoam in kg\" => \"Packing net weight styrofoam in kg\"\n          \"Packing net weight of textile in kg\" => \"Packing net weight of textile in kg\"\n          \"Packaging number\" => \"Packaging number\"\n          \"Pattern\" => \"Pattern\"\n          \"Suitable for number of persons\" => \"Suitable for number of persons\"\n          \"PLC state\" => \"PLC state\"\n          \"Pole height\" => \"Pole height\"\n          \"Power type\" => \"Power type\"\n          \"Pre-assembled\" => \"Pre-assembled\"\n          \"\"Product bulletpoint 1 (English United States\" => \"\"Product bulletpoint 1 (English United States\"\n          \"\"Product bulletpoint 1 (Dutch Netherlands\" => \"\"Product bulletpoint 1 (Dutch Netherlands\"\n          \"\"Product bulletpoint 2 (English United States\" => \"\"Product bulletpoint 2 (English United States\"\n          \"\"Product bulletpoint 2 (Dutch Netherlands\" => \"\"Product bulletpoint 2 (Dutch Netherlands\"\n          \"\"Product bulletpoint 3 (English United States\" => \"\"Product bulletpoint 3 (English United States\"\n          \"\"Product bulletpoint 3 (Dutch Netherlands\" => \"\"Product bulletpoint 3 (Dutch Netherlands\"\n          \"\"Product bulletpoint 4 (English United States\" => \"\"Product bulletpoint 4 (English United States\"\n          \"\"Product bulletpoint 4 (Dutch Netherlands\" => \"\"Product bulletpoint 4 (Dutch Netherlands\"\n          \"\"Product bulletpoint 5 (English United States\" => \"\"Product bulletpoint 5 (English United States\"\n          \"\"Product bulletpoint 5 (Dutch Netherlands\" => \"\"Product bulletpoint 5 (Dutch Netherlands\"\n          \"Product depth\" => \"Product depth\"\n          \"Product depth (Unit)\" => \"Product depth (Unit)\"\n          \"Product Description\" => \"Product Description\"\n          \"Product diameter\" => \"Product diameter\"\n          \"Product diameter (Unit)\" => \"Product diameter (Unit)\"\n          \"Product Group By Attribute(s)\" => \"Product Group By Attribute(s)\"\n          \"Product height\" => \"Product height\"\n          \"Product height (Unit)\" => \"Product height (Unit)\"\n          \"Product length\" => \"Product length\"\n          \"Product length (Unit)\" => \"Product length (Unit)\"\n          \"\"Product name (English United States\" => \"\"Product name (English United States\"\n          \"\"Product name (Dutch Netherlands\" => \"\"Product name (Dutch Netherlands\"\n          \"Product type (normal/KIT/Special)\" => \"Product type (normal/KIT/Special)\"\n          \"Product width\" => \"Product width\"\n          \"Product width (Unit)\" => \"Product width (Unit)\"\n          \"Quality Mark\" => \"Quality Mark\"\n          \"Suitable for area\" => \"Suitable for area\"\n          \"Rotatable\" => \"Rotatable\"\n          \"Material backing carpet\" => \"Material backing carpet\"\n          \"Safety Class\" => \"Safety Class\"\n          \"Drop\" => \"Drop\"\n          \"Drop year\" => \"Drop year\"\n          \"Seat amount\" => \"Seat amount\"\n          \"Seat color\" => \"Seat color\"\n          \"Seat depth\" => \"Seat depth\"\n          \"Seat depth (Unit)\" => \"Seat depth (Unit)\"\n          \"Seat height\" => \"Seat height\"\n          \"Seat height (Unit)\" => \"Seat height (Unit)\"\n          \"Seating comfort\" => \"Seating comfort\"\n          \"Material seat\" => \"Material seat\"\n          \"Seat width\" => \"Seat width\"\n          \"Seat width (Unit)\" => \"Seat width (Unit)\"\n          \"Serial name\" => \"Serial name\"\n          \"Set Contains\" => \"Set Contains\"\n          \"Shape\" => \"Shape\"\n          \"Shelf amount\" => \"Shelf amount\"\n          \"Max. loadable weight shelf\" => \"Max. loadable weight shelf\"\n          \"Max. loadable weight shelf (Unit)\" => \"Max. loadable weight shelf (Unit)\"\n          \"\"Short description (English United States\" => \"\"Short description (English United States\"\n          \"\"Short description (Dutch Netherlands\" => \"\"Short description (Dutch Netherlands\"\n          \"Position corner sofa\" => \"Position corner sofa\"\n          \"Stack able\" => \"Stack able\"\n          \"Stuffing material\" => \"Stuffing material\"\n          \"Suitable for underfloor heating\" => \"Suitable for underfloor heating\"\n          \"Trade name and contact address of manufacturer or importer in EU\" => \"Trade name and contact address of manufacturer or importer in EU\"\n          \"Type of surface mounted spotlight\" => \"Type of surface mounted spotlight\"\n          \"Extendable table top\" => \"Extendable table top\"\n          \"Table top finish\" => \"Table top finish\"\n          \"Material table top\" => \"Material table top\"\n          \"Table type\" => \"Table type\"\n          \"Upholstered\" => \"Upholstered\"\n          \"Integrated USB port\" => \"Integrated USB port\"\n          \"VAT Code (H)\" => \"VAT Code (H)\"\n          \"Wall mount\" => \"Wall mount\"\n          \"Washing instructions\" => \"Washing instructions\"\n          \"Silent clockwork\" => \"Silent clockwork\"\n          \"Waterproof\" => \"Waterproof\"\n          \"With wheels\" => \"With wheels\"\n          \"Wire length\" => \"Wire length\"\n          \"Wire length (Unit)\" => \"Wire length (Unit)\"\n          \"Wire with plug included\" => \"Wire with plug included\"\n          \"Asset collection images\" => \"Asset collection images\"\n          \"[assetCollectionImages-file_path]\" => \"[assetCollectionImages-file_path]\"\n          \"Image URL 1\" => \"Image URL 1\"\n          \"Image URL 2\" => \"Image URL 2\"\n          \"Image URL 3\" => \"Image URL 3\"\n          \"Image URL 4\" => \"Image URL 4\"\n          \"Image URL 5\" => \"Image URL 5\"\n          \"Image URL 6\" => \"Image URL 6\"\n          \"Image URL 7\" => \"Image URL 7\"\n          \"Image URL 8\" => \"Image URL 8\"\n          \"Image URL 9\" => \"Image URL 9\"\n          \"Image URL 10\" => \"Image URL 10\"\n          \"Image URL 11\" => \"Image URL 11\"\n          \"Image URL 12\" => \"Image URL 12\"\n          \"Image URL 13\" => \"Image URL 13\"\n          \"Image URL 14\" => \"Image URL 14\"\n          \"Image URL 15\" => \"Image URL 15\"\n          \"\" => \"\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_input_array\" => array:1 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:228 [\n        0 => array:2 [\n          \"label\" => \"Categories\"\n          \"value\" => \"Default,Categories\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Collection label\"\n          \"value\" => \"Default,Collection label\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Product name\"\n          \"value\" => \"Default,Product name\"\n        ]\n        3 => array:2 [\n          \"label\" => \"SKU\"\n          \"value\" => \"Default,SKU\"\n        ]\n        4 => array:2 [\n          \"label\" => \"GTIN\"\n          \"value\" => \"Default,GTIN\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Color\"\n          \"value\" => \"Default,Color\"\n        ]\n        6 => array:2 [\n          \"label\" => \"Material\"\n          \"value\" => \"Default,Material\"\n        ]\n        7 => array:2 [\n          \"label\" => \"Multiple consumer\"\n          \"value\" => \"Default,Multiple consumer\"\n        ]\n        8 => array:2 [\n          \"label\" => \"Multiple required\"\n          \"value\" => \"Default,Multiple required\"\n        ]\n        9 => array:2 [\n          \"label\" => \"Colli amount\"\n          \"value\" => \"Default,Colli amount\"\n        ]\n        10 => array:2 [\n          \"label\" => \"Adjustment options\"\n          \"value\" => \"Default,Adjustment options\"\n        ]\n        11 => array:2 [\n          \"label\" => \"Anti slip\"\n          \"value\" => \"Default,Anti slip\"\n        ]\n        12 => array:2 [\n          \"label\" => \"Armrest Height\"\n          \"value\" => \"Default,Armrest Height\"\n        ]\n        13 => array:2 [\n          \"label\" => \"Armrest Height (Unit)\"\n          \"value\" => \"Default,Armrest Height (Unit)\"\n        ]\n        14 => array:2 [\n          \"label\" => \"With armrests\"\n          \"value\" => \"Default,With armrests\"\n        ]\n        15 => array:2 [\n          \"label\" => \"Width armrest\"\n          \"value\" => \"Default,Width armrest\"\n        ]\n        16 => array:2 [\n          \"label\" => \"Width armrest (Unit)\"\n          \"value\" => \"Default,Width armrest (Unit)\"\n        ]\n        17 => array:2 [\n          \"label\" => \"Adjustable backrest\"\n          \"value\" => \"Default,Adjustable backrest\"\n        ]\n        18 => array:2 [\n          \"label\" => \"With backrests\"\n          \"value\" => \"Default,With backrests\"\n        ]\n        19 => array:2 [\n          \"label\" => \"[baseColor]\"\n          \"value\" => \"Default,[baseColor]\"\n        ]\n        20 => array:2 [\n          \"label\" => \"Material base\"\n          \"value\" => \"Default,Material base\"\n        ]\n        21 => array:2 [\n          \"label\" => \"Base type\"\n          \"value\" => \"Default,Base type\"\n        ]\n        22 => array:2 [\n          \"label\" => \"Battery amount\"\n          \"value\" => \"Default,Battery amount\"\n        ]\n        23 => array:2 [\n          \"label\" => \"Battery info\"\n          \"value\" => \"Default,Battery info\"\n        ]\n        24 => array:2 [\n          \"label\" => \"Body size\"\n          \"value\" => \"Default,Body size\"\n        ]\n        25 => array:2 [\n          \"label\" => \"Body size (Unit)\"\n          \"value\" => \"Default,Body size (Unit)\"\n        ]\n        26 => array:2 [\n          \"label\" => \"Cabinet type\"\n          \"value\" => \"Default,Cabinet type\"\n        ]\n        27 => array:2 [\n          \"label\" => \"Cable Color\"\n          \"value\" => \"Default,Cable Color\"\n        ]\n        28 => array:2 [\n          \"label\" => \"Material cable\"\n          \"value\" => \"Default,Material cable\"\n        ]\n        29 => array:2 [\n          \"label\" => \"Suitable for number of candles\"\n          \"value\" => \"Default,Suitable for number of candles\"\n        ]\n        30 => array:2 [\n          \"label\" => \"CE Marking\"\n          \"value\" => \"Default,CE Marking\"\n        ]\n        31 => array:2 [\n          \"label\" => \"Max. watt CFL\"\n          \"value\" => \"Default,Max. watt CFL\"\n        ]\n        32 => array:2 [\n          \"label\" => \"Max. watt CFL (Unit)\"\n          \"value\" => \"Default,Max. watt CFL (Unit)\"\n        ]\n        33 => array:2 [\n          \"label\" => \"Chair type\"\n          \"value\" => \"Default,Chair type\"\n        ]\n        34 => array:2 [\n          \"label\" => \"Coat rack type\"\n          \"value\" => \"Default,Coat rack type\"\n        ]\n        35 => array:2 [\n          \"label\" => \"HS code\"\n          \"value\" => \"Default,HS code\"\n        ]\n        36 => array:2 [\n          \"label\" => \"HS code U.S.A.\"\n          \"value\" => \"Default,HS code U.S.A.\"\n        ]\n        37 => array:2 [\n          \"label\" => \"Colour Fastness\"\n          \"value\" => \"Default,Colour Fastness\"\n        ]\n        38 => array:2 [\n          \"label\" => \"Connection voltage\"\n          \"value\" => \"Default,Connection voltage\"\n        ]\n        39 => array:2 [\n          \"label\" => \"Connection voltage (Unit)\"\n          \"value\" => \"Default,Connection voltage (Unit)\"\n        ]\n        40 => array:2 [\n          \"label\" => \"Includes MDF\"\n          \"value\" => \"Default,Includes MDF\"\n        ]\n        41 => array:2 [\n          \"label\" => \"Sofa type\"\n          \"value\" => \"Default,Sofa type\"\n        ]\n        42 => array:2 [\n          \"label\" => \"Country of Origin\"\n          \"value\" => \"Default,Country of Origin\"\n        ]\n        43 => array:2 [\n          \"label\" => \"Cover detachable\"\n          \"value\" => \"Default,Cover detachable\"\n        ]\n        44 => array:2 [\n          \"label\" => \"Product description Google\"\n          \"value\" => \"Default,Product description Google\"\n        ]\n        45 => array:2 [\n          \"label\" => \"Dimmable\"\n          \"value\" => \"Default,Dimmable\"\n        ]\n        46 => array:2 [\n          \"label\" => \"Dining chair type\"\n          \"value\" => \"Default,Dining chair type\"\n        ]\n        47 => array:2 [\n          \"label\" => \"Door type\"\n          \"value\" => \"Default,Door type\"\n        ]\n        48 => array:2 [\n          \"label\" => \"Drawer amount\"\n          \"value\" => \"Default,Drawer amount\"\n        ]\n        49 => array:2 [\n          \"label\" => \"[drawerIncluded]\"\n          \"value\" => \"Default,[drawerIncluded]\"\n        ]\n        50 => array:2 [\n          \"label\" => \"Embargo date\"\n          \"value\" => \"Default,Embargo date\"\n        ]\n        51 => array:2 [\n          \"label\" => \"Energy Class\"\n          \"value\" => \"Default,Energy Class\"\n        ]\n        52 => array:2 [\n          \"label\" => \"Ergonomic\"\n          \"value\" => \"Default,Ergonomic\"\n        ]\n        53 => array:2 [\n          \"label\" => \"Mounting plate/foot height\"\n          \"value\" => \"Default,Mounting plate/foot height\"\n        ]\n        54 => array:2 [\n          \"label\" => \"Mounting plate/foot height (Unit)\"\n          \"value\" => \"Default,Mounting plate/foot height (Unit)\"\n        ]\n        55 => array:2 [\n          \"label\" => \"Fire retardant\"\n          \"value\" => \"Default,Fire retardant\"\n        ]\n        56 => array:2 [\n          \"label\" => \"Fitting\"\n          \"value\" => \"Default,Fitting\"\n        ]\n        57 => array:2 [\n          \"label\" => \"Flask amount\"\n          \"value\" => \"Default,Flask amount\"\n        ]\n        58 => array:2 [\n          \"label\" => \"Foam density\"\n          \"value\" => \"Default,Foam density\"\n        ]\n        59 => array:2 [\n          \"label\" => \"Furniture finish\"\n          \"value\" => \"Default,Furniture finish\"\n        ]\n        60 => array:2 [\n          \"label\" => \"Garden chair type\"\n          \"value\" => \"Default,Garden chair type\"\n        ]\n        61 => array:2 [\n          \"label\" => \"Gross weight in kg\"\n          \"value\" => \"Default,Gross weight in kg\"\n        ]\n        62 => array:2 [\n          \"label\" => \"Height Adjustable\"\n          \"value\" => \"Default,Height Adjustable\"\n        ]\n        63 => array:2 [\n          \"label\" => \"[hockerIncluded]\"\n          \"value\" => \"Default,[hockerIncluded]\"\n        ]\n        64 => array:2 [\n          \"label\" => \"Hook amount\"\n          \"value\" => \"Default,Hook amount\"\n        ]\n        65 => array:2 [\n          \"label\" => \"Suitable for Indoor/Outdoor\"\n          \"value\" => \"Default,Suitable for Indoor/Outdoor\"\n        ]\n        66 => array:2 [\n          \"label\" => \"IP Value\"\n          \"value\" => \"Default,IP Value\"\n        ]\n        67 => array:2 [\n          \"label\" => \"Kelvin\"\n          \"value\" => \"Default,Kelvin\"\n        ]\n        68 => array:2 [\n          \"label\" => \"Kelvin (Unit)\"\n          \"value\" => \"Default,Kelvin (Unit)\"\n        ]\n        69 => array:2 [\n          \"label\" => \"Diameter lamp shade\"\n          \"value\" => \"Default,Diameter lamp shade\"\n        ]\n        70 => array:2 [\n          \"label\" => \"Diameter lamp shade (Unit)\"\n          \"value\" => \"Default,Diameter lamp shade (Unit)\"\n        ]\n        71 => array:2 [\n          \"label\" => \"Lamp shade Height\"\n          \"value\" => \"Default,Lamp shade Height\"\n        ]\n        72 => array:2 [\n          \"label\" => \"Lamp shade Height (Unit)\"\n          \"value\" => \"Default,Lamp shade Height (Unit)\"\n        ]\n        73 => array:2 [\n          \"label\" => \"Lamp shade Length\"\n          \"value\" => \"Default,Lamp shade Length\"\n        ]\n        74 => array:2 [\n          \"label\" => \"Lamp shade Length (Unit)\"\n          \"value\" => \"Default,Lamp shade Length (Unit)\"\n        ]\n        75 => array:2 [\n          \"label\" => \"Width shade\"\n          \"value\" => \"Default,Width shade\"\n        ]\n        76 => array:2 [\n          \"label\" => \"Width shade (Unit)\"\n          \"value\" => \"Default,Width shade (Unit)\"\n        ]\n        77 => array:2 [\n          \"label\" => \"Max. watt LED\"\n          \"value\" => \"Default,Max. watt LED\"\n        ]\n        78 => array:2 [\n          \"label\" => \"Max. watt LED (Unit)\"\n          \"value\" => \"Default,Max. watt LED (Unit)\"\n        ]\n        79 => array:2 [\n          \"label\" => \"Leg Distance\"\n          \"value\" => \"Default,Leg Distance\"\n        ]\n        80 => array:2 [\n          \"label\" => \"Leg Distance (Unit)\"\n          \"value\" => \"Default,Leg Distance (Unit)\"\n        ]\n        81 => array:2 [\n          \"label\" => \"Leg height\"\n          \"value\" => \"Default,Leg height\"\n        ]\n        82 => array:2 [\n          \"label\" => \"Leg height (Unit)\"\n          \"value\" => \"Default,Leg height (Unit)\"\n        ]\n        83 => array:2 [\n          \"label\" => \"Light color\"\n          \"value\" => \"Default,Light color\"\n        ]\n        84 => array:2 [\n          \"label\" => \"Light direction\"\n          \"value\" => \"Default,Light direction\"\n        ]\n        85 => array:2 [\n          \"label\" => \"Lighting control\"\n          \"value\" => \"Default,Lighting control\"\n        ]\n        86 => array:2 [\n          \"label\" => \"Light source amount\"\n          \"value\" => \"Default,Light source amount\"\n        ]\n        87 => array:2 [\n          \"label\" => \"Light Source Included\"\n          \"value\" => \"Default,Light Source Included\"\n        ]\n        88 => array:2 [\n          \"label\" => \"Light Source Lifespan\"\n          \"value\" => \"Default,Light Source Lifespan\"\n        ]\n        89 => array:2 [\n          \"label\" => \"Light Source Lifespan (Unit)\"\n          \"value\" => \"Default,Light Source Lifespan (Unit)\"\n        ]\n        90 => array:2 [\n          \"label\" => \"Lumen per light source\"\n          \"value\" => \"Default,Lumen per light source\"\n        ]\n        91 => array:2 [\n          \"label\" => \"Lumen per light source (Unit)\"\n          \"value\" => \"Default,Lumen per light source (Unit)\"\n        ]\n        92 => array:2 [\n          \"label\" => \"Light source removable\"\n          \"value\" => \"Default,Light source removable\"\n        ]\n        93 => array:2 [\n          \"label\" => \"max. loadable weight\"\n          \"value\" => \"Default,max. loadable weight\"\n        ]\n        94 => array:2 [\n          \"label\" => \"max. loadable weight (Unit)\"\n          \"value\" => \"Default,max. loadable weight (Unit)\"\n        ]\n        95 => array:2 [\n          \"label\" => \"Logistic height in cm\"\n          \"value\" => \"Default,Logistic height in cm\"\n        ]\n        96 => array:2 [\n          \"label\" => \"Logistic length in cm\"\n          \"value\" => \"Default,Logistic length in cm\"\n        ]\n        97 => array:2 [\n          \"label\" => \"Logistic width in cm\"\n          \"value\" => \"Default,Logistic width in cm\"\n        ]\n        98 => array:2 [\n          \"label\" => \"\"Long description (English United States\"\n          \"value\" => \"Default,\"Long description (English United States\"\n        ]\n        99 => array:2 [\n          \"label\" => \"Dealer login)\"\"\n          \"value\" => \"Default,Dealer login)\"\"\n        ]\n        100 => array:2 [\n          \"label\" => \"\"Long description (Dutch Netherlands\"\n          \"value\" => \"Default,\"Long description (Dutch Netherlands\"\n        ]\n        101 => array:2 [\n          \"label\" => \"Maintenance advice\"\n          \"value\" => \"Default,Maintenance advice\"\n        ]\n        102 => array:2 [\n          \"label\" => \"Martindale\"\n          \"value\" => \"Default,Martindale\"\n        ]\n        103 => array:2 [\n          \"label\" => \"Material body light\"\n          \"value\" => \"Default,Material body light\"\n        ]\n        104 => array:2 [\n          \"label\" => \"Material composition\"\n          \"value\" => \"Default,Material composition\"\n        ]\n        105 => array:2 [\n          \"label\" => \"Material cushion\"\n          \"value\" => \"Default,Material cushion\"\n        ]\n        106 => array:2 [\n          \"label\" => \"Material carpet\"\n          \"value\" => \"Default,Material carpet\"\n        ]\n        107 => array:2 [\n          \"label\" => \"Material shade\"\n          \"value\" => \"Default,Material shade\"\n        ]\n        108 => array:2 [\n          \"label\" => \"Meta title (English United States)\"\n          \"value\" => \"Default,Meta title (English United States)\"\n        ]\n        109 => array:2 [\n          \"label\" => \"Meta title (Dutch Netherlands)\"\n          \"value\" => \"Default,Meta title (Dutch Netherlands)\"\n        ]\n        110 => array:2 [\n          \"label\" => \"Mirror type\"\n          \"value\" => \"Default,Mirror type\"\n        ]\n        111 => array:2 [\n          \"label\" => \"Length of mounting plate/foot\"\n          \"value\" => \"Default,Length of mounting plate/foot\"\n        ]\n        112 => array:2 [\n          \"label\" => \"Length of mounting plate/foot (Unit)\"\n          \"value\" => \"Default,Length of mounting plate/foot (Unit)\"\n        ]\n        113 => array:2 [\n          \"label\" => \"Width of mounting plate/feet\"\n          \"value\" => \"Default,Width of mounting plate/feet\"\n        ]\n        114 => array:2 [\n          \"label\" => \"Width of mounting plate/feet (Unit)\"\n          \"value\" => \"Default,Width of mounting plate/feet (Unit)\"\n        ]\n        115 => array:2 [\n          \"label\" => \"Netto Weight Article\"\n          \"value\" => \"Default,Netto Weight Article\"\n        ]\n        116 => array:2 [\n          \"label\" => \"Netto Weight Article (Unit)\"\n          \"value\" => \"Default,Netto Weight Article (Unit)\"\n        ]\n        117 => array:2 [\n          \"label\" => \"Net weight in kg\"\n          \"value\" => \"Default,Net weight in kg\"\n        ]\n        118 => array:2 [\n          \"label\" => \"Office chair type\"\n          \"value\" => \"Default,Office chair type\"\n        ]\n        119 => array:2 [\n          \"label\" => \"Other dimensions\"\n          \"value\" => \"Default,Other dimensions\"\n        ]\n        120 => array:2 [\n          \"label\" => \"Outer Packaging Gross Volume\"\n          \"value\" => \"Default,Outer Packaging Gross Volume\"\n        ]\n        121 => array:2 [\n          \"label\" => \"Packaging Gross Weight (in KG)\"\n          \"value\" => \"Default,Packaging Gross Weight (in KG)\"\n        ]\n        122 => array:2 [\n          \"label\" => \"GTIN Outer Packaging\"\n          \"value\" => \"Default,GTIN Outer Packaging\"\n        ]\n        123 => array:2 [\n          \"label\" => \"Outer Packaging Width\"\n          \"value\" => \"Default,Outer Packaging Width\"\n        ]\n        124 => array:2 [\n          \"label\" => \"Outer Packaging Length\"\n          \"value\" => \"Default,Outer Packaging Length\"\n        ]\n        125 => array:2 [\n          \"label\" => \"Outer Packaging Height\"\n          \"value\" => \"Default,Outer Packaging Height\"\n        ]\n        126 => array:2 [\n          \"label\" => \"Packing net weight of solid wood in kg\"\n          \"value\" => \"Default,Packing net weight of solid wood in kg\"\n        ]\n        127 => array:2 [\n          \"label\" => \"Packaging net weight of paper in kg\"\n          \"value\" => \"Default,Packaging net weight of paper in kg\"\n        ]\n        128 => array:2 [\n          \"label\" => \"Packaging net weight plastic in kg\"\n          \"value\" => \"Default,Packaging net weight plastic in kg\"\n        ]\n        129 => array:2 [\n          \"label\" => \"Packing net weight of gel/gep wood in kg\"\n          \"value\" => \"Default,Packing net weight of gel/gep wood in kg\"\n        ]\n        130 => array:2 [\n          \"label\" => \"Packing net weight styrofoam in kg\"\n          \"value\" => \"Default,Packing net weight styrofoam in kg\"\n        ]\n        131 => array:2 [\n          \"label\" => \"Packing net weight of textile in kg\"\n          \"value\" => \"Default,Packing net weight of textile in kg\"\n        ]\n        132 => array:2 [\n          \"label\" => \"Packaging number\"\n          \"value\" => \"Default,Packaging number\"\n        ]\n        133 => array:2 [\n          \"label\" => \"Pattern\"\n          \"value\" => \"Default,Pattern\"\n        ]\n        134 => array:2 [\n          \"label\" => \"Suitable for number of persons\"\n          \"value\" => \"Default,Suitable for number of persons\"\n        ]\n        135 => array:2 [\n          \"label\" => \"PLC state\"\n          \"value\" => \"Default,PLC state\"\n        ]\n        136 => array:2 [\n          \"label\" => \"Pole height\"\n          \"value\" => \"Default,Pole height\"\n        ]\n        137 => array:2 [\n          \"label\" => \"Power type\"\n          \"value\" => \"Default,Power type\"\n        ]\n        138 => array:2 [\n          \"label\" => \"Pre-assembled\"\n          \"value\" => \"Default,Pre-assembled\"\n        ]\n        139 => array:2 [\n          \"label\" => \"\"Product bulletpoint 1 (English United States\"\n          \"value\" => \"Default,\"Product bulletpoint 1 (English United States\"\n        ]\n        140 => array:2 [\n          \"label\" => \"\"Product bulletpoint 1 (Dutch Netherlands\"\n          \"value\" => \"Default,\"Product bulletpoint 1 (Dutch Netherlands\"\n        ]\n        141 => array:2 [\n          \"label\" => \"\"Product bulletpoint 2 (English United States\"\n          \"value\" => \"Default,\"Product bulletpoint 2 (English United States\"\n        ]\n        142 => array:2 [\n          \"label\" => \"\"Product bulletpoint 2 (Dutch Netherlands\"\n          \"value\" => \"Default,\"Product bulletpoint 2 (Dutch Netherlands\"\n        ]\n        143 => array:2 [\n          \"label\" => \"\"Product bulletpoint 3 (English United States\"\n          \"value\" => \"Default,\"Product bulletpoint 3 (English United States\"\n        ]\n        144 => array:2 [\n          \"label\" => \"\"Product bulletpoint 3 (Dutch Netherlands\"\n          \"value\" => \"Default,\"Product bulletpoint 3 (Dutch Netherlands\"\n        ]\n        145 => array:2 [\n          \"label\" => \"\"Product bulletpoint 4 (English United States\"\n          \"value\" => \"Default,\"Product bulletpoint 4 (English United States\"\n        ]\n        146 => array:2 [\n          \"label\" => \"\"Product bulletpoint 4 (Dutch Netherlands\"\n          \"value\" => \"Default,\"Product bulletpoint 4 (Dutch Netherlands\"\n        ]\n        147 => array:2 [\n          \"label\" => \"\"Product bulletpoint 5 (English United States\"\n          \"value\" => \"Default,\"Product bulletpoint 5 (English United States\"\n        ]\n        148 => array:2 [\n          \"label\" => \"\"Product bulletpoint 5 (Dutch Netherlands\"\n          \"value\" => \"Default,\"Product bulletpoint 5 (Dutch Netherlands\"\n        ]\n        149 => array:2 [\n          \"label\" => \"Product depth\"\n          \"value\" => \"Default,Product depth\"\n        ]\n        150 => array:2 [\n          \"label\" => \"Product depth (Unit)\"\n          \"value\" => \"Default,Product depth (Unit)\"\n        ]\n        151 => array:2 [\n          \"label\" => \"Product Description\"\n          \"value\" => \"Default,Product Description\"\n        ]\n        152 => array:2 [\n          \"label\" => \"Product diameter\"\n          \"value\" => \"Default,Product diameter\"\n        ]\n        153 => array:2 [\n          \"label\" => \"Product diameter (Unit)\"\n          \"value\" => \"Default,Product diameter (Unit)\"\n        ]\n        154 => array:2 [\n          \"label\" => \"Product Group By Attribute(s)\"\n          \"value\" => \"Default,Product Group By Attribute(s)\"\n        ]\n        155 => array:2 [\n          \"label\" => \"Product height\"\n          \"value\" => \"Default,Product height\"\n        ]\n        156 => array:2 [\n          \"label\" => \"Product height (Unit)\"\n          \"value\" => \"Default,Product height (Unit)\"\n        ]\n        157 => array:2 [\n          \"label\" => \"Product length\"\n          \"value\" => \"Default,Product length\"\n        ]\n        158 => array:2 [\n          \"label\" => \"Product length (Unit)\"\n          \"value\" => \"Default,Product length (Unit)\"\n        ]\n        159 => array:2 [\n          \"label\" => \"\"Product name (English United States\"\n          \"value\" => \"Default,\"Product name (English United States\"\n        ]\n        160 => array:2 [\n          \"label\" => \"\"Product name (Dutch Netherlands\"\n          \"value\" => \"Default,\"Product name (Dutch Netherlands\"\n        ]\n        161 => array:2 [\n          \"label\" => \"Product type (normal/KIT/Special)\"\n          \"value\" => \"Default,Product type (normal/KIT/Special)\"\n        ]\n        162 => array:2 [\n          \"label\" => \"Product width\"\n          \"value\" => \"Default,Product width\"\n        ]\n        163 => array:2 [\n          \"label\" => \"Product width (Unit)\"\n          \"value\" => \"Default,Product width (Unit)\"\n        ]\n        164 => array:2 [\n          \"label\" => \"Quality Mark\"\n          \"value\" => \"Default,Quality Mark\"\n        ]\n        165 => array:2 [\n          \"label\" => \"Suitable for area\"\n          \"value\" => \"Default,Suitable for area\"\n        ]\n        166 => array:2 [\n          \"label\" => \"Rotatable\"\n          \"value\" => \"Default,Rotatable\"\n        ]\n        167 => array:2 [\n          \"label\" => \"Material backing carpet\"\n          \"value\" => \"Default,Material backing carpet\"\n        ]\n        168 => array:2 [\n          \"label\" => \"Safety Class\"\n          \"value\" => \"Default,Safety Class\"\n        ]\n        169 => array:2 [\n          \"label\" => \"Drop\"\n          \"value\" => \"Default,Drop\"\n        ]\n        170 => array:2 [\n          \"label\" => \"Drop year\"\n          \"value\" => \"Default,Drop year\"\n        ]\n        171 => array:2 [\n          \"label\" => \"Seat amount\"\n          \"value\" => \"Default,Seat amount\"\n        ]\n        172 => array:2 [\n          \"label\" => \"Seat color\"\n          \"value\" => \"Default,Seat color\"\n        ]\n        173 => array:2 [\n          \"label\" => \"Seat depth\"\n          \"value\" => \"Default,Seat depth\"\n        ]\n        174 => array:2 [\n          \"label\" => \"Seat depth (Unit)\"\n          \"value\" => \"Default,Seat depth (Unit)\"\n        ]\n        175 => array:2 [\n          \"label\" => \"Seat height\"\n          \"value\" => \"Default,Seat height\"\n        ]\n        176 => array:2 [\n          \"label\" => \"Seat height (Unit)\"\n          \"value\" => \"Default,Seat height (Unit)\"\n        ]\n        177 => array:2 [\n          \"label\" => \"Seating comfort\"\n          \"value\" => \"Default,Seating comfort\"\n        ]\n        178 => array:2 [\n          \"label\" => \"Material seat\"\n          \"value\" => \"Default,Material seat\"\n        ]\n        179 => array:2 [\n          \"label\" => \"Seat width\"\n          \"value\" => \"Default,Seat width\"\n        ]\n        180 => array:2 [\n          \"label\" => \"Seat width (Unit)\"\n          \"value\" => \"Default,Seat width (Unit)\"\n        ]\n        181 => array:2 [\n          \"label\" => \"Serial name\"\n          \"value\" => \"Default,Serial name\"\n        ]\n        182 => array:2 [\n          \"label\" => \"Set Contains\"\n          \"value\" => \"Default,Set Contains\"\n        ]\n        183 => array:2 [\n          \"label\" => \"Shape\"\n          \"value\" => \"Default,Shape\"\n        ]\n        184 => array:2 [\n          \"label\" => \"Shelf amount\"\n          \"value\" => \"Default,Shelf amount\"\n        ]\n        185 => array:2 [\n          \"label\" => \"Max. loadable weight shelf\"\n          \"value\" => \"Default,Max. loadable weight shelf\"\n        ]\n        186 => array:2 [\n          \"label\" => \"Max. loadable weight shelf (Unit)\"\n          \"value\" => \"Default,Max. loadable weight shelf (Unit)\"\n        ]\n        187 => array:2 [\n          \"label\" => \"\"Short description (English United States\"\n          \"value\" => \"Default,\"Short description (English United States\"\n        ]\n        188 => array:2 [\n          \"label\" => \"\"Short description (Dutch Netherlands\"\n          \"value\" => \"Default,\"Short description (Dutch Netherlands\"\n        ]\n        189 => array:2 [\n          \"label\" => \"Position corner sofa\"\n          \"value\" => \"Default,Position corner sofa\"\n        ]\n        190 => array:2 [\n          \"label\" => \"Stack able\"\n          \"value\" => \"Default,Stack able\"\n        ]\n        191 => array:2 [\n          \"label\" => \"Stuffing material\"\n          \"value\" => \"Default,Stuffing material\"\n        ]\n        192 => array:2 [\n          \"label\" => \"Suitable for underfloor heating\"\n          \"value\" => \"Default,Suitable for underfloor heating\"\n        ]\n        193 => array:2 [\n          \"label\" => \"Trade name and contact address of manufacturer or importer in EU\"\n          \"value\" => \"Default,Trade name and contact address of manufacturer or importer in EU\"\n        ]\n        194 => array:2 [\n          \"label\" => \"Type of surface mounted spotlight\"\n          \"value\" => \"Default,Type of surface mounted spotlight\"\n        ]\n        195 => array:2 [\n          \"label\" => \"Extendable table top\"\n          \"value\" => \"Default,Extendable table top\"\n        ]\n        196 => array:2 [\n          \"label\" => \"Table top finish\"\n          \"value\" => \"Default,Table top finish\"\n        ]\n        197 => array:2 [\n          \"label\" => \"Material table top\"\n          \"value\" => \"Default,Material table top\"\n        ]\n        198 => array:2 [\n          \"label\" => \"Table type\"\n          \"value\" => \"Default,Table type\"\n        ]\n        199 => array:2 [\n          \"label\" => \"Upholstered\"\n          \"value\" => \"Default,Upholstered\"\n        ]\n        200 => array:2 [\n          \"label\" => \"Integrated USB port\"\n          \"value\" => \"Default,Integrated USB port\"\n        ]\n        201 => array:2 [\n          \"label\" => \"VAT Code (H)\"\n          \"value\" => \"Default,VAT Code (H)\"\n        ]\n        202 => array:2 [\n          \"label\" => \"Wall mount\"\n          \"value\" => \"Default,Wall mount\"\n        ]\n        203 => array:2 [\n          \"label\" => \"Washing instructions\"\n          \"value\" => \"Default,Washing instructions\"\n        ]\n        204 => array:2 [\n          \"label\" => \"Silent clockwork\"\n          \"value\" => \"Default,Silent clockwork\"\n        ]\n        205 => array:2 [\n          \"label\" => \"Waterproof\"\n          \"value\" => \"Default,Waterproof\"\n        ]\n        206 => array:2 [\n          \"label\" => \"With wheels\"\n          \"value\" => \"Default,With wheels\"\n        ]\n        207 => array:2 [\n          \"label\" => \"Wire length\"\n          \"value\" => \"Default,Wire length\"\n        ]\n        208 => array:2 [\n          \"label\" => \"Wire length (Unit)\"\n          \"value\" => \"Default,Wire length (Unit)\"\n        ]\n        209 => array:2 [\n          \"label\" => \"Wire with plug included\"\n          \"value\" => \"Default,Wire with plug included\"\n        ]\n        210 => array:2 [\n          \"label\" => \"Asset collection images\"\n          \"value\" => \"Default,Asset collection images\"\n        ]\n        211 => array:2 [\n          \"label\" => \"[assetCollectionImages-file_path]\"\n          \"value\" => \"Default,[assetCollectionImages-file_path]\"\n        ]\n        212 => array:2 [\n          \"label\" => \"Image URL 1\"\n          \"value\" => \"Default,Image URL 1\"\n        ]\n        213 => array:2 [\n          \"label\" => \"Image URL 2\"\n          \"value\" => \"Default,Image URL 2\"\n        ]\n        214 => array:2 [\n          \"label\" => \"Image URL 3\"\n          \"value\" => \"Default,Image URL 3\"\n        ]\n        215 => array:2 [\n          \"label\" => \"Image URL 4\"\n          \"value\" => \"Default,Image URL 4\"\n        ]\n        216 => array:2 [\n          \"label\" => \"Image URL 5\"\n          \"value\" => \"Default,Image URL 5\"\n        ]\n        217 => array:2 [\n          \"label\" => \"Image URL 6\"\n          \"value\" => \"Default,Image URL 6\"\n        ]\n        218 => array:2 [\n          \"label\" => \"Image URL 7\"\n          \"value\" => \"Default,Image URL 7\"\n        ]\n        219 => array:2 [\n          \"label\" => \"Image URL 8\"\n          \"value\" => \"Default,Image URL 8\"\n        ]\n        220 => array:2 [\n          \"label\" => \"Image URL 9\"\n          \"value\" => \"Default,Image URL 9\"\n        ]\n        221 => array:2 [\n          \"label\" => \"Image URL 10\"\n          \"value\" => \"Default,Image URL 10\"\n        ]\n        222 => array:2 [\n          \"label\" => \"Image URL 11\"\n          \"value\" => \"Default,Image URL 11\"\n        ]\n        223 => array:2 [\n          \"label\" => \"Image URL 12\"\n          \"value\" => \"Default,Image URL 12\"\n        ]\n        224 => array:2 [\n          \"label\" => \"Image URL 13\"\n          \"value\" => \"Default,Image URL 13\"\n        ]\n        225 => array:2 [\n          \"label\" => \"Image URL 14\"\n          \"value\" => \"Default,Image URL 14\"\n        ]\n        226 => array:2 [\n          \"label\" => \"Image URL 15\"\n          \"value\" => \"Default,Image URL 15\"\n        ]\n        227 => array:2 [\n          \"label\" => \"\"\n          \"value\" => \"Default,\"\n        ]\n      ]\n    ]\n  ]\n  \"file_path\" => \"mapping_fields/upload/json/1748332390_datafile.csv\"\n  \"file_url\" => \"https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1748332390_datafile.csv\"\n  \"data_required\" => array:9 [\n    \"template_method_type\" => \"import\"\n    \"output_type\" => \"Apimio\"\n    \"sync\" => false\n    \"redirect_url_route\" => \"products.import\"\n    \"organization_id\" => \"11\"\n    \"versions\" => array:1 [\n      8 => \"en-US\"\n    ]\n    \"catalogs\" => array:1 [\n      8 => \"Tanzayb Store\"\n    ]\n    \"import_action\" => \"3\"\n    \"selected_template\" => array:9 [\n      \"temp_id\" => 23\n      \"temp_name\" => \"testingffff3355\"\n      \"created_at\" => Illuminate\\Support\\Carbon @1748332370 {#2467\n        #endOfTime: false\n        #startOfTime: false\n        #constructedObjectId: \"00000000000009a30000000000000000\"\n        #localMonthsOverflow: null\n        #localYearsOverflow: null\n        #localStrictModeEnabled: null\n        #localHumanDiffOptions: null\n        #localToStringFormat: null\n        #localSerializer: null\n        #localMacros: null\n        #localGenericMacros: null\n        #localFormatFunction: null\n        #localTranslator: null\n        #dumpProperties: array:3 [\n          0 => \"date\"\n          1 => \"timezone_type\"\n          2 => \"timezone\"\n        ]\n        #dumpLocale: null\n        #dumpDateProperties: null\n        date: 2025-05-27 07:52:50.0 UTC (+00:00)\n      }\n      \"payload\" => array:1 [\n        \"data\" => array:4 [\n          0 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Product Name\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => array:1 [\n              0 => \"General,product_name\"\n            ]\n            \"id\" => \"e22c210b-4e41-4dcd-bbba-c99303fe7c64\"\n          ]\n          1 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,SKU\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => array:1 [\n              0 => \"Variant,sku\"\n            ]\n            \"id\" => \"69183295-f7c7-4565-9004-c8f139879ad3\"\n          ]\n          2 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Price\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => array:1 [\n              0 => \"Variant,price\"\n            ]\n            \"id\" => \"333633e9-0359-4206-a7ee-38c3cc2b9db7\"\n          ]\n          3 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Quantity\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"f482a0d5-47d0-47c6-96c6-ce18b4c2b7b5\"\n          ]\n        ]\n      ]\n      \"catalog\" => array:1 [\n        0 => \"8\"\n      ]\n      \"version\" => \"8\"\n      \"type\" => \"import\"\n      \"export_type\" => null\n      \"product_status\" => 1\n    ]\n  ]\n  \"import_action\" => 3\n  \"apimio_attributes_required\" => array:2 [\n    \"all_families\" => []\n    \"all_attributes\" => array:6 [\n      0 => array:2 [\n        \"id\" => 1\n        \"name\" => \"single line text\"\n      ]\n      1 => array:2 [\n        \"id\" => 2\n        \"name\" => \"number\"\n      ]\n      2 => array:2 [\n        \"id\" => 3\n        \"name\" => \"multi line text\"\n      ]\n      3 => array:2 [\n        \"id\" => 7\n        \"name\" => \"measurement\"\n      ]\n      4 => array:2 [\n        \"id\" => 9\n        \"name\" => \"json\"\n      ]\n      5 => array:2 [\n        \"id\" => 11\n        \"name\" => \"url\"\n      ]\n    ]\n  ]\n  \"template_attributes\" => []\n  \"output_array\" => array:2 [\n    \"array_name\" => \"Apimio\"\n    \"nodes\" => array:6 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:5 [\n          \"handle\" => \"Product Identifier\"\n          \"file\" => \"Product Images\"\n          \"vendor\" => \"Vendor\"\n          \"brand\" => \"Brand\"\n          \"categories\" => \"Category\"\n        ]\n      ]\n      1 => array:2 [\n        \"name\" => \"General\"\n        \"attributes\" => array:2 [\n          \"product_name\" => \"Product Name\"\n          \"description\" => \"Description\"\n        ]\n      ]\n      2 => array:2 [\n        \"name\" => \"Variant\"\n        \"attributes\" => array:11 [\n          \"sku\" => \"SKU\"\n          \"name\" => \"Name\"\n          \"file\" => \"Image\"\n          \"price\" => \"Price\"\n          \"compare_at_price\" => \"Compare at Price\"\n          \"cost_price\" => \"Cost Price\"\n          \"barcode\" => \"UPC / Barcode\"\n          \"weight\" => \"Weight\"\n          \"weight_unit\" => \"Weight Unit\"\n          \"track_quantity\" => \"Track Quantity\"\n          \"continue_selling\" => \"Continue Selling\"\n        ]\n      ]\n      3 => array:2 [\n        \"name\" => \"Variant Option\"\n        \"attributes\" => array:6 [\n          \"option1_name\" => \"Option 1 Name\"\n          \"option1_value\" => \"Option 1 Value\"\n          \"option2_name\" => \"Option 2 Name\"\n          \"option2_value\" => \"Option 2 Value\"\n          \"option3_name\" => \"Option 3 Name\"\n          \"option3_value\" => \"Option 3 Value\"\n        ]\n      ]\n      4 => array:2 [\n        \"name\" => \"Inventory -> Tanzayb Store\"\n        \"attributes\" => array:1 [\n          8 => \"Tanzayb Store Warehouse\"\n        ]\n      ]\n      5 => array:2 [\n        \"name\" => \"SEO\"\n        \"attributes\" => array:4 [\n          \"seo_url\" => \"URL Slug\"\n          \"seo_title\" => \"SEO Title\"\n          \"seo_description\" => \"SEO Description\"\n          \"seo_keyword\" => \"Tags\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_output_array\" => array:6 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:5 [\n        0 => array:2 [\n          \"label\" => \"Product Identifier\"\n          \"value\" => \"Default,handle\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Product Images\"\n          \"value\" => \"Default,file\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Vendor\"\n          \"value\" => \"Default,vendor\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Brand\"\n          \"value\" => \"Default,brand\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Category\"\n          \"value\" => \"Default,categories\"\n        ]\n      ]\n    ]\n    1 => array:3 [\n      \"label\" => \"General\"\n      \"title\" => \"General\"\n      \"options\" => array:2 [\n        0 => array:2 [\n          \"label\" => \"Product Name\"\n          \"value\" => \"General,product_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Description\"\n          \"value\" => \"General,description\"\n        ]\n      ]\n    ]\n    2 => array:3 [\n      \"label\" => \"Variant\"\n      \"title\" => \"Variant\"\n      \"options\" => array:11 [\n        0 => array:2 [\n          \"label\" => \"SKU\"\n          \"value\" => \"Variant,sku\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Name\"\n          \"value\" => \"Variant,name\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Image\"\n          \"value\" => \"Variant,file\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Price\"\n          \"value\" => \"Variant,price\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Compare at Price\"\n          \"value\" => \"Variant,compare_at_price\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Cost Price\"\n          \"value\" => \"Variant,cost_price\"\n        ]\n        6 => array:2 [\n          \"label\" => \"UPC / Barcode\"\n          \"value\" => \"Variant,barcode\"\n        ]\n        7 => array:2 [\n          \"label\" => \"Weight\"\n          \"value\" => \"Variant,weight\"\n        ]\n        8 => array:2 [\n          \"label\" => \"Weight Unit\"\n          \"value\" => \"Variant,weight_unit\"\n        ]\n        9 => array:2 [\n          \"label\" => \"Track Quantity\"\n          \"value\" => \"Variant,track_quantity\"\n        ]\n        10 => array:2 [\n          \"label\" => \"Continue Selling\"\n          \"value\" => \"Variant,continue_selling\"\n        ]\n      ]\n    ]\n    3 => array:3 [\n      \"label\" => \"Variant Option\"\n      \"title\" => \"Variant Option\"\n      \"options\" => array:6 [\n        0 => array:2 [\n          \"label\" => \"Option 1 Name\"\n          \"value\" => \"Variant Option,option1_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Option 1 Value\"\n          \"value\" => \"Variant Option,option1_value\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Option 2 Name\"\n          \"value\" => \"Variant Option,option2_name\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Option 2 Value\"\n          \"value\" => \"Variant Option,option2_value\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Option 3 Name\"\n          \"value\" => \"Variant Option,option3_name\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Option 3 Value\"\n          \"value\" => \"Variant Option,option3_value\"\n        ]\n      ]\n    ]\n    4 => array:3 [\n      \"label\" => \"Inventory -> Tanzayb Store\"\n      \"title\" => \"Inventory -> Tanzayb Store\"\n      \"options\" => array:1 [\n        0 => array:2 [\n          \"label\" => \"Tanzayb Store Warehouse\"\n          \"value\" => \"Inventory -> Tanzayb Store,8\"\n        ]\n      ]\n    ]\n    5 => array:3 [\n      \"label\" => \"SEO\"\n      \"title\" => \"SEO\"\n      \"options\" => array:4 [\n        0 => array:2 [\n          \"label\" => \"URL Slug\"\n          \"value\" => \"SEO,seo_url\"\n        ]\n        1 => array:2 [\n          \"label\" => \"SEO Title\"\n          \"value\" => \"SEO,seo_title\"\n        ]\n        2 => array:2 [\n          \"label\" => \"SEO Description\"\n          \"value\" => \"SEO,seo_description\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Tags\"\n          \"value\" => \"SEO,seo_keyword\"\n        ]\n      ]\n    ]\n  ]\n  \"mapping_data\" => array:4 [\n    0 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Product Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"General,product_name\"\n      ]\n      \"id\" => \"e22c210b-4e41-4dcd-bbba-c99303fe7c64\"\n    ]\n    1 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,SKU\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Variant,sku\"\n      ]\n      \"id\" => \"69183295-f7c7-4565-9004-c8f139879ad3\"\n    ]\n    2 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Variant,price\"\n      ]\n      \"id\" => \"333633e9-0359-4206-a7ee-38c3cc2b9db7\"\n    ]\n    3 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Quantity\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"f482a0d5-47d0-47c6-96c6-ce18b4c2b7b5\"\n    ]\n  ]\n]", "success": "Template applied successfully"}, "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f02892b-8c83-41b7-b2c2-772a0e3e820f\" target=\"_blank\">View in Telescope</a>", "path_info": "/products/import/apply-template", "status_code": "<pre class=sf-dump id=sf-dump-745358588 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-745358588\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-965135201 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>template_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-965135201\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1461620433 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1461620433\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-483399184 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImYzRHVReGdQUjZhL3pQNW81d0pnT1E9PSIsInZhbHVlIjoiL0FaMEpmTy9EUnByd2VWd045aWJ6Mm9QTnZOYXFlRXRoc0hNZ1IzVGlsSzllMkVwWjV2eHZJRkR6aGd4b1FYTHhsQjdVeCsyYWVnMWh2UnhvUFEreGcyNjhlN25CdzN6QXU5NG1lcHB0QWM5WFZrenczeUs0T2ZYNVIwSHVtTHQiLCJtYWMiOiJjMGU1YzdjNDBiNGZiYWRhYTI4MGI2MTBjODlmMWE4OGY1MTZmNDJmNjA1OTg3ZDYyZjI3MjVkNDhmNDlhNDczIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">0f4e2ee0f7e2ca9da665d2f8035743df</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/products/import/step2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1317 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlBnUFZnSUIxd2RabTBsbnNiNVpmTUE9PSIsInZhbHVlIjoibEYxTENta2l3WE5hdnQ2UGVMeE9KU2lZdEE4YitHM1dsNlpJMDRpTUROUUtuc2FhU2l6Qnl4aklkYXZpK0l5WXhCT0g3Yi9UTE5XUXlpWUtUVFM4OXNkTG5BYnRtRlk2cUtuOUdKVC9NYmpQRmNSSWFCL20wSEVBekE0WVQ4L1dpZ21xVkE2bmJDblVGdWtZYmdhenprZ1J4NzFFZjFHWEs4NDhGM0l6ZkJ5RGJSRVAwVTFpSUFjNE5jOGdXY2JLcU02VWRjc2lwZWQxRzBHNlZSUFEydERmdW5CN3UvdGJwT2VkMDIwd08xQT0iLCJtYWMiOiJiYmM1ZTEzODhjYjA3MzUyMDE3Zjc0ZTI0YWE3N2RjNjUxYmJjOTgzOWZkYjJiNWI0ZTIzNWU0MjdiZjFlOTBhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImYzRHVReGdQUjZhL3pQNW81d0pnT1E9PSIsInZhbHVlIjoiL0FaMEpmTy9EUnByd2VWd045aWJ6Mm9QTnZOYXFlRXRoc0hNZ1IzVGlsSzllMkVwWjV2eHZJRkR6aGd4b1FYTHhsQjdVeCsyYWVnMWh2UnhvUFEreGcyNjhlN25CdzN6QXU5NG1lcHB0QWM5WFZrenczeUs0T2ZYNVIwSHVtTHQiLCJtYWMiOiJjMGU1YzdjNDBiNGZiYWRhYTI4MGI2MTBjODlmMWE4OGY1MTZmNDJmNjA1OTg3ZDYyZjI3MjVkNDhmNDlhNDczIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IkcxZEhaRVlVd3JFL3dkWjNRQ2FiV0E9PSIsInZhbHVlIjoibGt6TTZGR2dBbjdXT281M1k3L1VpMlRLb2ZGUUdPMVcyN29jSk00OUdlME43T282WGNvU2ZKNCtRK3ZTLzk3UEJrVENVaHM5UURaei9jeFJwZUYwWEM2aUJhSm9rSFFkNkhLOUtXQlB5UmRiTGt0dk1JeGlMZjQwckJ6L3pVeEYiLCJtYWMiOiIwMjFiOWYxMTFlYjRhNDJmOWU4N2NmNTA5YzM1OGJmNWVmNzVlYTRmZDJhMzVjZmQxYjBiOGYzZjRiZmEzYTExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-483399184\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-824509911 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">3|lyOnN9rFaNXD362RGbrGZMM746BZDyLiYeGFkfAHYF0FfGnJZdaE7RGsFJsg|$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k1WJWB6ZnSJcQ4dYcV3CZVMdsUzKOM46xMgpx7dI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-824509911\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1800746625 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 27 May 2025 07:53:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>vary</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">X-Inertia</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Inhxc1oxeFFWMjZuUk1RRWlzci9Lb3c9PSIsInZhbHVlIjoiVG9WTHB6S054bk03MXRVUVhqdHNVRnNoK2owRVMrVVlXM2Z3ZXFqV2VseUtrMVhvZGozc0ZUR2NQN211amhPenZ1ZmRUQlZtblFlRkFURWtncHZzd2hTVTJ4aXhRd0tjZi9KRGNSblJ4eno0NTNubXhtVXZ0am16dC94TWp5ZXAiLCJtYWMiOiI1MjgzNGVlNTlhYjM1YTQ5N2E5ZTYwYWM0YjVhMWNkNzAyZWRiNjlhYzA2OGU2MWFhNTljOTgyYjhjMTI4M2Q3IiwidGFnIjoiIn0%3D; expires=Tue, 27 May 2025 09:53:20 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6IkxHa2R2YjZBazFwVG9UZWJXNVlET2c9PSIsInZhbHVlIjoiSXkydThTZ1ZicG05WXAwWk52dS8zaEZJbGo4NUc1V2VjTGxiSkN0bmN2dmNQZElKSmZiZlg0cGp2SFFyalZITXJQNE1yQ0M1alp2Q3FPNlVVSU1jQ29yY09NZGxlTTNrY0FjaXVFVjEvaWNWWUhtZml5MDdzbC9iWHd0NTJkSm4iLCJtYWMiOiIyMGJkOGJkYzVkY2FiN2M5NjBjMzZiZmRjNzVkMWE1MWExMDdhNjYwMzA0OTI2ZWM4MzgyZTBiYzU1ODk0ZmVkIiwidGFnIjoiIn0%3D; expires=Tue, 27 May 2025 09:53:20 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Inhxc1oxeFFWMjZuUk1RRWlzci9Lb3c9PSIsInZhbHVlIjoiVG9WTHB6S054bk03MXRVUVhqdHNVRnNoK2owRVMrVVlXM2Z3ZXFqV2VseUtrMVhvZGozc0ZUR2NQN211amhPenZ1ZmRUQlZtblFlRkFURWtncHZzd2hTVTJ4aXhRd0tjZi9KRGNSblJ4eno0NTNubXhtVXZ0am16dC94TWp5ZXAiLCJtYWMiOiI1MjgzNGVlNTlhYjM1YTQ5N2E5ZTYwYWM0YjVhMWNkNzAyZWRiNjlhYzA2OGU2MWFhNTljOTgyYjhjMTI4M2Q3IiwidGFnIjoiIn0%3D; expires=Tue, 27-May-2025 09:53:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6IkxHa2R2YjZBazFwVG9UZWJXNVlET2c9PSIsInZhbHVlIjoiSXkydThTZ1ZicG05WXAwWk52dS8zaEZJbGo4NUc1V2VjTGxiSkN0bmN2dmNQZElKSmZiZlg0cGp2SFFyalZITXJQNE1yQ0M1alp2Q3FPNlVVSU1jQ29yY09NZGxlTTNrY0FjaXVFVjEvaWNWWUhtZml5MDdzbC9iWHd0NTJkSm4iLCJtYWMiOiIyMGJkOGJkYzVkY2FiN2M5NjBjMzZiZmRjNzVkMWE1MWExMDdhNjYwMzA0OTI2ZWM4MzgyZTBiYzU1ODk0ZmVkIiwidGFnIjoiIn0%3D; expires=Tue, 27-May-2025 09:53:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1800746625\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1675250356 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"67 characters\">http://localhost:8000/products/import/apply-template?template_id=22</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq</span>\"\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>input_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">CSV</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:228</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>Categories</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Categories</span>\"\n            \"<span class=sf-dump-key>Collection label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Collection label</span>\"\n            \"<span class=sf-dump-key>Product name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product name</span>\"\n            \"<span class=sf-dump-key>SKU</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>GTIN</span>\" => \"<span class=sf-dump-str title=\"4 characters\">GTIN</span>\"\n            \"<span class=sf-dump-key>Color</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Color</span>\"\n            \"<span class=sf-dump-key>Material</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Material</span>\"\n            \"<span class=sf-dump-key>Multiple consumer</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Multiple consumer</span>\"\n            \"<span class=sf-dump-key>Multiple required</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Multiple required</span>\"\n            \"<span class=sf-dump-key>Colli amount</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Colli amount</span>\"\n            \"<span class=sf-dump-key>Adjustment options</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Adjustment options</span>\"\n            \"<span class=sf-dump-key>Anti slip</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Anti slip</span>\"\n            \"<span class=sf-dump-key>Armrest Height</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Armrest Height</span>\"\n            \"<span class=sf-dump-key>Armrest Height (Unit)</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Armrest Height (Unit)</span>\"\n            \"<span class=sf-dump-key>With armrests</span>\" => \"<span class=sf-dump-str title=\"13 characters\">With armrests</span>\"\n            \"<span class=sf-dump-key>Width armrest</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Width armrest</span>\"\n            \"<span class=sf-dump-key>Width armrest (Unit)</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Width armrest (Unit)</span>\"\n            \"<span class=sf-dump-key>Adjustable backrest</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Adjustable backrest</span>\"\n            \"<span class=sf-dump-key>With backrests</span>\" => \"<span class=sf-dump-str title=\"14 characters\">With backrests</span>\"\n            \"<span class=sf-dump-key>[baseColor]</span>\" => \"<span class=sf-dump-str title=\"11 characters\">[baseColor]</span>\"\n            \"<span class=sf-dump-key>Material base</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Material base</span>\"\n            \"<span class=sf-dump-key>Base type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Base type</span>\"\n            \"<span class=sf-dump-key>Battery amount</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Battery amount</span>\"\n            \"<span class=sf-dump-key>Battery info</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Battery info</span>\"\n            \"<span class=sf-dump-key>Body size</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Body size</span>\"\n            \"<span class=sf-dump-key>Body size (Unit)</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Body size (Unit)</span>\"\n            \"<span class=sf-dump-key>Cabinet type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Cabinet type</span>\"\n            \"<span class=sf-dump-key>Cable Color</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Cable Color</span>\"\n            \"<span class=sf-dump-key>Material cable</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Material cable</span>\"\n            \"<span class=sf-dump-key>Suitable for number of candles</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Suitable for number of candles</span>\"\n            \"<span class=sf-dump-key>CE Marking</span>\" => \"<span class=sf-dump-str title=\"10 characters\">CE Marking</span>\"\n            \"<span class=sf-dump-key>Max. watt CFL</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Max. watt CFL</span>\"\n            \"<span class=sf-dump-key>Max. watt CFL (Unit)</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Max. watt CFL (Unit)</span>\"\n            \"<span class=sf-dump-key>Chair type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Chair type</span>\"\n            \"<span class=sf-dump-key>Coat rack type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Coat rack type</span>\"\n            \"<span class=sf-dump-key>HS code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">HS code</span>\"\n            \"<span class=sf-dump-key>HS code U.S.A.</span>\" => \"<span class=sf-dump-str title=\"14 characters\">HS code U.S.A.</span>\"\n            \"<span class=sf-dump-key>Colour Fastness</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Colour Fastness</span>\"\n            \"<span class=sf-dump-key>Connection voltage</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Connection voltage</span>\"\n            \"<span class=sf-dump-key>Connection voltage (Unit)</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Connection voltage (Unit)</span>\"\n            \"<span class=sf-dump-key>Includes MDF</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Includes MDF</span>\"\n            \"<span class=sf-dump-key>Sofa type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Sofa type</span>\"\n            \"<span class=sf-dump-key>Country of Origin</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Country of Origin</span>\"\n            \"<span class=sf-dump-key>Cover detachable</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Cover detachable</span>\"\n            \"<span class=sf-dump-key>Product description Google</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Product description Google</span>\"\n            \"<span class=sf-dump-key>Dimmable</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Dimmable</span>\"\n            \"<span class=sf-dump-key>Dining chair type</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Dining chair type</span>\"\n            \"<span class=sf-dump-key>Door type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Door type</span>\"\n            \"<span class=sf-dump-key>Drawer amount</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Drawer amount</span>\"\n            \"<span class=sf-dump-key>[drawerIncluded]</span>\" => \"<span class=sf-dump-str title=\"16 characters\">[drawerIncluded]</span>\"\n            \"<span class=sf-dump-key>Embargo date</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Embargo date</span>\"\n            \"<span class=sf-dump-key>Energy Class</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Energy Class</span>\"\n            \"<span class=sf-dump-key>Ergonomic</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Ergonomic</span>\"\n            \"<span class=sf-dump-key>Mounting plate/foot height</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Mounting plate/foot height</span>\"\n            \"<span class=sf-dump-key>Mounting plate/foot height (Unit)</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Mounting plate/foot height (Unit)</span>\"\n            \"<span class=sf-dump-key>Fire retardant</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Fire retardant</span>\"\n            \"<span class=sf-dump-key>Fitting</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Fitting</span>\"\n            \"<span class=sf-dump-key>Flask amount</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Flask amount</span>\"\n            \"<span class=sf-dump-key>Foam density</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Foam density</span>\"\n            \"<span class=sf-dump-key>Furniture finish</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Furniture finish</span>\"\n            \"<span class=sf-dump-key>Garden chair type</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Garden chair type</span>\"\n            \"<span class=sf-dump-key>Gross weight in kg</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Gross weight in kg</span>\"\n            \"<span class=sf-dump-key>Height Adjustable</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Height Adjustable</span>\"\n            \"<span class=sf-dump-key>[hockerIncluded]</span>\" => \"<span class=sf-dump-str title=\"16 characters\">[hockerIncluded]</span>\"\n            \"<span class=sf-dump-key>Hook amount</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Hook amount</span>\"\n            \"<span class=sf-dump-key>Suitable for Indoor/Outdoor</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Suitable for Indoor/Outdoor</span>\"\n            \"<span class=sf-dump-key>IP Value</span>\" => \"<span class=sf-dump-str title=\"8 characters\">IP Value</span>\"\n            \"<span class=sf-dump-key>Kelvin</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Kelvin</span>\"\n            \"<span class=sf-dump-key>Kelvin (Unit)</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Kelvin (Unit)</span>\"\n            \"<span class=sf-dump-key>Diameter lamp shade</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Diameter lamp shade</span>\"\n            \"<span class=sf-dump-key>Diameter lamp shade (Unit)</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Diameter lamp shade (Unit)</span>\"\n            \"<span class=sf-dump-key>Lamp shade Height</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Lamp shade Height</span>\"\n            \"<span class=sf-dump-key>Lamp shade Height (Unit)</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Lamp shade Height (Unit)</span>\"\n            \"<span class=sf-dump-key>Lamp shade Length</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Lamp shade Length</span>\"\n            \"<span class=sf-dump-key>Lamp shade Length (Unit)</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Lamp shade Length (Unit)</span>\"\n            \"<span class=sf-dump-key>Width shade</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Width shade</span>\"\n            \"<span class=sf-dump-key>Width shade (Unit)</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Width shade (Unit)</span>\"\n            \"<span class=sf-dump-key>Max. watt LED</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Max. watt LED</span>\"\n            \"<span class=sf-dump-key>Max. watt LED (Unit)</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Max. watt LED (Unit)</span>\"\n            \"<span class=sf-dump-key>Leg Distance</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Leg Distance</span>\"\n            \"<span class=sf-dump-key>Leg Distance (Unit)</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Leg Distance (Unit)</span>\"\n            \"<span class=sf-dump-key>Leg height</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Leg height</span>\"\n            \"<span class=sf-dump-key>Leg height (Unit)</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Leg height (Unit)</span>\"\n            \"<span class=sf-dump-key>Light color</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Light color</span>\"\n            \"<span class=sf-dump-key>Light direction</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Light direction</span>\"\n            \"<span class=sf-dump-key>Lighting control</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Lighting control</span>\"\n            \"<span class=sf-dump-key>Light source amount</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Light source amount</span>\"\n            \"<span class=sf-dump-key>Light Source Included</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Light Source Included</span>\"\n            \"<span class=sf-dump-key>Light Source Lifespan</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Light Source Lifespan</span>\"\n            \"<span class=sf-dump-key>Light Source Lifespan (Unit)</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Light Source Lifespan (Unit)</span>\"\n            \"<span class=sf-dump-key>Lumen per light source</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Lumen per light source</span>\"\n            \"<span class=sf-dump-key>Lumen per light source (Unit)</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Lumen per light source (Unit)</span>\"\n            \"<span class=sf-dump-key>Light source removable</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Light source removable</span>\"\n            \"<span class=sf-dump-key>max. loadable weight</span>\" => \"<span class=sf-dump-str title=\"20 characters\">max. loadable weight</span>\"\n            \"<span class=sf-dump-key>max. loadable weight (Unit)</span>\" => \"<span class=sf-dump-str title=\"27 characters\">max. loadable weight (Unit)</span>\"\n            \"<span class=sf-dump-key>Logistic height in cm</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Logistic height in cm</span>\"\n            \"<span class=sf-dump-key>Logistic length in cm</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Logistic length in cm</span>\"\n            \"<span class=sf-dump-key>Logistic width in cm</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Logistic width in cm</span>\"\n            \"<span class=sf-dump-key>&quot;Long description (English United States</span>\" => \"<span class=sf-dump-str title=\"40 characters\">&quot;Long description (English United States</span>\"\n            \"<span class=sf-dump-key>Dealer login)&quot;</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Dealer login)&quot;</span>\"\n            \"<span class=sf-dump-key>&quot;Long description (Dutch Netherlands</span>\" => \"<span class=sf-dump-str title=\"36 characters\">&quot;Long description (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>Maintenance advice</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Maintenance advice</span>\"\n            \"<span class=sf-dump-key>Martindale</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Martindale</span>\"\n            \"<span class=sf-dump-key>Material body light</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Material body light</span>\"\n            \"<span class=sf-dump-key>Material composition</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Material composition</span>\"\n            \"<span class=sf-dump-key>Material cushion</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Material cushion</span>\"\n            \"<span class=sf-dump-key>Material carpet</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Material carpet</span>\"\n            \"<span class=sf-dump-key>Material shade</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Material shade</span>\"\n            \"<span class=sf-dump-key>Meta title (English United States)</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Meta title (English United States)</span>\"\n            \"<span class=sf-dump-key>Meta title (Dutch Netherlands)</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Meta title (Dutch Netherlands)</span>\"\n            \"<span class=sf-dump-key>Mirror type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Mirror type</span>\"\n            \"<span class=sf-dump-key>Length of mounting plate/foot</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Length of mounting plate/foot</span>\"\n            \"<span class=sf-dump-key>Length of mounting plate/foot (Unit)</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Length of mounting plate/foot (Unit)</span>\"\n            \"<span class=sf-dump-key>Width of mounting plate/feet</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Width of mounting plate/feet</span>\"\n            \"<span class=sf-dump-key>Width of mounting plate/feet (Unit)</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Width of mounting plate/feet (Unit)</span>\"\n            \"<span class=sf-dump-key>Netto Weight Article</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Netto Weight Article</span>\"\n            \"<span class=sf-dump-key>Netto Weight Article (Unit)</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Netto Weight Article (Unit)</span>\"\n            \"<span class=sf-dump-key>Net weight in kg</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Net weight in kg</span>\"\n            \"<span class=sf-dump-key>Office chair type</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Office chair type</span>\"\n            \"<span class=sf-dump-key>Other dimensions</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Other dimensions</span>\"\n            \"<span class=sf-dump-key>Outer Packaging Gross Volume</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Outer Packaging Gross Volume</span>\"\n            \"<span class=sf-dump-key>Packaging Gross Weight (in KG)</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Packaging Gross Weight (in KG)</span>\"\n            \"<span class=sf-dump-key>GTIN Outer Packaging</span>\" => \"<span class=sf-dump-str title=\"20 characters\">GTIN Outer Packaging</span>\"\n            \"<span class=sf-dump-key>Outer Packaging Width</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Outer Packaging Width</span>\"\n            \"<span class=sf-dump-key>Outer Packaging Length</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Outer Packaging Length</span>\"\n            \"<span class=sf-dump-key>Outer Packaging Height</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Outer Packaging Height</span>\"\n            \"<span class=sf-dump-key>Packing net weight of solid wood in kg</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Packing net weight of solid wood in kg</span>\"\n            \"<span class=sf-dump-key>Packaging net weight of paper in kg</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Packaging net weight of paper in kg</span>\"\n            \"<span class=sf-dump-key>Packaging net weight plastic in kg</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Packaging net weight plastic in kg</span>\"\n            \"<span class=sf-dump-key>Packing net weight of gel/gep wood in kg</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Packing net weight of gel/gep wood in kg</span>\"\n            \"<span class=sf-dump-key>Packing net weight styrofoam in kg</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Packing net weight styrofoam in kg</span>\"\n            \"<span class=sf-dump-key>Packing net weight of textile in kg</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Packing net weight of textile in kg</span>\"\n            \"<span class=sf-dump-key>Packaging number</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Packaging number</span>\"\n            \"<span class=sf-dump-key>Pattern</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Pattern</span>\"\n            \"<span class=sf-dump-key>Suitable for number of persons</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Suitable for number of persons</span>\"\n            \"<span class=sf-dump-key>PLC state</span>\" => \"<span class=sf-dump-str title=\"9 characters\">PLC state</span>\"\n            \"<span class=sf-dump-key>Pole height</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Pole height</span>\"\n            \"<span class=sf-dump-key>Power type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Power type</span>\"\n            \"<span class=sf-dump-key>Pre-assembled</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Pre-assembled</span>\"\n            \"<span class=sf-dump-key>&quot;Product bulletpoint 1 (English United States</span>\" => \"<span class=sf-dump-str title=\"45 characters\">&quot;Product bulletpoint 1 (English United States</span>\"\n            \"<span class=sf-dump-key>&quot;Product bulletpoint 1 (Dutch Netherlands</span>\" => \"<span class=sf-dump-str title=\"41 characters\">&quot;Product bulletpoint 1 (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>&quot;Product bulletpoint 2 (English United States</span>\" => \"<span class=sf-dump-str title=\"45 characters\">&quot;Product bulletpoint 2 (English United States</span>\"\n            \"<span class=sf-dump-key>&quot;Product bulletpoint 2 (Dutch Netherlands</span>\" => \"<span class=sf-dump-str title=\"41 characters\">&quot;Product bulletpoint 2 (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>&quot;Product bulletpoint 3 (English United States</span>\" => \"<span class=sf-dump-str title=\"45 characters\">&quot;Product bulletpoint 3 (English United States</span>\"\n            \"<span class=sf-dump-key>&quot;Product bulletpoint 3 (Dutch Netherlands</span>\" => \"<span class=sf-dump-str title=\"41 characters\">&quot;Product bulletpoint 3 (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>&quot;Product bulletpoint 4 (English United States</span>\" => \"<span class=sf-dump-str title=\"45 characters\">&quot;Product bulletpoint 4 (English United States</span>\"\n            \"<span class=sf-dump-key>&quot;Product bulletpoint 4 (Dutch Netherlands</span>\" => \"<span class=sf-dump-str title=\"41 characters\">&quot;Product bulletpoint 4 (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>&quot;Product bulletpoint 5 (English United States</span>\" => \"<span class=sf-dump-str title=\"45 characters\">&quot;Product bulletpoint 5 (English United States</span>\"\n            \"<span class=sf-dump-key>&quot;Product bulletpoint 5 (Dutch Netherlands</span>\" => \"<span class=sf-dump-str title=\"41 characters\">&quot;Product bulletpoint 5 (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>Product depth</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Product depth</span>\"\n            \"<span class=sf-dump-key>Product depth (Unit)</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Product depth (Unit)</span>\"\n            \"<span class=sf-dump-key>Product Description</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Product Description</span>\"\n            \"<span class=sf-dump-key>Product diameter</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Product diameter</span>\"\n            \"<span class=sf-dump-key>Product diameter (Unit)</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Product diameter (Unit)</span>\"\n            \"<span class=sf-dump-key>Product Group By Attribute(s)</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Product Group By Attribute(s)</span>\"\n            \"<span class=sf-dump-key>Product height</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product height</span>\"\n            \"<span class=sf-dump-key>Product height (Unit)</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Product height (Unit)</span>\"\n            \"<span class=sf-dump-key>Product length</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product length</span>\"\n            \"<span class=sf-dump-key>Product length (Unit)</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Product length (Unit)</span>\"\n            \"<span class=sf-dump-key>&quot;Product name (English United States</span>\" => \"<span class=sf-dump-str title=\"36 characters\">&quot;Product name (English United States</span>\"\n            \"<span class=sf-dump-key>&quot;Product name (Dutch Netherlands</span>\" => \"<span class=sf-dump-str title=\"32 characters\">&quot;Product name (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>Product type (normal/KIT/Special)</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Product type (normal/KIT/Special)</span>\"\n            \"<span class=sf-dump-key>Product width</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Product width</span>\"\n            \"<span class=sf-dump-key>Product width (Unit)</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Product width (Unit)</span>\"\n            \"<span class=sf-dump-key>Quality Mark</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Quality Mark</span>\"\n            \"<span class=sf-dump-key>Suitable for area</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Suitable for area</span>\"\n            \"<span class=sf-dump-key>Rotatable</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Rotatable</span>\"\n            \"<span class=sf-dump-key>Material backing carpet</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Material backing carpet</span>\"\n            \"<span class=sf-dump-key>Safety Class</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Safety Class</span>\"\n            \"<span class=sf-dump-key>Drop</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Drop</span>\"\n            \"<span class=sf-dump-key>Drop year</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Drop year</span>\"\n            \"<span class=sf-dump-key>Seat amount</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Seat amount</span>\"\n            \"<span class=sf-dump-key>Seat color</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Seat color</span>\"\n            \"<span class=sf-dump-key>Seat depth</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Seat depth</span>\"\n            \"<span class=sf-dump-key>Seat depth (Unit)</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Seat depth (Unit)</span>\"\n            \"<span class=sf-dump-key>Seat height</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Seat height</span>\"\n            \"<span class=sf-dump-key>Seat height (Unit)</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Seat height (Unit)</span>\"\n            \"<span class=sf-dump-key>Seating comfort</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Seating comfort</span>\"\n            \"<span class=sf-dump-key>Material seat</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Material seat</span>\"\n            \"<span class=sf-dump-key>Seat width</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Seat width</span>\"\n            \"<span class=sf-dump-key>Seat width (Unit)</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Seat width (Unit)</span>\"\n            \"<span class=sf-dump-key>Serial name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Serial name</span>\"\n            \"<span class=sf-dump-key>Set Contains</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Set Contains</span>\"\n            \"<span class=sf-dump-key>Shape</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Shape</span>\"\n            \"<span class=sf-dump-key>Shelf amount</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Shelf amount</span>\"\n            \"<span class=sf-dump-key>Max. loadable weight shelf</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Max. loadable weight shelf</span>\"\n            \"<span class=sf-dump-key>Max. loadable weight shelf (Unit)</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Max. loadable weight shelf (Unit)</span>\"\n            \"<span class=sf-dump-key>&quot;Short description (English United States</span>\" => \"<span class=sf-dump-str title=\"41 characters\">&quot;Short description (English United States</span>\"\n            \"<span class=sf-dump-key>&quot;Short description (Dutch Netherlands</span>\" => \"<span class=sf-dump-str title=\"37 characters\">&quot;Short description (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>Position corner sofa</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Position corner sofa</span>\"\n            \"<span class=sf-dump-key>Stack able</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Stack able</span>\"\n            \"<span class=sf-dump-key>Stuffing material</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Stuffing material</span>\"\n            \"<span class=sf-dump-key>Suitable for underfloor heating</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Suitable for underfloor heating</span>\"\n            \"<span class=sf-dump-key>Trade name and contact address of manufacturer or importer in EU</span>\" => \"<span class=sf-dump-str title=\"64 characters\">Trade name and contact address of manufacturer or importer in EU</span>\"\n            \"<span class=sf-dump-key>Type of surface mounted spotlight</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Type of surface mounted spotlight</span>\"\n            \"<span class=sf-dump-key>Extendable table top</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Extendable table top</span>\"\n            \"<span class=sf-dump-key>Table top finish</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Table top finish</span>\"\n            \"<span class=sf-dump-key>Material table top</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Material table top</span>\"\n            \"<span class=sf-dump-key>Table type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Table type</span>\"\n            \"<span class=sf-dump-key>Upholstered</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Upholstered</span>\"\n            \"<span class=sf-dump-key>Integrated USB port</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Integrated USB port</span>\"\n            \"<span class=sf-dump-key>VAT Code (H)</span>\" => \"<span class=sf-dump-str title=\"12 characters\">VAT Code (H)</span>\"\n            \"<span class=sf-dump-key>Wall mount</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Wall mount</span>\"\n            \"<span class=sf-dump-key>Washing instructions</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Washing instructions</span>\"\n            \"<span class=sf-dump-key>Silent clockwork</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Silent clockwork</span>\"\n            \"<span class=sf-dump-key>Waterproof</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Waterproof</span>\"\n            \"<span class=sf-dump-key>With wheels</span>\" => \"<span class=sf-dump-str title=\"11 characters\">With wheels</span>\"\n            \"<span class=sf-dump-key>Wire length</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Wire length</span>\"\n            \"<span class=sf-dump-key>Wire length (Unit)</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Wire length (Unit)</span>\"\n            \"<span class=sf-dump-key>Wire with plug included</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Wire with plug included</span>\"\n            \"<span class=sf-dump-key>Asset collection images</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Asset collection images</span>\"\n            \"<span class=sf-dump-key>[assetCollectionImages-file_path]</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[assetCollectionImages-file_path]</span>\"\n            \"<span class=sf-dump-key>Image URL 1</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 1</span>\"\n            \"<span class=sf-dump-key>Image URL 2</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 2</span>\"\n            \"<span class=sf-dump-key>Image URL 3</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 3</span>\"\n            \"<span class=sf-dump-key>Image URL 4</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 4</span>\"\n            \"<span class=sf-dump-key>Image URL 5</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 5</span>\"\n            \"<span class=sf-dump-key>Image URL 6</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 6</span>\"\n            \"<span class=sf-dump-key>Image URL 7</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 7</span>\"\n            \"<span class=sf-dump-key>Image URL 8</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 8</span>\"\n            \"<span class=sf-dump-key>Image URL 9</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 9</span>\"\n            \"<span class=sf-dump-key>Image URL 10</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Image URL 10</span>\"\n            \"<span class=sf-dump-key>Image URL 11</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Image URL 11</span>\"\n            \"<span class=sf-dump-key>Image URL 12</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Image URL 12</span>\"\n            \"<span class=sf-dump-key>Image URL 13</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Image URL 13</span>\"\n            \"<span class=sf-dump-key>Image URL 14</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Image URL 14</span>\"\n            \"<span class=sf-dump-key>Image URL 15</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Image URL 15</span>\"\n            \"\" => \"\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_input_array</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:228</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Categories</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,Categories</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Collection label</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Collection label</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Product name</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Default,SKU</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">GTIN</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,GTIN</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Color</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,Color</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Material</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Default,Material</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Multiple consumer</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Default,Multiple consumer</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Multiple required</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Default,Multiple required</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Colli amount</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Colli amount</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Adjustment options</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Default,Adjustment options</span>\"\n          </samp>]\n          <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Anti slip</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Anti slip</span>\"\n          </samp>]\n          <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Armrest Height</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Armrest Height</span>\"\n          </samp>]\n          <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Armrest Height (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Armrest Height (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">With armrests</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,With armrests</span>\"\n          </samp>]\n          <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Width armrest</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Width armrest</span>\"\n          </samp>]\n          <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Width armrest (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Default,Width armrest (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Adjustable backrest</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Default,Adjustable backrest</span>\"\n          </samp>]\n          <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">With backrests</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,With backrests</span>\"\n          </samp>]\n          <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">[baseColor]</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,[baseColor]</span>\"\n          </samp>]\n          <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Material base</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Material base</span>\"\n          </samp>]\n          <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Base type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Base type</span>\"\n          </samp>]\n          <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Battery amount</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Battery amount</span>\"\n          </samp>]\n          <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Battery info</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Battery info</span>\"\n          </samp>]\n          <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Body size</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Body size</span>\"\n          </samp>]\n          <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Body size (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Body size (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Cabinet type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Cabinet type</span>\"\n          </samp>]\n          <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Cable Color</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Cable Color</span>\"\n          </samp>]\n          <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Material cable</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Material cable</span>\"\n          </samp>]\n          <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Suitable for number of candles</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Default,Suitable for number of candles</span>\"\n          </samp>]\n          <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">CE Marking</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,CE Marking</span>\"\n          </samp>]\n          <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Max. watt CFL</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Max. watt CFL</span>\"\n          </samp>]\n          <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Max. watt CFL (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Default,Max. watt CFL (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Chair type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,Chair type</span>\"\n          </samp>]\n          <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Coat rack type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Coat rack type</span>\"\n          </samp>]\n          <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">HS code</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Default,HS code</span>\"\n          </samp>]\n          <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">HS code U.S.A.</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,HS code U.S.A.</span>\"\n          </samp>]\n          <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Colour Fastness</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,Colour Fastness</span>\"\n          </samp>]\n          <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Connection voltage</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Default,Connection voltage</span>\"\n          </samp>]\n          <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Connection voltage (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Default,Connection voltage (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Includes MDF</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Includes MDF</span>\"\n          </samp>]\n          <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Sofa type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Sofa type</span>\"\n          </samp>]\n          <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Country of Origin</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Default,Country of Origin</span>\"\n          </samp>]\n          <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Cover detachable</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Cover detachable</span>\"\n          </samp>]\n          <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Product description Google</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Default,Product description Google</span>\"\n          </samp>]\n          <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Dimmable</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Default,Dimmable</span>\"\n          </samp>]\n          <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Dining chair type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Default,Dining chair type</span>\"\n          </samp>]\n          <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Door type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Door type</span>\"\n          </samp>]\n          <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Drawer amount</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Drawer amount</span>\"\n          </samp>]\n          <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">[drawerIncluded]</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,[drawerIncluded]</span>\"\n          </samp>]\n          <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Embargo date</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Embargo date</span>\"\n          </samp>]\n          <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Energy Class</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Energy Class</span>\"\n          </samp>]\n          <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Ergonomic</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Ergonomic</span>\"\n          </samp>]\n          <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Mounting plate/foot height</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Default,Mounting plate/foot height</span>\"\n          </samp>]\n          <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Mounting plate/foot height (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Default,Mounting plate/foot height (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Fire retardant</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Fire retardant</span>\"\n          </samp>]\n          <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Fitting</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Default,Fitting</span>\"\n          </samp>]\n          <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Flask amount</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Flask amount</span>\"\n          </samp>]\n          <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Foam density</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Foam density</span>\"\n          </samp>]\n          <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Furniture finish</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Furniture finish</span>\"\n          </samp>]\n          <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Garden chair type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Default,Garden chair type</span>\"\n          </samp>]\n          <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Gross weight in kg</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Default,Gross weight in kg</span>\"\n          </samp>]\n          <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Height Adjustable</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Default,Height Adjustable</span>\"\n          </samp>]\n          <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">[hockerIncluded]</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,[hockerIncluded]</span>\"\n          </samp>]\n          <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Hook amount</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Hook amount</span>\"\n          </samp>]\n          <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Suitable for Indoor/Outdoor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Suitable for Indoor/Outdoor</span>\"\n          </samp>]\n          <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">IP Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Default,IP Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Kelvin</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Kelvin</span>\"\n          </samp>]\n          <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Kelvin (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Kelvin (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Diameter lamp shade</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Default,Diameter lamp shade</span>\"\n          </samp>]\n          <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Diameter lamp shade (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Default,Diameter lamp shade (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Lamp shade Height</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Default,Lamp shade Height</span>\"\n          </samp>]\n          <span class=sf-dump-index>72</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Lamp shade Height (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Lamp shade Height (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>73</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Lamp shade Length</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Default,Lamp shade Length</span>\"\n          </samp>]\n          <span class=sf-dump-index>74</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Lamp shade Length (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Lamp shade Length (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>75</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Width shade</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Width shade</span>\"\n          </samp>]\n          <span class=sf-dump-index>76</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Width shade (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Default,Width shade (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>77</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Max. watt LED</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Max. watt LED</span>\"\n          </samp>]\n          <span class=sf-dump-index>78</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Max. watt LED (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Default,Max. watt LED (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>79</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Leg Distance</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Leg Distance</span>\"\n          </samp>]\n          <span class=sf-dump-index>80</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Leg Distance (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Default,Leg Distance (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>81</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Leg height</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,Leg height</span>\"\n          </samp>]\n          <span class=sf-dump-index>82</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Leg height (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Default,Leg height (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>83</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Light color</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Light color</span>\"\n          </samp>]\n          <span class=sf-dump-index>84</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Light direction</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,Light direction</span>\"\n          </samp>]\n          <span class=sf-dump-index>85</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Lighting control</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Lighting control</span>\"\n          </samp>]\n          <span class=sf-dump-index>86</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Light source amount</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Default,Light source amount</span>\"\n          </samp>]\n          <span class=sf-dump-index>87</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Light Source Included</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Light Source Included</span>\"\n          </samp>]\n          <span class=sf-dump-index>88</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Light Source Lifespan</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Light Source Lifespan</span>\"\n          </samp>]\n          <span class=sf-dump-index>89</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Light Source Lifespan (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Default,Light Source Lifespan (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>90</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Lumen per light source</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Default,Lumen per light source</span>\"\n          </samp>]\n          <span class=sf-dump-index>91</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Lumen per light source (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Default,Lumen per light source (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>92</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Light source removable</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Default,Light source removable</span>\"\n          </samp>]\n          <span class=sf-dump-index>93</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"20 characters\">max. loadable weight</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Default,max. loadable weight</span>\"\n          </samp>]\n          <span class=sf-dump-index>94</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">max. loadable weight (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,max. loadable weight (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>95</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Logistic height in cm</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Logistic height in cm</span>\"\n          </samp>]\n          <span class=sf-dump-index>96</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Logistic length in cm</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Logistic length in cm</span>\"\n          </samp>]\n          <span class=sf-dump-index>97</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Logistic width in cm</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Default,Logistic width in cm</span>\"\n          </samp>]\n          <span class=sf-dump-index>98</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"40 characters\">&quot;Long description (English United States</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Default,&quot;Long description (English United States</span>\"\n          </samp>]\n          <span class=sf-dump-index>99</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Dealer login)&quot;</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Dealer login)&quot;</span>\"\n          </samp>]\n          <span class=sf-dump-index>100</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"36 characters\">&quot;Long description (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Default,&quot;Long description (Dutch Netherlands</span>\"\n          </samp>]\n          <span class=sf-dump-index>101</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Maintenance advice</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Default,Maintenance advice</span>\"\n          </samp>]\n          <span class=sf-dump-index>102</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Martindale</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,Martindale</span>\"\n          </samp>]\n          <span class=sf-dump-index>103</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Material body light</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Default,Material body light</span>\"\n          </samp>]\n          <span class=sf-dump-index>104</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Material composition</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Default,Material composition</span>\"\n          </samp>]\n          <span class=sf-dump-index>105</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Material cushion</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Material cushion</span>\"\n          </samp>]\n          <span class=sf-dump-index>106</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Material carpet</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,Material carpet</span>\"\n          </samp>]\n          <span class=sf-dump-index>107</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Material shade</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Material shade</span>\"\n          </samp>]\n          <span class=sf-dump-index>108</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Meta title (English United States)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Default,Meta title (English United States)</span>\"\n          </samp>]\n          <span class=sf-dump-index>109</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Meta title (Dutch Netherlands)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Default,Meta title (Dutch Netherlands)</span>\"\n          </samp>]\n          <span class=sf-dump-index>110</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Mirror type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Mirror type</span>\"\n          </samp>]\n          <span class=sf-dump-index>111</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Length of mounting plate/foot</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Default,Length of mounting plate/foot</span>\"\n          </samp>]\n          <span class=sf-dump-index>112</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Length of mounting plate/foot (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Default,Length of mounting plate/foot (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>113</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Width of mounting plate/feet</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Default,Width of mounting plate/feet</span>\"\n          </samp>]\n          <span class=sf-dump-index>114</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Width of mounting plate/feet (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Default,Width of mounting plate/feet (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>115</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Netto Weight Article</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Default,Netto Weight Article</span>\"\n          </samp>]\n          <span class=sf-dump-index>116</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Netto Weight Article (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Netto Weight Article (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>117</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Net weight in kg</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Net weight in kg</span>\"\n          </samp>]\n          <span class=sf-dump-index>118</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Office chair type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Default,Office chair type</span>\"\n          </samp>]\n          <span class=sf-dump-index>119</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Other dimensions</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Other dimensions</span>\"\n          </samp>]\n          <span class=sf-dump-index>120</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Outer Packaging Gross Volume</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Default,Outer Packaging Gross Volume</span>\"\n          </samp>]\n          <span class=sf-dump-index>121</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Packaging Gross Weight (in KG)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Default,Packaging Gross Weight (in KG)</span>\"\n          </samp>]\n          <span class=sf-dump-index>122</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"20 characters\">GTIN Outer Packaging</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Default,GTIN Outer Packaging</span>\"\n          </samp>]\n          <span class=sf-dump-index>123</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Outer Packaging Width</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Outer Packaging Width</span>\"\n          </samp>]\n          <span class=sf-dump-index>124</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Outer Packaging Length</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Default,Outer Packaging Length</span>\"\n          </samp>]\n          <span class=sf-dump-index>125</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Outer Packaging Height</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Default,Outer Packaging Height</span>\"\n          </samp>]\n          <span class=sf-dump-index>126</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Packing net weight of solid wood in kg</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Default,Packing net weight of solid wood in kg</span>\"\n          </samp>]\n          <span class=sf-dump-index>127</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Packaging net weight of paper in kg</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Default,Packaging net weight of paper in kg</span>\"\n          </samp>]\n          <span class=sf-dump-index>128</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Packaging net weight plastic in kg</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Default,Packaging net weight plastic in kg</span>\"\n          </samp>]\n          <span class=sf-dump-index>129</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Packing net weight of gel/gep wood in kg</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Default,Packing net weight of gel/gep wood in kg</span>\"\n          </samp>]\n          <span class=sf-dump-index>130</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Packing net weight styrofoam in kg</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Default,Packing net weight styrofoam in kg</span>\"\n          </samp>]\n          <span class=sf-dump-index>131</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Packing net weight of textile in kg</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Default,Packing net weight of textile in kg</span>\"\n          </samp>]\n          <span class=sf-dump-index>132</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Packaging number</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Packaging number</span>\"\n          </samp>]\n          <span class=sf-dump-index>133</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Pattern</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Default,Pattern</span>\"\n          </samp>]\n          <span class=sf-dump-index>134</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Suitable for number of persons</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Default,Suitable for number of persons</span>\"\n          </samp>]\n          <span class=sf-dump-index>135</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">PLC state</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,PLC state</span>\"\n          </samp>]\n          <span class=sf-dump-index>136</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Pole height</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Pole height</span>\"\n          </samp>]\n          <span class=sf-dump-index>137</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Power type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,Power type</span>\"\n          </samp>]\n          <span class=sf-dump-index>138</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Pre-assembled</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Pre-assembled</span>\"\n          </samp>]\n          <span class=sf-dump-index>139</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"45 characters\">&quot;Product bulletpoint 1 (English United States</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Default,&quot;Product bulletpoint 1 (English United States</span>\"\n          </samp>]\n          <span class=sf-dump-index>140</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"41 characters\">&quot;Product bulletpoint 1 (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Default,&quot;Product bulletpoint 1 (Dutch Netherlands</span>\"\n          </samp>]\n          <span class=sf-dump-index>141</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"45 characters\">&quot;Product bulletpoint 2 (English United States</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Default,&quot;Product bulletpoint 2 (English United States</span>\"\n          </samp>]\n          <span class=sf-dump-index>142</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"41 characters\">&quot;Product bulletpoint 2 (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Default,&quot;Product bulletpoint 2 (Dutch Netherlands</span>\"\n          </samp>]\n          <span class=sf-dump-index>143</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"45 characters\">&quot;Product bulletpoint 3 (English United States</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Default,&quot;Product bulletpoint 3 (English United States</span>\"\n          </samp>]\n          <span class=sf-dump-index>144</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"41 characters\">&quot;Product bulletpoint 3 (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Default,&quot;Product bulletpoint 3 (Dutch Netherlands</span>\"\n          </samp>]\n          <span class=sf-dump-index>145</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"45 characters\">&quot;Product bulletpoint 4 (English United States</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Default,&quot;Product bulletpoint 4 (English United States</span>\"\n          </samp>]\n          <span class=sf-dump-index>146</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"41 characters\">&quot;Product bulletpoint 4 (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Default,&quot;Product bulletpoint 4 (Dutch Netherlands</span>\"\n          </samp>]\n          <span class=sf-dump-index>147</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"45 characters\">&quot;Product bulletpoint 5 (English United States</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Default,&quot;Product bulletpoint 5 (English United States</span>\"\n          </samp>]\n          <span class=sf-dump-index>148</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"41 characters\">&quot;Product bulletpoint 5 (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Default,&quot;Product bulletpoint 5 (Dutch Netherlands</span>\"\n          </samp>]\n          <span class=sf-dump-index>149</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Product depth</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Product depth</span>\"\n          </samp>]\n          <span class=sf-dump-index>150</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Product depth (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Default,Product depth (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>151</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Product Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Default,Product Description</span>\"\n          </samp>]\n          <span class=sf-dump-index>152</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Product diameter</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Product diameter</span>\"\n          </samp>]\n          <span class=sf-dump-index>153</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Product diameter (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Default,Product diameter (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>154</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Product Group By Attribute(s)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Default,Product Group By Attribute(s)</span>\"\n          </samp>]\n          <span class=sf-dump-index>155</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product height</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Product height</span>\"\n          </samp>]\n          <span class=sf-dump-index>156</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Product height (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Product height (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>157</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product length</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Product length</span>\"\n          </samp>]\n          <span class=sf-dump-index>158</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Product length (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Product length (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>159</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"36 characters\">&quot;Product name (English United States</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Default,&quot;Product name (English United States</span>\"\n          </samp>]\n          <span class=sf-dump-index>160</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">&quot;Product name (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,&quot;Product name (Dutch Netherlands</span>\"\n          </samp>]\n          <span class=sf-dump-index>161</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Product type (normal/KIT/Special)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Default,Product type (normal/KIT/Special)</span>\"\n          </samp>]\n          <span class=sf-dump-index>162</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Product width</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Product width</span>\"\n          </samp>]\n          <span class=sf-dump-index>163</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Product width (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Default,Product width (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>164</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Quality Mark</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Quality Mark</span>\"\n          </samp>]\n          <span class=sf-dump-index>165</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Suitable for area</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Default,Suitable for area</span>\"\n          </samp>]\n          <span class=sf-dump-index>166</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Rotatable</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Rotatable</span>\"\n          </samp>]\n          <span class=sf-dump-index>167</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Material backing carpet</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Default,Material backing carpet</span>\"\n          </samp>]\n          <span class=sf-dump-index>168</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Safety Class</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Safety Class</span>\"\n          </samp>]\n          <span class=sf-dump-index>169</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Drop</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,Drop</span>\"\n          </samp>]\n          <span class=sf-dump-index>170</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Drop year</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Drop year</span>\"\n          </samp>]\n          <span class=sf-dump-index>171</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Seat amount</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Seat amount</span>\"\n          </samp>]\n          <span class=sf-dump-index>172</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Seat color</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,Seat color</span>\"\n          </samp>]\n          <span class=sf-dump-index>173</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Seat depth</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,Seat depth</span>\"\n          </samp>]\n          <span class=sf-dump-index>174</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Seat depth (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Default,Seat depth (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>175</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Seat height</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Seat height</span>\"\n          </samp>]\n          <span class=sf-dump-index>176</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Seat height (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Default,Seat height (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>177</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Seating comfort</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,Seating comfort</span>\"\n          </samp>]\n          <span class=sf-dump-index>178</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Material seat</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Material seat</span>\"\n          </samp>]\n          <span class=sf-dump-index>179</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Seat width</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,Seat width</span>\"\n          </samp>]\n          <span class=sf-dump-index>180</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Seat width (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Default,Seat width (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>181</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Serial name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Serial name</span>\"\n          </samp>]\n          <span class=sf-dump-index>182</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Set Contains</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Set Contains</span>\"\n          </samp>]\n          <span class=sf-dump-index>183</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Shape</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,Shape</span>\"\n          </samp>]\n          <span class=sf-dump-index>184</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Shelf amount</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Shelf amount</span>\"\n          </samp>]\n          <span class=sf-dump-index>185</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Max. loadable weight shelf</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Default,Max. loadable weight shelf</span>\"\n          </samp>]\n          <span class=sf-dump-index>186</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Max. loadable weight shelf (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Default,Max. loadable weight shelf (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>187</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"41 characters\">&quot;Short description (English United States</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Default,&quot;Short description (English United States</span>\"\n          </samp>]\n          <span class=sf-dump-index>188</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"37 characters\">&quot;Short description (Dutch Netherlands</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Default,&quot;Short description (Dutch Netherlands</span>\"\n          </samp>]\n          <span class=sf-dump-index>189</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Position corner sofa</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Default,Position corner sofa</span>\"\n          </samp>]\n          <span class=sf-dump-index>190</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Stack able</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,Stack able</span>\"\n          </samp>]\n          <span class=sf-dump-index>191</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Stuffing material</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Default,Stuffing material</span>\"\n          </samp>]\n          <span class=sf-dump-index>192</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Suitable for underfloor heating</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Default,Suitable for underfloor heating</span>\"\n          </samp>]\n          <span class=sf-dump-index>193</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"64 characters\">Trade name and contact address of manufacturer or importer in EU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"72 characters\">Default,Trade name and contact address of manufacturer or importer in EU</span>\"\n          </samp>]\n          <span class=sf-dump-index>194</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Type of surface mounted spotlight</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Default,Type of surface mounted spotlight</span>\"\n          </samp>]\n          <span class=sf-dump-index>195</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Extendable table top</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Default,Extendable table top</span>\"\n          </samp>]\n          <span class=sf-dump-index>196</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Table top finish</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Table top finish</span>\"\n          </samp>]\n          <span class=sf-dump-index>197</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Material table top</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Default,Material table top</span>\"\n          </samp>]\n          <span class=sf-dump-index>198</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Table type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,Table type</span>\"\n          </samp>]\n          <span class=sf-dump-index>199</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Upholstered</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Upholstered</span>\"\n          </samp>]\n          <span class=sf-dump-index>200</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Integrated USB port</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Default,Integrated USB port</span>\"\n          </samp>]\n          <span class=sf-dump-index>201</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">VAT Code (H)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,VAT Code (H)</span>\"\n          </samp>]\n          <span class=sf-dump-index>202</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Wall mount</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,Wall mount</span>\"\n          </samp>]\n          <span class=sf-dump-index>203</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Washing instructions</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Default,Washing instructions</span>\"\n          </samp>]\n          <span class=sf-dump-index>204</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Silent clockwork</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Silent clockwork</span>\"\n          </samp>]\n          <span class=sf-dump-index>205</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Waterproof</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,Waterproof</span>\"\n          </samp>]\n          <span class=sf-dump-index>206</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">With wheels</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,With wheels</span>\"\n          </samp>]\n          <span class=sf-dump-index>207</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Wire length</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Wire length</span>\"\n          </samp>]\n          <span class=sf-dump-index>208</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Wire length (Unit)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Default,Wire length (Unit)</span>\"\n          </samp>]\n          <span class=sf-dump-index>209</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Wire with plug included</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Default,Wire with plug included</span>\"\n          </samp>]\n          <span class=sf-dump-index>210</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Asset collection images</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Default,Asset collection images</span>\"\n          </samp>]\n          <span class=sf-dump-index>211</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[assetCollectionImages-file_path]</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Default,[assetCollectionImages-file_path]</span>\"\n          </samp>]\n          <span class=sf-dump-index>212</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 1</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Image URL 1</span>\"\n          </samp>]\n          <span class=sf-dump-index>213</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 2</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Image URL 2</span>\"\n          </samp>]\n          <span class=sf-dump-index>214</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 3</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Image URL 3</span>\"\n          </samp>]\n          <span class=sf-dump-index>215</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 4</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Image URL 4</span>\"\n          </samp>]\n          <span class=sf-dump-index>216</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 5</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Image URL 5</span>\"\n          </samp>]\n          <span class=sf-dump-index>217</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 6</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Image URL 6</span>\"\n          </samp>]\n          <span class=sf-dump-index>218</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 7</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Image URL 7</span>\"\n          </samp>]\n          <span class=sf-dump-index>219</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 8</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Image URL 8</span>\"\n          </samp>]\n          <span class=sf-dump-index>220</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Image URL 9</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Image URL 9</span>\"\n          </samp>]\n          <span class=sf-dump-index>221</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Image URL 10</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Image URL 10</span>\"\n          </samp>]\n          <span class=sf-dump-index>222</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Image URL 11</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Image URL 11</span>\"\n          </samp>]\n          <span class=sf-dump-index>223</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Image URL 12</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Image URL 12</span>\"\n          </samp>]\n          <span class=sf-dump-index>224</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Image URL 13</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Image URL 13</span>\"\n          </samp>]\n          <span class=sf-dump-index>225</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Image URL 14</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Image URL 14</span>\"\n          </samp>]\n          <span class=sf-dump-index>226</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Image URL 15</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Image URL 15</span>\"\n          </samp>]\n          <span class=sf-dump-index>227</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Default,</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"50 characters\">mapping_fields/upload/json/1748332390_datafile.csv</span>\"\n    \"<span class=sf-dump-key>file_url</span>\" => \"<span class=sf-dump-str title=\"100 characters\">https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1748332390_datafile.csv</span>\"\n    \"<span class=sf-dump-key>data_required</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>template_method_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n      \"<span class=sf-dump-key>output_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>sync</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>redirect_url_route</span>\" => \"<span class=sf-dump-str title=\"15 characters\">products.import</span>\"\n      \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n      \"<span class=sf-dump-key>versions</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>8</span> => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>catalogs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>8</span> => \"<span class=sf-dump-str title=\"13 characters\">Tanzayb Store</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>selected_template</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>temp_id</span>\" => <span class=sf-dump-num>23</span>\n        \"<span class=sf-dump-key>temp_name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">testingffff3355</span>\"\n        \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\Carbon @1748332370\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Carbon @1748332370</span> {<a class=sf-dump-ref>#2467</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000009a30000000000000000</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n          <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Tuesday, May 27, 2025\n- 00:00:30.80426 from now\nDST Off\">2025-05-27 07:52:50.0 UTC (+00:00)</span>\n        </samp>}\n        \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Product Name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">e22c210b-4e41-4dcd-bbba-c99303fe7c64</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Default,SKU</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">69183295-f7c7-4565-9004-c8f139879ad3</span>\"\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,Price</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">333633e9-0359-4206-a7ee-38c3cc2b9db7</span>\"\n            </samp>]\n            <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">Default,Quantity</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">f482a0d5-47d0-47c6-96c6-ce18b4c2b7b5</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>catalog</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>8</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>version</span>\" => \"<span class=sf-dump-str>8</span>\"\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n        \"<span class=sf-dump-key>export_type</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>product_status</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>import_action</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>apimio_attributes_required</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>all_families</span>\" => []\n      \"<span class=sf-dump-key>all_attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">single line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">multi line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">measurement</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>template_attributes</span>\" => []\n    \"<span class=sf-dump-key>output_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>handle</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>categories</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>compare_at_price</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>cost_price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>barcode</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>weight_unit</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>track_quantity</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>continue_selling</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>option1_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>option1_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>option2_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>option2_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>option3_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>option3_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>8</span> => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>seo_url</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>seo_keyword</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_output_array</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,categories</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">General,description</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,name</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,compare_at_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Variant,cost_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant,barcode</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant,weight</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant,weight_unit</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Variant,track_quantity</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,continue_selling</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option1_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option1_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option2_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option2_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option3_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option3_value</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Inventory -&gt; Tanzayb Store,8</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">SEO,seo_url</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>mapping_data</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Product Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">e22c210b-4e41-4dcd-bbba-c99303fe7c64</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Default,SKU</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">69183295-f7c7-4565-9004-c8f139879ad3</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">333633e9-0359-4206-a7ee-38c3cc2b9db7</span>\"\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">Default,Quantity</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">f482a0d5-47d0-47c6-96c6-ce18b4c2b7b5</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Template applied successfully</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1675250356\", {\"maxDepth\":0})</script>\n"}}