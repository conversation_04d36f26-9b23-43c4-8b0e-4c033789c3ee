<?php

namespace App\Models\Billing;

use App\Models\Organization\Organization;
use App\Notifications\SlackNotification as SN;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Validator;
use Laravel\Cashier\Exceptions\IncompletePayment;

use Laravel\Cashier\Subscription;
use Stripe\Coupon;
use Stripe\Exception\InvalidRequestException;

class Billing extends Model
{
    public $data;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    public function validation()
    {
        $validator = Validator::make($this->data, $this->rules());
        if (!$validator->fails()) {
            $this->attributes = $this->data;
        }
        return $validator;
    }

    //Valdiation rules
    public function rules()
    {
        //validation constants
        return [
            'name' => 'required|max:255',
            'city' => 'required|max:255|regex:/^[\pL\s\-]+$/u',
            'country' => 'required|max:255',
            'state' => 'required|max:255',
            'address' => 'required|max:255',
            'street_address' => 'required|max:255',
            'postal_code' => 'required|max:255',
        ];
    }

    //Getting data
    public function set_data($data)
    {
        $this->data = $data;
        return $this;
    }

    //Setting user
    public function set_user($user) {
        $this->data['user'] = $user;
        return $this;
    }

    //Getting Card Details
    public function card_details($org)
    {
        try {
            $org->createOrGetStripeCustomer(["name" => $org->name]);
            $org->updateDefaultPaymentMethod($this->data['payment_method']);
            return response("success", 200);
        } catch (\Exception $e) {
            return response($e->getMessage(), 422);
        }
    }

    //creating subscription
    public function Subscription($error_func,$success_func)
    {
        try {
            $org = Organization::where('id',$this->data['user']->organization_id)->first();
            if ($org) {
                // check if plan exist
                $monthly_plan_details = DB::table("plans")->where("stripe_monthly_id", $this->data['stripe_id'])->first();
                $yearly_plan_details = DB::table("plans")->Where("stripe_yearly_id", $this->data['stripe_id'])->first();
                if ($monthly_plan_details == null && $yearly_plan_details == null) {
                    return $error_func(['main'=> 'This plan does not exist.']);
                }

                if($monthly_plan_details == null)
                {
                    $plan_details = $yearly_plan_details;
                    $price = $yearly_plan_details->price_per_year;
                }
                else {
                    $plan_details = $monthly_plan_details;
                    $price = $monthly_plan_details->price_per_month;
                }

                // check if has payment method defined
                if (!$org->hasPaymentMethod()) {
                    return $error_func(["main" => "Default card is not attached"]);
                }

                // check if plan is already subscribed
                if ($org->subscribed('default')) {
                    if ($org->subscribedToPlan($this->data['stripe_id'], 'default')) {
                        return $error_func(['main' => 'Already subscribed to this plan']);
                    }
                }
                // get payment method
                $defaultPaymentMethod = $org->defaultPaymentMethod();
                //if user used other payment method
                if (isset($this->data['card_id'])){
                    if ($defaultPaymentMethod == $this->data['card_id']) {
                        $defaultPaymentMethod->id = $this->data['card_id'];
                    } else {
                        //if user haven't used default payment method making new method default
                        $defaultPaymentMethod->id = $org->updateDefaultPaymentMethod($this->data['card_id']);
                    }
                }

                try {
                    // check if subscription exists
                    if ($org->subscribed('default')) {
                        // coupons are only allowed for new subscriptions.
                        $stripeCharge =$org->subscription('default');
                        $stripeCharge->swap($this->data['stripe_id']);
                        if ($stripeCharge->quantity > 1) {
                            $stripeCharge->updateQuantity(1);
                        }

                        if (App::environment() != 'local') {
                            if ($stripeCharge) {
                                /*Slack Notification*/
                                $plan = DB::table("plans")->where("stripe_monthly_id", $this->data['stripe_id'])->orWhere("stripe_yearly_id", $this->data['stripe_id'])->first();
                                $content = 'Plan has been Upgraded. :tada: :money_mouth_face::money_mouth_face:';
                                $fields =
                                    [
                                        'Name' => $this->data['user']->fname . ' ' . $this->data['user']->lname,
                                        'Email' => $this->data['user']->email,
                                        'Phone' => $this->data['user']->phone ?? '',
                                        'Plan' => $plan->name,

                                    ];
                                $title = 'Plan Upgraded';
                                if (App::environment() != 'production') {
                                    $channel = '#apimio-notifications-staging';
                                } else {
                                    $channel = '#apimio-notifications';
                                }
                                $detail = [
                                    "content" => $content,
                                    "fields" => $fields,
                                    "title" => $title,
                                    "channel" => $channel
                                ];
                                Notification::route('slack', env('LOG_SLACK_WEBHOOK_URL'))
                                    ->notify(new SN($detail));
                                return redirect(route('dashboard'))->withSuccess('Plan changed successfully');
                            }
                        } else {
                            return redirect(route('dashboard'))->withSuccess('Plan changed successfully');
                        }
                        if ($stripeCharge->stripe_status != 'active') {
                            return $error_func(['main' =>  "There might be some problem upgrading your plan, Please try again."]);
                        }
                    }
                    else {

                        try {
                            // adding one so that quantity is maintained
                            $stripeCharge = $org->newSubscription('default', $this->data['stripe_id']);
                            if (isset($this->data['coupon']) ? !empty($this->data['coupon']) : false) {
                                \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));
                                try {
                                    Coupon::retrieve($this->data['coupon']);
                                } catch (InvalidRequestException $e) {
                                    return $error_func(['main' => "Your coupon is invalid."]);
                                }
                                $stripeCharge = $stripeCharge->withCoupon($this->data['coupon']);
                            }

                            $stripeCharge = $stripeCharge->create($defaultPaymentMethod->id, ['email' => $this->data['user']->email], ['metadata' => [
                                "name" => $plan_details->name,
                                "handle" => $plan_details->handle,
                                "price" => $price,
                                "total_products" => $plan_details->no_of_products,
                                "storage" => $plan_details->storage,
                                "no_of_catalogue" => $plan_details->no_of_catalogue,
                                "no_of_languages" => $plan_details->no_of_languages,
                                "no_of_currencies" => $plan_details->no_of_currencies,
                                "no_of_retailers" => $plan_details->no_of_retailers,
                                "no_of_vendors" => $plan_details->no_of_vendors,
                                "no_of_team_members" => $plan_details->no_of_team_members,
                                "no_of_channels" => $plan_details->no_of_channels,
                                "is_brand_portal" => $plan_details->is_brand_portal,
                                "is_vaf" => $plan_details->is_vaf,
                                "is_oks" => $plan_details->is_oks,
                                "is_trial_available" => $plan_details->is_trial_available,
                                "trial_period" => $plan_details->trial_period,
                                "organization_id" => $org,

                                "country" => $this->data['country'],
                                "city" => $this->data['city'],
                                "state" => $this->data['state'],
                                "address" => $this->data['address'],
                                "street_address" => $this->data['street_address'],
                                "unit" => $this->data['unit'],
                                "postal_code" => $this->data['postal_code'],
                            ]]);
                        } catch (IncompletePayment $exception) {


                            //if user subscribed in trial period
                            if (isset($org->trial_ends_at)) {
                                $org->trial_ends_at = null;
                                $org->save();
                            }

                            return redirect()->route(
                                'cashier.payment',
                                [$exception->payment->id, 'redirect' => route('dashboard')]
                            );
                        }
                        if ($stripeCharge->stripe_status != 'active') {
                            return $error_func(['main' =>  "There might be some problem upgrading your plan, Please try again."]);
                        } else {
                            //if user subscribed in trial period
                            if (isset($org->trial_ends_at)) {
                                $org->trial_ends_at = null;
                                $org->save();
                            }
                        }

                        //slack notification
                        if (App::environment() != 'local') {
                            $plan = DB::table("plans")->where("stripe_monthly_id", $this->data['stripe_id'])->orWhere("stripe_yearly_id", $this->data['stripe_id'])->first();

                            $content = 'Plan has been Purchased. :tada: :+1:';
                            $fields =
                                [
                                    'Name' => $this->data['user']->fname . ' ' . $this->data['user']->lname,
                                    'Email' => $this->data['user']->email,
                                    'Phone' => $this->data['user']->phone ?? '',
                                    'Plan' => $plan->name,

                                ];
                            $title = 'Plan Purchased';
                            if (App::environment() != 'production') {
                                $channel = '#apimio-notifications-staging';
                            }
                            else {
                                $channel = '#apimio-notifications';
                            }
                            $detail = [
                                "content" => $content,
                                "fields" => $fields,
                                "title" => $title,
                                "channel" => $channel
                            ];
                            Notification::route('slack', env('LOG_SLACK_WEBHOOK_URL'))
                                ->notify(new SN($detail));
                        }
                        //end slack notification
                        return $success_func();
                    }
                }
                catch (\Exception $exception) {
                    return $error_func(['main' =>  "There might be some problem processing your payment, Please try again."]);
                }
            }
            else {
                throw new \Exception('Please add organization first');
            }

        }
        catch(\Exception $e) {
            return redirect()->back()->withErrors(['main'=> $e->getMessage()]);
        }
    }

    public function dashboard_plan()
    {
        return Auth::user()->asStripeCustomer()->subscriptions->data[0]->metadata;
    }

}
