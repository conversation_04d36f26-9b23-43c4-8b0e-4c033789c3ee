<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ExportCsvApiRecord;

class ExportCsvApiRecordController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $csvRecords = ExportCsvApiRecord::all();
        return view('exportcsvapirecord.index', compact('csvRecords'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store($request)
    {
        $exportRecord = ExportCsvApiRecord::create($request->all());
        return response()->json($exportRecord, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update($request, string $id)
    {
        $exportRecord = ExportCsvApiRecord::find($id);
        $exportRecord->update($request->all());
        return response()->json($exportRecord, 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
