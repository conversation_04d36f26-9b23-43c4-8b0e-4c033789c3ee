<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChannelResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'value' => $this->id,
            'organization_id' => $this->organization_id,
            'name' => $this->name,
            'title' => $this->name,
            'product_update' => $this->product_update,
            'inventory' => $this->inventory,
            'export_status' => $this->export_status,
            'category_empty' => $this->category_empty,
            'type' => $this->type,
            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at->toDateTimeString(),
        ];
    }
}
