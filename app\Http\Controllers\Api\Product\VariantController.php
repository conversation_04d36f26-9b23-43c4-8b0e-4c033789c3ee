<?php

namespace App\Http\Controllers\Api\Product;

use data;
use Illuminate\Http\Request;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\Product\Attribute;
use App\Http\Controllers\Controller;
use App\Events\Product\ManageVariants;
use App\Http\Requests\Product\VariantRequest;
use App\Http\Resources\Product\Variant\GetResource;
use App\Models\Organization\Organization;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\Product\Version;
use App\Models\Setting;

class VariantController extends Controller
{
    /**
     * Retrieve attribute variants options.
     *
     * This method retrieves attribute variants options based on the provided attribute IDs.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getAttributeVariantsOptions(Request $request,$product_id,$version_id)
    {
        $query =  Attribute::where('attribute_type_id', 13)
             ->whereNot('is_default', 1)
            ->with('attribute_options');
        if ($request->filled('attribute_ids')) {
            $query->whereIn('id', $request->attribute_ids);
        }
        $attributes = $query->get();
        $existing_selected_variant = Variant::where('product_id', $product_id)
            ->where('version_id', $version_id)
            ->pluck('option')
            ->toArray();
        $variantModel = new Variant();
        $selected_variants_id = $variantModel->filter_already_assigned_variants($existing_selected_variant);
        foreach ($attributes as $attribute) {
            $attribute->is_assigned = in_array($attribute->id, $selected_variants_id);
            foreach ($attribute->attribute_options as $option) {
                $option->is_selected = false;
                foreach ($existing_selected_variant as $variant) {
                    $variantData = json_decode($variant, true);
                    foreach ($variantData['attributes'] as $variantAttribute) {
                        if ($variantAttribute['id'] == $attribute->id && in_array($option->name, $variantData['options'])) {
                            $option->is_selected = true;
                            break 2;
                        }
                    }
                }
            }
        }

        if ($attributes->isEmpty()) {
            return response(['status' => '404', 'message' => 'Attribute not found'], 404);
        }

        return response(['data' => $attributes, 'message' => 'Attributes retrieved successfully'], 200);
    }
    /**
     * Retrieve all attribute variants.
     *
     * This method retrieves attribute variants based on the provided product ID and version ID.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $product_id
     * @param  int  $version_id
     * @return \Illuminate\Http\Response
     */
    public function getAttributeVariants(Request $request, $product_id, $version_id)
    {
        $status = 200;
        $organization_id = Auth::user()->organization_id;
        $message = 'Variants retrieved successfully';
        $versionCurrency = Version::where('id', $version_id)->value('currency');
        $variants = Variant::with(['file:id,link,name'])
            ->where('product_id', $product_id)
            ->where('version_id', $version_id)
            ->get();
        $variant_settings = Setting::where('organization_id', $organization_id)->pluck('value', 'key');
        $organization_unit = Organization::where('id', $organization_id)->value('units');
        $variantsFinalData = [
            'variantsData' => $variants->map(function ($variant) use ($organization_unit) {
                $variant->weight_unit = $variant->weight_unit ?? $organization_unit;
                return $variant;
            }),
            'product_id' => $product_id,
            'version_id' => $version_id,
            'organization_id' => $organization_id,
            'currency' => $versionCurrency,
            'variant_settings' => $variant_settings,
        ];
        if (!$variants) {
            $status = 404;
            $message = 'Variant not found';
        } else if (count($variants) <= 0) {
            $message = 'Variant not found';
        }
        return response(['data' => $variantsFinalData, 'message' => $message], $status);
    }

    /**
     * Trigger a variant event for a specific product and version to save variants.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $product_id
     * @param  int  $version_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function createVariant(VariantRequest $request)
    {
        // $product = new Product();
        // $product->id = $product_id;
        // $product->organization_id = auth()->user()->organization_id;
        // $variants = $request->input('variants', []);
        // $attributes = $request->get('attributes', []);
        // $data = ['versions' => [['id' => $version_id]]];
        // event(new ManageVariants($product, $variants, $data));

        $data = $request->only(['product_id', 'attributes', 'version_id']);

        $data = $data + ['organization_id' => auth()->user()->organization_id];
        $variant = new Variant();
        // dd($data);
        return $variant->set_data($data)->save_variant_combinations(
            // when error
            function ($error) {
                return ['error' => $error];
            },
            // when success
            function ($id) use ($request) {

                return response(['message' => 'ManageVariants event triggered successfully']);
            }
        );
    }
    /**
     * Delete variants.
     *
     * This method deletes variants based on the provided variant IDs array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function delete_variants(Request $request)
    {
        if ($request->filled('variantIds')) {
            Variant::whereIn('id', $request->variantIds)->delete();

            return response(['message' => 'Variants deleted successfully.'], 200);
        } else {
            return response(['message' => 'Variant not found'], 404);
        }
    }
    /**
     * Update attribute variants.
     *
     * This method updates attribute variants based on the provided request data.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateAttributeVariants(VariantRequest $request)
    {
        $variant = new Variant();
        return $variant->set_data($request->all())->store(
            // when error
            function ($error) {
                return ['error' => $error];
            },
            // when success
            function () {
                return response(['message' => 'Variants Update successfully']);
            }
        );
    }
}
