<?php

namespace App\Http\Controllers\Product;

use Illuminate\Http\Request;
use App\Models\Product\Category;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Gate;

class CategoriesController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified', 'activeOrganization']);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (Gate::denies('SubscriptionAccess', 'category')) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        $category_data = (new Category())->get_parent_category(null, null, 'view');

        $category = new Category();
        if ($request->has('q')) {
            $category->filter(["name" => $request->get('q')]);
        }
        $data['categories'] = $category->fetch();
        return view('products.categories.view', compact('category_data','data', 'request'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $category_data = (new Category())->get_parent_category();
        return view('products.categories.add', compact('category_data'));
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $category = new Category();
        return $category->set_data($request->all())->store(function ($error) {
            return back()->withInput()->withErrors($error);
        }, function () {
            return redirect(route("categories.index"))->withSuccess("Category created successfully.");
        });
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $category = Category::find($id);
        $category_data = (new Category())->get_parent_category($id);
        return view('products.categories.add', compact('category','category_data'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $category = new Category();
        return $category->set_id($id)->set_data($request->all())->store(function ($error) {
            return back()->withInput()->withErrors($error);
        }, function () {
            return redirect(route("categories.index"))->withSuccess("Category updated successfully.");
        });
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $category = Category::findOrFail($id);
        $category->delete();
        return back()->withSuccess("Deleted successfully.");
    }
}
