<?php

namespace App\Events;

use App\Models\Channel\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class LocationEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    public array $data;
    public Channel $channel;
    public  $refresh;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($channel,$data,$refresh = null)
    {
        $this->data = $data;
        $this->channel = $channel;
        $this->refresh = $refresh;
    }
}
