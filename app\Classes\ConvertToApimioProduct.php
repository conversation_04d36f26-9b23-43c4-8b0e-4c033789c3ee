<?php

namespace App\Classes;

use App\Models\Channel\Channel;
use App\Models\Invite\Invite;
use App\Models\Organization\File;
use App\Models\Product\Attribute;
use App\Models\Product\Brand;
use App\Models\Product\Category;
use App\Models\Product\Family;
use App\Models\Product\Inventory;
use App\Models\Product\Product;
use App\Models\Product\Vendor;
use App\Models\Product\Version;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
/**
 * @deprecated class
 **/
class ConvertToApimioProduct
{
    private $organization_id = [];
    private $store_product = [];
    private $shopify_variants = false;

    private function csvValidation() : array {
        return  [
            "sku"                           => "required",
            "families.*.name"               => "required",
            "families.*.attributes"         => "required",
            "families.*.attributes.*.handle"  => "required",
            "families.*.attributes.*.attribute_type_id"  => "required",
            "brands.*.name"                 => "required",
            "categories.*.name"             => "required",
            "vendors.*.name"                => "required",
            "versions.*.name"               => "required_without:versions.*.id",
            "versions.*.id"                 => "required_without:versions.*.name",
            "channels.*.name"               => "required_without:channels.*.id",
            "channels.*.id"                 => "required_without:channels.*.name",
            'channels.*.type'               => ['required_without:channels.*.id',Rule::in(['shopify','magento','woocommerce'])],
            "files.*.link"                  => "required",
            "variants.attributes"         => "required_with:variants",
            "variants.data"                 => "required_with:variants",
        ];
    }

    private function shopifyValidation() : array {
        return  [
            "sku"                           => "required",
            "families.*.name"               => "required",
            "families.*.attributes"         => "required",
            "families.*.attributes.*.handle"  => "required",
            "families.*.attributes.*.attribute_type_id"  => "required",
            "brands.*.name"                 => "required",
            "categories.*.name"             => "required",
            "vendors.*.name"                => "required",
            "versions.*.name"               => "required_without:versions.*.id",
            "versions.*.id"                 => "required_without:versions.*.name",
            "channels.*.name"               => "required_without:channels.*.id",
            "channels.*.id"                 => "required_without:channels.*.name",
            'channels.*.type'               => ['required_without:channels.*.id',Rule::in(['shopify','magento','woocommerce'])],
            "files.*.link"                  => "required",
        ];
    }

    public function getSku(array $product) : void {
        $this->store_product["sku"] =  $product["sku"];
    }

    public function getFamilies(array $families ) :void {
        foreach ($families as $family) {
            $family_exist = Family::query();
            if(isset($this->organization_id["check_from"])) {
                $family_exist = Family::withoutGlobalScopes()
                    ->where("organization_id", $this->organization_id["check_from"]);
            }
            if(isset($family["id"])) {
                $family_exist = $family_exist->find("id", $family["id"]);
            }
            else {
                $family_exist = $family_exist->where("name", $family["name"])->get()->first();
            }


            // save attributes to assign with family
            foreach ($family["attributes"] as $attribute) {
                try {
                    $attribute_exist = Attribute::query();

                    if(isset($this->organization_id["check_from"])) {
                        $attribute_exist = $attribute_exist->withoutGlobalScopes()
                            ->where("organization_id", $this->organization_id["check_from"]);
                    }

                    if(isset($attribute["id"])) {
                        $attribute_exist = $attribute_exist->find($attribute["id"]);
                    }
                    else if(isset($attribute["handle"])) {
                        $attribute_exist = $attribute_exist->where("handle", $attribute["handle"])->get()->first();
                    }
                    else if(isset($attribute["name"])) {
                        $attribute_exist = $attribute_exist->where("name", $attribute["name"])->get()->first();
                    }

                    // create new attribute

                    if(!$attribute_exist) {
                        $attribute_obj = new Attribute();
                        if(isset($this->organization_id["save_to"])) {
                            $attribute["organization_id"] = $this->organization_id["save_to"];
                        }

                        $attribute_obj->set_data($attribute)->store(
                        // error function
                            function ($errors) {
                                return $errors;
                            },
                            // success function
                            function ($obj) use (&$store_product, &$attribute_exist) {
                                $attribute_exist = $obj;
                            }
                        );
                    }
                    // update attribute
                    if ($family_exist) {
                        if($attribute_exist && !$attribute_exist->families()->exists()) {
                            $family_exist->attributes()->attach($attribute_exist->id);
                        }
                        else {
                            $family_exist = $attribute_exist->families()->get()->first();
                        }
                    }
                    else {
                        $family["attribute_ids"] = $attribute_exist->id;
                        $obj = new Family();
                        if (isset($this->organization_id["save_to"])) {
                            $family["organization_id"] = $this->organization_id["save_to"];
                        }

                        $obj->set_data($family)->store(
                        // error function
                            function ($errors) {
                                return $errors;
                            },
                            // success function
                            function ($obj) use (&$store_product, &$family_exist) {
                                $family_exist = $obj;
                            }
                        );
                    }

                    if (isset($organization_id["save_to"])) {
                        $this->store_product["attribute"][
                        $attribute_exist
                            ->families()
                            ->withoutGlobalScopes()
                            ->where("organization_id", $this->organization_id["save_to"])
                            ->find($family_exist->id)->pivot->id
                        ] = isset($attribute["value"]) ? $attribute["value"] : null;
                    } else {
                        $this->store_product["attribute"][
                        $attribute_exist
                            ->families()
                            ->find($family_exist->id)
                            ->pivot->id]                    = $attribute["value"];
                    }
                }
                catch (\Exception $e) {
                    Log::error($e);
                }
            }
        }
    }

    public function getBrands(array $brands) : void {

        foreach ($brands as $brand) {
            if(isset($this->organization_id["check_from"])) {

                $brand_exist = Brand::withoutGlobalScopes()
                    ->where("organization_id", $this->organization_id["check_from"])
                    ->where("name", $brand["name"])->get()->first();

            }
            else {
                $brand_exist = Brand::where("name", $brand["name"])->get()->first();
            }
            if($brand_exist) {
                $this->store_product["brand"][] = $brand_exist->id;
            }
            else {
                $obj = new Brand();
                if(isset($this->organization_id["save_to"])) {
                    $brand["organization_id"] = $this->organization_id["save_to"];
                }
                $obj->set_data($brand)->store(
                // error function
                    function ($errors) {
                        return $errors;
                    },
                    // success function
                    function ($obj){
                        $this->store_product["brand"][] = $obj->id;
                    }
                );
            }
        }
    }

    public function getCategories(array $categories) : void {

        foreach ($categories as $category) {
            if(isset($this->organization_id["check_from"])) {
                $category_exist = Category::withoutGlobalScopes()
                    ->where("organization_id", $this->organization_id["check_from"])
                    ->where("name", $category["name"])->get()->first();
            }
            else {
                $category_exist = Category::where("name", $category["name"])->get()->first();
            }
            if($category_exist) {
                $this->store_product["category"][] = $category_exist->id;
            }
            else {
                $obj = new Category();
                if(isset($this->organization_id["save_to"])) {
                    $category["organization_id"] = $this->organization_id["save_to"];
                }
                $obj->set_data($category)->store(
                // error function
                    function ($errors) {
                        return $errors;
                    },
                    // success function
                    function ($obj){
                        $this->store_product["category"][] = $obj->id;
                    }
                );
            }
        }
    }

    public function getVendors(array $vendors) :void {
        foreach ($vendors as $vendor) {

            if(isset($this->organization_id["check_from"])) {
                $vendor_exist = Invite::withoutGlobalScopes()
                    ->where("organization_id_sender", $this->organization_id["check_from"])
                    ->where("fname", $vendor["name"])->get()->first();
            }
            else {
                $vendor_exist = Invite::where("fname", $vendor["name"])->get()->first();
            }
            if($vendor_exist) {
                $this->store_product["vendor"][] = $vendor_exist->id;
            }
            else {
                if(isset($this->organization_id["save_to"])) {
                    $vendor["organization_id"] = $this->organization_id["save_to"];
                }
                $vendor["type"]='vendor';
                (new Invite())->set_data($vendor)->storeVendor(
                // error function
                    function ($errors) {
                        return $errors;
                    },
                    // success function
                    function ($obj){
                        $this->store_product["vendor"][] = $obj->id;
                    }
                );
            }
        }
    }

    public function getVersions(array $product ) : void {

        if(isset($product["versions"])) {

            foreach ($product["versions"] as $version) {
                if (isset($version["id"])) {
                    $this->store_product["version_id"] = $version["id"];
                } else {
                    if (isset($this->organization_id["check_from"])) {
                        $version_exist = Version::withoutGlobalScopes()
                            ->where("organization_id", $this->organization_id["check_from"])
                            ->where("name", $version["name"])->get()->first();
                    } else {
                        $version_exist = Version::where("name", $version["name"])->get()->first();
                    }
                    if ($version_exist) {
                        $this->store_product["version_id"] = $version_exist->id;
                    } else {
                        //  $this->saveNewVersion($store_product);
                        $obj = new Version();
                        if (isset($this->organization_id["save_to"])) {
                            $version["organization_id"] = $this->organization_id["save_to"];
                        }
                        $obj->set_data($version)->store(
                        // error function
                            function ($errors) {
                                Log::error('save  apimio product version error');
                                Log::error($errors);
                            },
                            // success function
                            function ($obj){
                                $this->store_product["version_id"] = $obj->id;
                            }
                        );
                    }
                }
            }
        }
        else
        {
            $new_version = Version::where('organization_id', $this->organization_id["check_from"])->first();
            $this->store_product["version_id"] = $new_version->id;
        }
    }

    public function getChannels(array $product ) :void {
        if(isset($product["channels"])){

            foreach ($product["channels"] as $channel) {

                if(isset($channel["id"])) {
                    $this->store_product["channels"][] = $channel["id"];
                }
                else {
                    if(isset($this->organization_id["check_from"])) {
                        $channel_exist = Channel::withoutGlobalScopes()
                            ->where("organization_id", $this->organization_id["check_from"])
                            ->where("name", $channel["name"])->get()->first();
                    }
                    else {
                        $channel_exist = Channel::where("name", $channel["name"])->get()->first();
                    }
                    if($channel_exist) {
                        $this->store_product["channels"][] = $channel_exist->id;
                    }
                    else {
                        $obj = new Channel();
                        if(isset($this->organization_id["save_to"])) {
                            $channel["organization_id"] = $this->organization_id["save_to"];
                        }
                        $obj->set_data($channel)->store(
                        // error function
                            function ($errors) {
                                return $errors;
                            },
                            // success function
                            function ($obj){
                                $this->store_product["channels"][] = $obj->id;
                            }
                        );
                    }
                }
            }
        }
        else
        {
            $this->store_product["channels"][] = Channel::withoutGlobalScopes()
                ->where("organization_id", $this->organization_id["check_from"])
                ->pluck('id')->first();
        }
    }

    public function getFiles(array $files ):void {
       $count = 1;
        foreach ($files as $file) {
           if($count == 1){
               $this->saveFile($file);
           }
           else{
               //get images without variants
               if(sizeof($file['variant_ids']) == 0){
                   $this->saveFile($file);
               }
           }
           $count++;

        }

    }
    private function saveFile($file){

        $file["link"] = strtok($file["link"], '?');

         $extension = pathinfo($file["link"], PATHINFO_EXTENSION);
            if(isset($this->organization_id["check_from"])) {
                $file_exist = File::withoutGlobalScopes()
                    ->where("organization_id", $this->organization_id["check_from"])
                    ->where("link", $file["link"])->get()->first();
            }
            else {
                $file_exist = File::where("link", $file["link"])->get()->first();
            }
            if($file_exist) {
                $this->store_product["files"][] =[
                    'id' => $file_exist->id,
                    'shopify_id' =>$file["id"]
                ];
            }
            else {
                $image1                   = new File();
                $image1->link             = $file["link"]??null;
                $image1->name             = $file["name"]??null;
                if(isset($this->organization_id["save_to"])) {
                    $image1->organization_id  = $this->organization_id["save_to"];
                }
                $image1->width            = $file['width']??null;
                $image1->height           = $file['height']??null;
                $image1->should_sync      = 1;
                $image1->type             = 'img';
                $image1->size             = $size??null;
                $image1->ext             = $extension??null;
                if($image1->save()){
                    $this->store_product["files"][] =[
                        'id' => $image1->id,
                        'shopify_id' =>$file["id"]
                    ];
                }
            }

    }

    public function getCsvVariants(array $variants) : void {

        $organization_id = $this->organization_id;
        $product["variants"]["attributes"] = array_map(function ($attribute) use($organization_id) {
            if(!isset($attribute["id"])) {
                $attribute_obj = new Attribute();
                if(isset($organization_id["save_to"])) {
                    $attribute["organization_id"] = $organization_id["save_to"];
                }
                $attribute["attribute_options"] = array_map(function ($row) {
                    return ["name" => $row];
                }, array_filter($attribute["options"]));
                $attribute["attribute_type_id"] = 4;
                $attribute_obj->set_data($attribute)->store(
                // error function
                    function ($errors) {
                        return $errors;
                    },
                    // success function
                    function ($obj) use (&$attribute) {
                        $attribute["id"] = $obj->id;
                    }
                );
                return [
                    "name" => $attribute["name"],
                    "options" => array_column($attribute["attribute_options"], "name"),
                    "id" => $attribute["id"],
                ];
            }
        }, $variants["attributes"]);
        $this->store_product["variants"] = $product["variants"];
    }

    public function getShopifyVariants(array $variants){
        $organization_id = $this->organization_id;

        foreach($variants as $variant){

          //attribute with attribute id and save attributes in db
           $new_attribute = array_map(function ($attribute) use($organization_id) {
                if(!isset($attribute["id"])) {

                    $attribute_obj = new Attribute();
                    $attr = $attribute_obj->where('organization_id',$organization_id["save_to"])->where('name',$attribute['name'])->first();
                    if($attr){
                        $attribute["id"] = $attr->id;
                        return $attribute;
                    }

                    if(isset($organization_id["save_to"])) {
                        $attribute1["organization_id"] = $organization_id["save_to"];
                    }

                    $attribute1["attribute_options"] = array_map(function ($row) {
                        return ["name" => $row];
                    }, array_filter($attribute["options"]));

                    $attribute1["attribute_type_id"] = 13;
                    $attribute1["name"] = $attribute["name"];
                    $attribute1["options"] = $attribute["options"];
                    try{
                        $attribute_obj->set_data($attribute1)->store(
                        // error function
                            function ($errors) {
                                Log::channel('shopify')->info('VARIANT OPTION ERROR');
                                Log::channel('shopify')->info($errors);
                                return $errors;
                            },
                            // success function
                            function ($obj) use (&$attribute) {
                                $attribute["id"] = $obj->id;

                            }
                        );
                    }catch(\Exception $e){
                        Log::channel('shopify')->info('VARIANT OPTION ERROR');
                        Log::channel('shopify')->info($e->getMessage());
                    }

                    return $attribute;
                }
            }, $variant["attributes"]);

            $variant['attributes'] = $new_attribute;
            $this->store_product["variants"][] = $variant;
        }

    }

    public function getProductId(array $product) :void {
        if(isset($this->organization_id["save_to"])) {

            if(isset($product['id'])){
                $product_exist = Product::withoutGlobalScopes()
                    ->where("organization_id", $this->organization_id["save_to"])
                    ->where("id", $product['id'])->get()
                    ->first();
            }else{
                $product_exist = Product::withoutGlobalScopes()
                    ->where("organization_id", $this->organization_id["save_to"])
                    ->where("sku", $this->store_product["sku"])->get()
                    ->first();
            }
        }
        else {
            if(isset($product['id'])){
                $product_exist = Product::where("id", $product['id'])->get()->first();
            }
            else{
                $product_exist = Product::where("sku", $this->store_product["sku"])->get()->first();
            }
        }
        if($product_exist) {
            $this->store_product["id"] = $product_exist->id;
        }
    }

    private function saving_and_initializing_product_variables(array $product , array $organization_id) :void {
        //set the organization_id
        $this->organization_id = $organization_id;

        // assign attributes to product array
        $this->getSku($product);

        if(isset($product["shopify_id"])) {
            $this->store_product["shopify_product_id"] = $product["shopify_id"];
        }
        if(isset($product["inventory_id"])) {
            $this->store_product["inventory_id"] = $product["inventory_id"];
        }
        if(isset($organization_id["save_to"])) {
            $this->store_product["organization_id"] = $organization_id["save_to"];
        }

        if(isset($product["status"])) {
            $this->store_product["status"] = $product["status"];
        }

        // save families
        if(isset($product["families"])) {
            $this->getFamilies($product["families"]);
        }

        // save brands
        if(isset($product["brands"])) {
            $this->getBrands($product["brands"]);
        }

        // save categories
        if(isset($product["categories"])) {
            $this->getCategories($product["categories"]);
        }

        // save vendors
        if(isset($product["vendors"])) {
            $this->getVendors($product["vendors"]);
        }

        // save versions

        $this->getVersions($product);

        // save channels
        $this->getChannels($product) ;

        // save files
        if(isset($product["files"])) {
            $this->getFiles($product["files"]);
        }

        // check if sku exist then update.
        $this->getProductId($product);
    }

    public function save_product( callable $error_callback , callable $success_callback , $product_detach = false )  {

        $product_obj = new Product();
        $store_product = $this->store_product;
        return $product_obj->set_data($store_product)->store(
            function ($errors) use ($error_callback) {
                Log::channel('shopify')->info($errors);
               // DB::rollBack();
                return $error_callback($errors);

            },
            function ($obj) use ($success_callback, $store_product,$product_obj) {

                if(isset($store_product["variants"])) {
                    if($this->shopify_variants) {
                        $obj->attach_shopify_variants($store_product["variants"],$this->organization_id,$store_product["channels"]);
                    }
                    else
                    {
                        $obj->attach_variants($store_product["variants"], $this->organization_id);
                    }
                }

                //on success of saving product, save that in which channel it belongs to.
                if(sizeof($obj->channels)>0){
                    $product_obj->save_product_status([
                        'cloned_product_channel_ids' => $store_product["channels"],
                        'cloned_product_id' =>  $obj->id,
                        'organization_id' => $obj->organization_id,
                        'type' => 'shopify',
                        'product_id' => $store_product["shopify_product_id"]
                    ]);

                }

                if(isset($store_product["inventory_id"])){
                    $inventory = Inventory::where('product_id',$obj->id)->where('store_connect_id',$store_product["inventory_id"])->first();
                    if(!$inventory){
                        $inventory = new Inventory();
                    }
                    $inventory->product_id = $obj->id;
                    $inventory->organization_id = $obj->organization_id;
                    $inventory->store_connect_id = $store_product["inventory_id"];
                    $inventory->store_type = 'shopify';
                    $inventory->save();
                }

               // DB::commit();
                $product = new Product();
                return $success_callback($product->fetch(function ($product) use ($obj) {
                    if(isset($this->organization_id["save_to"])) {
                        return $product->withoutGlobalScopes()->findOrFail($obj->id);
                    }
                    else {
                        return $product->findOrFail($obj->id);
                    }
                }));
            },
            $product_detach
        );


    }


    /**
     * Saves given array with all the product information.
     *
     * @param  callback  $error_callback returned with errors
     * @param  callback  $success_callback returned with product object
     * @param  array     $product product array with information
     * @param  array     $organization_id optional, associative array with "save_to" to save all information in provided organization and "check_from" to query information in provided organization id
     *
     * @return  callable Returns $success_callback with product object when there are no error,
     * and $error_callback with errors when there are errors.
     */
    public function convert_shopify_to_apimio_product (callable $error_callback, callable $success_callback, array $product, array $organization_id = [])  {

        // validate minimum data required to save a product.
        $validator = Validator::make($product, $this->shopifyValidation() );

        if($validator->fails()) {
            return $error_callback($validator->errors());
        }

        $product_detach = false;

        $this->organization_id = $organization_id;

        if(isset($product['id'])) {
            $this->store_product['id'] = $product['id'];
        }
        if(isset($product['detach'])) {
            $product_detach = $product['detach'];
        }
        if(isset($product['store_detach'])) {
            $this->store_product['store_detach'] = $product['store_detach'];
        }
        /*if(isset($product['channels']) && sizeof($product['channels'])>0) {
            $this->store_product['channels'] = $product['channels'][0];
        }*/

        if(isset($product["variants"])) {
            // Create or update attributes
            $this->getShopifyVariants($product['variants']);
        }else{
            $this->store_product["variants"] = [] ;
        }
        $this->saving_and_initializing_product_variables($product,$organization_id );
        $this->shopify_variants = true ;
        // save product
         return ($this->save_product($error_callback,$success_callback,$product_detach));

    }

    /**
     * Saves given array with all the product information.
     *
     * @param  callback  $error_callback returned with errors
     * @param  callback  $success_callback returned with product object
     * @param  array     $product product array with information
     * @param  array     $organization_id optional, associative array with "save_to" to save all information in provided organization and "check_from" to query information in provided organization id
     *
     * @return  Product Returns $success_callback with product object when there are no error,
     * and $error_callback with errors when there are errors.
     */
    public function convert_csv_to_apimio_product (callable $error_callback, callable $success_callback, array $product, array $organization_id = []) :callable {


        // validate minimum data required to save a product.
        $validator = Validator::make($product, $this->csvValidation());

        if($validator->fails()) {
            return $error_callback($validator->errors());
        }

        $this->saving_and_initializing_product_variables($product,$organization_id);

        if(isset($product["variants"])) {
            // Create or update attributes
            $this->getCsvVariants($product['variants']);
        }
        // save product
        return $this->save_product($error_callback,$success_callback);

    }

}
