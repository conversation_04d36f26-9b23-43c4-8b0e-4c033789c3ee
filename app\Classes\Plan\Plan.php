<?php

namespace App\Classes\Plan;

class Plan
{
    public $stripe_monthly_id;
    public $stripe_yearly_id;
    public $stripe_product_id;
    public $name;
    public $handle;
    public $icon_link;
    public $price_per_month;
    public $price_per_year;
    public $next_price;
    public $no_of_sku;
    public $no_of_channel;
    public $no_of_brand;
    public $storage;
    public $is_brand_portal;
    public $is_vaf;
    public $is_oks;
    public $is_trial_available;
    public $trial_period;
    public $modules;
    public $items;
    public $color;
    public $info;
    public $addOns;
    public $no_of_lang;
    public $shopifyUrl;

    public function __construct(
        $stripe_monthly_id, $stripe_yearly_id,$stripe_product_id, $name, $handle, $icon_link,
        $price_per_month, $price_per_year, $next_price, $no_of_sku, $no_of_channel,
        $no_of_brand, $storage, $is_brand_portal, $is_vaf, $is_oks, $is_trial_available,
        $trial_period, $modules, $items, $color,$info,$addOns,$no_of_lang,$shopifyUrl
    ) {
        $this->stripe_monthly_id = $stripe_monthly_id;
        $this->stripe_yearly_id = $stripe_yearly_id;
        $this->stripe_product_id = $stripe_product_id;
        $this->name = $name;
        $this->handle = $handle;
        $this->icon_link = $icon_link;
        $this->price_per_month = $price_per_month;
        $this->price_per_year = $price_per_year;
        $this->next_price = $next_price;
        $this->no_of_sku = $no_of_sku;
        $this->no_of_channel = $no_of_channel;
        $this->no_of_brand = $no_of_brand;
        $this->storage = $storage;
        $this->is_brand_portal = $is_brand_portal;
        $this->is_vaf = $is_vaf;
        $this->is_oks = $is_oks;
        $this->is_trial_available = $is_trial_available;
        $this->trial_period = $trial_period;
        $this->modules = $modules;
        $this->items = $items;
        $this->color = $color;
        $this->info = $info;
        $this->addOns = $addOns;
        $this->no_of_lang = $no_of_lang;
        $this->shopifyUrl = $shopifyUrl;
    }
}
