<?php

namespace App\Http\Middleware;

use App\Models\Invite\Invite;
use App\Models\Organization\Organization;
use Closure;
use Illuminate\Support\Facades\Auth;

class BillingMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $org = Organization::findOrFail(Auth::user()->organization_id ?? "");

        if ($org->onTrial()) {
            return $next($request);
        } else {
            $invite = new Invite();
            //checking if user has subscribed
            if($org->is_subscribed()) {
                return $next($request);
            } elseif($invite->isInvited(Auth::user()->email)) {
                return $next($request);
            }
            else {
                return redirect(route('billing'));
            }
        }
    }
}
