<?php

namespace App\Http\Requests\Version;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;
use App\Models\Product\Version;

class VersionRequest extends FormRequest
{

    public function authorize()
    {
        $response = Gate::inspect('createLanguage',Version::class);
        if (!$response->allowed()) {
            abort(403, $response->message() ?: 'Unauthorized to create a version.');
        }

        return true; // Authorization passed
    }



    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'organization_id' => 'prohibited',
            // If 'id' is set, validate it as an integer; otherwise it's optional
            'id' => 'nullable|integer|exists:versions,id',

            // 'name' should be a required string and unique (maybe within the same organization)
            'name' => 'required|string|max:255|unique:versions,name,' . $this->route('version') .',id,organization_id,' . auth()->user()->id,

            // always upper case
            'currency' => 'nullable|string|uppercase',

            // 'separator' should be a string, typically one character
            'separator' => 'nullable|string',
        ];
    }


    public function messages()
    {
        return [
            'id.integer' => 'The ID must be a valid integer.',
            'id.exists' => 'The specified ID does not exist in the versions table.',
            'organization_id.integer' => 'The organization ID must be a valid integer.',
            'organization_id.exists' => 'The specified organization does not exist.',
            'name.required' => 'The name field is required.',
            'name.string' => 'The name must be a string.',
            'name.max' => 'The name cannot exceed 255 characters.',
            'name.unique' => 'This name is already taken.',
            'currency.string' => 'The currency must be a valid string.',
            'currency.in' => 'The selected currency is invalid.',
            'separator.string' => 'The separator must be a string.',
            'separator.max' => 'The separator must be a single character.',
        ];
    }
}
