<?php

namespace App\Classes\Product;


class Attribute {

    public $attr;

    public function __construct()
    {
        return null;
    }

    public function singlelinetTextAttribute($regular_expression, $max_value, $min_value, $value_type, $shopify_id ,$required)
    {
        $arr = ["regex" => $regular_expression, "max" => $max_value, "min" => $min_value, 'value_type' => $value_type,'shopify_id' => $shopify_id ,"required" => $required, 'type' => 'single_line'];
        if(!isset($regular_expression))
        {
            unset($arr["regex"]);
        }

        if(!isset($max_value))
        {
            unset($arr["max"]);
        }

        if(!isset($min_value))
        {
            unset($arr["min"]);
        }

        if(!isset($value_type))
        {
            unset($arr["value_type"]);
        }

        if(!isset($shopify_id))
        {
            unset($arr["shopify_id"]);
        }

        if(!isset($required))
        {
            unset($arr["required"]);
        }

        return json_encode($arr);
    }

    public function numberAttribute($max_value, $min_value, $max_precision, $type, $value_type, $shopify_id ,$required)
    {
        $arr = ["max" =>  $max_value, "min" => $min_value, 'precision' => $max_precision ,"type" => $type, 'value_type' => $value_type,'shopify_id' => $shopify_id ,"required" => $required];

        if(!isset($max_value))
        {
            unset($arr["max"]);
        }

        if(!isset($min_value))
        {
            unset($arr["min"]);
        }

        if(!isset($max_precision))
        {
            unset($arr["precision"]);
        }

        if(!isset($value_type))
        {
            unset($arr["value_type"]);
        }

        if(!isset($shopify_id))
        {
            unset($arr["shopify_id"]);
        }

        if(!isset($required))
        {
            unset($arr["required"]);
        }

        return json_encode($arr);
    }

    public function multilineTextAttribute($regular_expression, $max_value, $min_value, $value_type, $shopify_id ,$required)
    {
        $arr = ["regex" => $regular_expression, "max" => $max_value, "min" => $min_value, 'value_type' => $value_type,'shopify_id' => $shopify_id ,"required" => $required, 'type' => 'multi_line'];

        if(!isset($regular_expression))
        {
            unset($arr["regex"]);
        }

        if(!isset($max_value))
        {
            unset($arr["max"]);
        }

        if(!isset($min_value))
        {
            unset($arr["min"]);
        }

        if(!isset($value_type))
        {
            unset($arr["value_type"]);
        }

        if(!isset($shopify_id))
        {
            unset($arr["shopify_id"]);
        }

        if(!isset($required))
        {
            unset($arr["required"]);
        }

        return json_encode($arr);
    }

    public function multiSelectAttribute($value_type, $shopify_id ,$required , $type = 'choices')
    {
        $arr = ['value_type' => $value_type,'shopify_id' => $shopify_id ,"required" => $required, 'type'=>$type];

        if(!isset($value_type))
        {
            unset($arr["value_type"]);
        }

        if(!isset($shopify_id))
        {
            unset($arr["shopify_id"]);
        }

        if(!isset($required))
        {
            unset($arr["required"]);
        }
        if(!isset($type))
        {
            unset($arr["type"]);
        }

        return json_encode($arr);
    }

    public function dateAndTimeAttribute($start_date, $end_date, $type, $value_type, $shopify_id ,$required)
    {
        $arr = ['min'=> $start_date, 'max' => $end_date, 'type' => $type, 'value_type' => $value_type,'shopify_id' => $shopify_id ,"required" => $required];

        if(!isset($start_date))
        {
            unset($arr["min"]);
        }

        if(!isset($end_date))
        {
            unset($arr["max"]);
        }

        if(!isset($value_type))
        {
            unset($arr["value_type"]);
        }

        if(!isset($shopify_id))
        {
            unset($arr["shopify_id"]);
        }

        if(!isset($required))
        {
            unset($arr["required"]);
        }

        return json_encode($arr);
    }

    public function fileAttribute($file_type, $file, $value_type, $shopify_id ,$required)
    {
        $arr = ["type" => $file_type, "file" => $file, 'value_type' => $value_type,'shopify_id' => $shopify_id ,"required" => $required];

        if (!isset($file_type)) {
            unset($arr['type']);
        }

        if (!isset($file)) {
            unset($arr['file']);
        }

        if(!isset($value_type))
        {
            unset($arr["value_type"]);
        }

        if(!isset($shopify_id))
        {
            unset($arr["shopify_id"]);
        }

        if(!isset($required))
        {
            unset($arr["required"]);
        }

        return json_encode($arr);
    }

    public function measurementAttribute($type,$max_unit, $min_unit ,$max_value, $min_value, $value_type, $shopify_id ,$required)
    {
        $arr = ["type"=> $type,'min_unit'=>$min_unit , 'max_unit'=>$max_unit, 'min' =>$min_value, 'max'=>$max_value, 'value_type' => $value_type,'shopify_id' => $shopify_id ,"required" => $required];

        if (!isset($type)) {
            unset($arr['type']);
        }

        if(!isset($min_unit))
        {
            unset($arr["min_unit"]);
        }

        if(!isset($max_unit))
        {
            unset($arr["max_unit"]);
        }

        if(!isset($max_value))
        {
            unset($arr["max"]);
        }

        if(!isset($min_value))
        {
            unset($arr["min"]);
        }

        if(!isset($value_type))
        {
            unset($arr["value_type"]);
        }

        if(!isset($shopify_id))
        {
            unset($arr["shopify_id"]);
        }

        if(!isset($required))
        {
            unset($arr["required"]);
        }

        return json_encode($arr);
    }

    public function ratingAttribute($max_value, $min_value, $value_type, $shopify_id ,$required)
    {
        $arr = [ "max" => $max_value, "min" => $min_value, 'value_type' => $value_type,'shopify_id' => $shopify_id ,"required" => $required, 'type' => "integer" /*adding this hardcode key for the frontend requirement from iqtidar*/];

        if(!isset($max_value))
        {
            unset($arr["max"]);
        }

        if(!isset($min_value))
        {
            unset($arr["min"]);
        }

        if(!isset($value_type))
        {
            unset($arr["value_type"]);
        }

        if(!isset($shopify_id))
        {
            unset($arr["shopify_id"]);
        }

        if(!isset($required))
        {
            unset($arr["required"]);
        }

        return json_encode($arr);
    }

    public function jsonAttribute($value_type, $shopify_id ,$required)
    {
        $arr = ['json', 'value_type' => $value_type,'shopify_id' => $shopify_id ,"required" => $required];

        if(!isset($value_type))
        {
            unset($arr["value_type"]);
        }

        if(!isset($shopify_id))
        {
            unset($arr["shopify_id"]);
        }

        if(!isset($required))
        {
            unset($arr["required"]);
        }

        return json_encode($arr);
    }

    public function booleanAttribute($value_type, $shopify_id ,$required)
    {
        $arr = ['type'=>'boolean', 'value_type' => $value_type,'shopify_id' => $shopify_id ,"required" => $required];

        if(!isset($value_type))
        {
            unset($arr["value_type"]);
        }

        if(!isset($shopify_id))
        {
            unset($arr["shopify_id"]);
        }

        if(!isset($required))
        {
            unset($arr["required"]);
        }

        return json_encode($arr);
    }

    public function urlAttribute($value_type, $shopify_id ,$required)
    {
        $arr = ['type'=>'url', 'value_type' => $value_type,'shopify_id' => $shopify_id ,"required" => $required];

        if(!isset($value_type))
        {
            unset($arr["value_type"]);
        }

        if(!isset($shopify_id))
        {
            unset($arr["shopify_id"]);
        }

        if(!isset($required))
        {
            unset($arr["required"]);
        }

        return json_encode($arr);
    }

    public function colorAttribute($value_type, $shopify_id ,$required)
    {
        $arr = ['type'=>'color', 'value_type' => $value_type,'shopify_id' => $shopify_id ,"required" => $required];

        if(!isset($value_type))
        {
            unset($arr["value_type"]);
        }

        if(!isset($shopify_id))
        {
            unset($arr["shopify_id"]);
        }

        if(!isset($required))
        {
            unset($arr["required"]);
        }

        return json_encode($arr);
    }

}
