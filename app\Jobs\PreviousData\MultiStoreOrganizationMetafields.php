<?php

namespace App\Jobs\PreviousData;

use App\Jobs\PreviousData\ProcessOrganizationChunk;
use App\Models\Organization\Organization;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Bus;


class MultiStoreOrganizationMetafields implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $organization_ids = Organization::query()
            ->withoutGlobalScopes()
            ->with('channels')
            ->has('channels','>=',2)
            ->get()
            ->pluck('id')
            ->toArray();

        $organizationChunks = array_chunk($organization_ids, 1); // Divide IDs into chunks of 10
        $batch = Bus::batch([])->dispatch();
        foreach ($organizationChunks as $chunk) {
            $batch->add(new ProcessOrganizationChunk($chunk));
        }
        /*
         *
         * For testing the data
         *
         * */
    /*    $channels = Channel::query()
            ->has('shopify_channels')
            ->withoutGlobalScopes()
            ->whereIn('organization_id',$organization_ids)
            ->get();

        foreach($channels as $channel){
            $attributes = Attribute::withoutGlobalScopes()
                ->with('families')
                ->where('organization_id',$channel->organization_id)
                ->whereHas('families',function($q){
                    $q->where('is_default',0);
                })
                ->get();
            foreach ($attributes as $attribute) {

                $rules = json_decode($attribute->rules, true);

                if(!empty($rules)){
                    $attr_channel = new AttributeChannel();
                    $attr_channel->channel_id = $channel->id;
                    $attr_channel->attribute_id = $attribute->id;
                    $attr_channel->store_connect_type = 'shopify';
                    $attr_channel->store_connect_id = $rules['shopify_id']??null;
                    $attr_channel->save();
                }
                (new CreateMetaFieldDefinition($channel->id,$channel->organization_id,false))
                    ->setData($attribute)
                    ->create();

            }
        }*/
    }
}
