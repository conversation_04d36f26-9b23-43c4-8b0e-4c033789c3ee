import React from "react";
import { Layout } from "antd";
import Logo from "../../../../public/v2/images/logo.png";
import { router } from "@inertiajs/react";

const { Header } = Layout;

const MinimalHeader = () => {
    return (
        <Header className="bg-white px-[30px] border border-[#D9D9D9] flex items-center h-[80px]">
            <div className="flex items-center">
                <span onClick={() => router.visit("/dashboard")}>
                    <img src={Logo} alt="Logo" className="w-36 cursor-pointer" />
                </span>
            </div>
        </Header>
    );
};

export default MinimalHeader;
