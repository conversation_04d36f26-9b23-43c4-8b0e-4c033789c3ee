<?php

namespace App\Jobs\Shopify;

use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use App\Models\Product\Category;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class StoreCustomCollectionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    public array $data = [];
    /**
     * Create a new job instance.
     */
    public function __construct(array $data = [])
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            foreach($this->data['collections'] as $collection){
                $array = [];
                $cat = Category::where('organization_id',$this->data['organization_id'])
                            ->where('name',$collection->title)
                            ->first();
                if($cat){
                    $array['id'] =$cat->id;
                }
    
                $array['name'] = $collection->title;
                $array['description'] = $collection->body_html;
                $array['organization_id'] = $this->data['organization_id'];
               // $collection_id = ['type'=>'shopify','collection_id'=>$collection['id']];
                $array['response'] = ['type'=>'shopify','collection_id'=>$collection->id,'channel_id'=> $this->data['channel_id']];
    
                $category = new Category();
                $category->set_data($array)->store(function () {
                    Log::channel('shopify')->info('error in saving category in db.');
                }, function () {
                    Log::channel('shopify')->info('success in saving cat.');
                });
            }
        } catch (\Exception $e) {
            Log::channel('shopify')->error([$e->getMessage()]);
        }

    }
}
