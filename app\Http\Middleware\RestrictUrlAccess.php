<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Organization\Organization;
use App\Models\BrandsPortal;
use App\User;
use Illuminate\Support\Facades\Auth;

class RestrictUrlAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        $hashToken = $request->header('X-Hash-Token');
        if ($hashToken) {
            $brandPortalId = (int) urldecode(decrypt($hashToken));
            $brandportal = BrandsPortal::withoutGlobalScopes()->find($brandPortalId);
            // Retrieve organization or other entity based on the hash token
            $organization = Organization::find($brandportal->organization_id);
            if (isset($organization)) {
                // Store the organization ID in the request for further use if needed
                $request->attributes->add(['organization_id' => $organization->id]);
                // Store the organization ID in the session for further use if needed
                session(['organization_id' => $organization->id]);
                // Attempt to find a user linked to this organization and authenticate
                $user = $organization->users_without_scope()->first();
                if ($user) {
                    Auth::setUser($user);

                    // Create a token for the authenticated user
                    $token = $user->createToken('API Token')->plainTextToken;
                }
            }
        }
        // Check if the user has access to the requested route
        if (!$this->isAllowed($request)) {
            return response()->json(['message' => 'Access Denied'], 403);
        }
        $response = $next($request);

        // Check if a token was created and append it to the response headers or body
        if ($token) {
            // To add the token to the response headers
            $response->headers->set('Authorization', 'Bearer ' . $token);
        }
        // Proceed with the request
        return $response;
    }

    private function isAllowed(Request $request)
    {
        // Define the allowed paths or conditions based on organization or token
        $allowedRoutes = [
            'filters.index',
            'filters.store',
            'categories.index',
            'categories.update',
        ];

        // Retrieve current route name from the request
        return in_array($request->route()->getName(), $allowedRoutes);
    }
}
