<?php

namespace App\Classes\Mapping;

use App\Models\Channel\Channel;
use App\Models\Location\Location;
use App\Models\Product\Template;
use App\Models\Product\Version;

class Conversion
{
    /**
     * fetch all version in array with id
     * @return array
     */
    public Static function fetch_all_version_in_array()
    {
        $versions = Version::all();
        $version_array = array();
        foreach ($versions as $version){
            $version_array[$version->id] = $version->name;
        }
        return $version_array;
    }
    /**
     * fetch all version in array with id
     * @return array
     */
    public Static function fetch_all_catalog_in_array()
    {
        $catalogs = Channel::all();
        $catalog_array = array();
        foreach ($catalogs as $catalog){
            $catalog_array[$catalog->id] = $catalog->name;
        }
        return $catalog_array;
    }

    /**
     * fetch all version in array with id
     * @return array
     */
    public Static function fetch_all_location_in_array()
    {
        $locations = Location::all();
        $location_array = array();
        foreach ($locations as $location){
            $location_array[$location->id] = $location->name;
        }
        return $location_array;
    }

    public Static function getTemplates(array $data){
      return  Template::query()
                  ->where([
                  'channel_id'=>$data['channel_id'],
                  'type'=>$data['type']])
                  ->get();
    }

}
