<?php

namespace App\Jobs;

use App\Jobs\Shopify\FetchCollections;
use App\Jobs\Shopify\FetchMetaFieldDefinitions;
use App\Jobs\Shopify\FetchMetaFields;
use App\Jobs\Shopify\LocationJob;
use App\Jobs\Shopify\UpdateInventoryJob;
use App\Models\Channel\ShopifyChannel;
use App\Models\EventStatusHandler;
use App\Notifications\ApimioNotification;
use App\Traits\AWSSetting;
use App\User;
use Ghazniali95\ShopifyConnector\App\Classes\Services\ProductService;
use Illuminate\Bus\Batch;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Storage;

class TestLambdaBatchTestJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use AWSSetting;

    /**
     * Create a new job instance.
     */
    public function __construct(public $event)
    {
        $this->initializeTrait();

    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        //test multiple queue running process
        $array['jobs'] = $this->event['jobs'];
        $array['batch_name'] = $this->event['batch_name'];
        $array['queue_name'] =$this->event['queue_name'];
        $this->jobBatchingRunner($array);

        // fetch shopify testing batching queue


      /*  $event = (object)$this->event;
        $res = (object)$this->event;
        //  $queue_key =mt_rand(0, 999).$event->channel_id;
        $queue_name = $event->queue_key;
        //$this->createSQS($queue_key);

        if ($res) {
            $channel = ShopifyChannel::query()->where('channel_id', $event->channel_id)->first();
            if ($channel) {

                //============= for batching ================//
                //start batching
                $batch = Bus::batch([])
                    ->then(function (Batch $batch) use ($event){
                        $batch->add([
                            new FetchMetaFieldDefinitions($event),
                            new FetchMetaFields($event),
                            new FetchCollections($event),
                            new LocationJob($event),
                            new UpdateInventoryJob($event)
                        ]);
                    })
                    ->finally(function (Batch $batch) use ($event,$queue_name) {
                        info('Batching completed. and now sending notification.');
                        // All jobs completed successfully
                        $path = route('products.index');
                        $details = [
                            'subject' => 'Shopify Products import to Apimio successfully.',
                            'greeting' => 'Hi ',
                            'body' => "Your import Shopify products queue is processed successfully. Please upgrade plan to add more products.",
                            'thanks' => 'Thank you for using ' . request()->getHttpHost(),
                            'actionText' => 'View',
                            'actionURL' => url($path),
                            'user_id' => $event->user_id,
                            'organization_id' => $event->organization_id,
                        ];

                        $notifier = new ApimioNotification($details);

                        //un-comment if you want to stop notification in email
                        $notifier->only_db_notify(true);
                        $user  = User::query()->find($event->user_id);
                        $user->notify($notifier);
                        $e = new EventStatusHandler();
                        $e->set_data(['organization_id' => $event->organization_id])->delete_event();
                        $this->deleteQueue($queue_name);
                    })
                    ->name('shopify fetch products')
                    ->onConnection('sqs')
                    ->onQueue($queue_name)
                    ->dispatch();
                $count = 0;
                //fetch and save PRODUCTS
                $productService = new ProductService($event->channel_id);
                $productService->getAll(function ($products) use ($event, $batch,$count) {
                    // Chunk the products into groups of 5
                    $productChunks = array_chunk($products, 5);
                    foreach ($productChunks as $chunk) {

                        $processableProducts = [];
                        foreach ($chunk as $product) {
                            $processableProducts[] = $product;
                        }
                        info('count starts,'. $count++);
                        if (!empty($processableProducts)) {
                            $s3Url = $this->saveToS3($processableProducts, 'shopify-products-' . $count);
                            $batch->add(new \App\Jobs\Shopify\FetchShopifyProduct($s3Url, $event));
                        }
                    }
                }, ['limit' => 250]);


            }
        }*/
    }

    function saveToS3($data, $fileName) {
        $path = 'queue-payload/' . $fileName . '.json';
        Storage::disk('s3')->put($path, json_encode($data));
        return $path;
    }
}
