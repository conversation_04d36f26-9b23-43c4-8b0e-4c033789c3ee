<?php

namespace App\Classes\Template;

/**
 * Generate payload for nodes to save in database.
 *
 * @test php vendor/bin/phpunit tests/Unit/TemplateGenerateNodeTest.php
 * */
class GenerateNode
{

    /**
     * Generate object from array.
     *
     * @param array $input array with information about formulas and fields.
     *
     * @return string
     * */
    public function generateObjectNode(array $input) : string {
        return json_encode($input);
    }

    /**
     * Convert array from object.
     *
     * @param string $input
     *
     * @return array
     * */
    public function convertObjectNode(string $input) : array {
        return json_decode($input, true);
    }
}
