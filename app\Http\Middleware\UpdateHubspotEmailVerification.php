<?php

namespace App\Http\Middleware;

use App\Services\Marketing\Hubspot\Hubspot;
use Closure;
use Doctrine\DBAL\Driver\Exception;
use Illuminate\Http\Request;

class UpdateHubspotEmailVerification
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            $hubspot = new Hubspot(\Auth::user());
            $hubspot->update("email_verified", "true");
        } catch (Exception $e) {
            \Log::error($e);
        }
        return $next($request);
    }
}
