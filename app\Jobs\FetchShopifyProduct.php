<?php

namespace App\Jobs;

use App\Models\Channel\ShopifyChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Product\Product;

class FetchShopifyProduct implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $max_product = 10000;
        $product_limit = 250 ;
        if ($this->data['organization_id']) {
            $p = new Product();
            $max_product = $p->plan_products($this->data['organization_id']);
        }
        if ($max_product < 250) {
            $product_limit = $max_product;
        }
        (new ShopifyChannel())->set_data(
            ['channel_id'=>$this->data['channel_id'],
                'organization_id'=>$this->data['organization_id'],
                'product_limit'=>$product_limit,
                'max_products' => $max_product
            ])->fetch_product(function($error){
                return back()->withErrors($error);
            },function(){
                return back()->withSuccess('Products sync successfully');
            });

    }
}
