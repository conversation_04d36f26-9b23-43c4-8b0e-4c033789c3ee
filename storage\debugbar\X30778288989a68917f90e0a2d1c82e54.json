{"__meta": {"id": "X30778288989a68917f90e0a2d1c82e54", "datetime": "2025-05-27 07:17:41", "utime": **********.843943, "method": "GET", "uri": "/api/2024-12/organization", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748330260.677327, "end": **********.843976, "duration": 1.1666491031646729, "duration_str": "1.17s", "measures": [{"label": "Booting", "start": 1748330260.677327, "relative_start": 0, "end": **********.329949, "relative_end": **********.329949, "duration": 0.6526219844818115, "duration_str": "653ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.329969, "relative_start": 0.652642011642456, "end": **********.84398, "relative_end": 4.0531158447265625e-06, "duration": 0.5140111446380615, "duration_str": "514ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 31116032, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/organization", "middleware": "api, auth:sanctum", "as": "organization.index", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@index", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=28\" onclick=\"\">app/Http/Controllers/Api/OrganizationController.php:28-39</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0054199999999999995, "accumulated_duration_str": "5.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.451664, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 49.077}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 3 and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 3)", "type": "query", "params": [], "bindings": [3, 3], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 37}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.696795, "duration": 0.00276, "duration_str": "2.76ms", "memory": 0, "memory_str": null, "filename": "OrganizationController.php:37", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=37", "ajax": false, "filename": "OrganizationController.php", "line": "37"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 49.077, "width_percent": 50.923}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Organization\\Organization": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/organization\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq"}, "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f027c6b-c5a9-4691-8615-ac0e8f2c2382\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/2024-12/organization", "status_code": "<pre class=sf-dump id=sf-dump-938682152 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-938682152\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2124748544 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2124748544\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1880008780 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1880008780\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1708070955 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ikg3U1lLeFlQMFRRN05IcW0zRU9Wb1E9PSIsInZhbHVlIjoibENCNDlma0ZjTFMrVkJ1dTNuT1N3RjdkMGd6ZTI0emI2NnNPd2V2L1BuWjRpNDloN1hld3d5MkcxWjVUd1UySmp0YkVGUGExc1RCNzV6Y2NCV2dlV2w3RFVSWDVKNkc0c1dxcnFVbVg4V2U3eXh4ckhaOFpLQTlzZ0txUCtLcEoiLCJtYWMiOiJkMzFlMzVhNWI4NmY2NjdkMjU0ODcyYzJmZDE0MzZhNjI3NDRmMGM0ZmY2OWQ0YmViZGUxMDlmMzgxYTlmNTA5IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost:8000/organization/select</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1317 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlBnUFZnSUIxd2RabTBsbnNiNVpmTUE9PSIsInZhbHVlIjoibEYxTENta2l3WE5hdnQ2UGVMeE9KU2lZdEE4YitHM1dsNlpJMDRpTUROUUtuc2FhU2l6Qnl4aklkYXZpK0l5WXhCT0g3Yi9UTE5XUXlpWUtUVFM4OXNkTG5BYnRtRlk2cUtuOUdKVC9NYmpQRmNSSWFCL20wSEVBekE0WVQ4L1dpZ21xVkE2bmJDblVGdWtZYmdhenprZ1J4NzFFZjFHWEs4NDhGM0l6ZkJ5RGJSRVAwVTFpSUFjNE5jOGdXY2JLcU02VWRjc2lwZWQxRzBHNlZSUFEydERmdW5CN3UvdGJwT2VkMDIwd08xQT0iLCJtYWMiOiJiYmM1ZTEzODhjYjA3MzUyMDE3Zjc0ZTI0YWE3N2RjNjUxYmJjOTgzOWZkYjJiNWI0ZTIzNWU0MjdiZjFlOTBhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ikg3U1lLeFlQMFRRN05IcW0zRU9Wb1E9PSIsInZhbHVlIjoibENCNDlma0ZjTFMrVkJ1dTNuT1N3RjdkMGd6ZTI0emI2NnNPd2V2L1BuWjRpNDloN1hld3d5MkcxWjVUd1UySmp0YkVGUGExc1RCNzV6Y2NCV2dlV2w3RFVSWDVKNkc0c1dxcnFVbVg4V2U3eXh4ckhaOFpLQTlzZ0txUCtLcEoiLCJtYWMiOiJkMzFlMzVhNWI4NmY2NjdkMjU0ODcyYzJmZDE0MzZhNjI3NDRmMGM0ZmY2OWQ0YmViZGUxMDlmMzgxYTlmNTA5IiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IndSWnZOSmFpejZtZGFDWFQxaCtqQUE9PSIsInZhbHVlIjoiYitsVlBjbDVMOE54bjhrNmQ4UlVsQmtnWDFlbHRkNmtJeEtJeTcxNDJEKzNqY3o4TFBpb3NOQkZ4aUUrZ0RsRVNuMnhxNGhZWGwyMGl6elpiVkJKYXkwOVJQK3RGRjVWZmNIcG9XNlI4ZUFmTzFKTUhPWG5GQUR5K3pWejJxWDYiLCJtYWMiOiIyM2M1OWVkOGU2ODJkNzc4ZjE2MDkyNzE0YzczNzU4M2Q1YzdlNDM2YzkzMzlhMjJlYTBiNzVjNTJhNDEzMWFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1708070955\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-752921500 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">3|lyOnN9rFaNXD362RGbrGZMM746BZDyLiYeGFkfAHYF0FfGnJZdaE7RGsFJsg|$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k1WJWB6ZnSJcQ4dYcV3CZVMdsUzKOM46xMgpx7dI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752921500\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-95165466 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 27 May 2025 07:17:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkRIS1NvaXBoUGdiV1BoeGlFeklSd1E9PSIsInZhbHVlIjoiUUE5Ulhyc1dxVElhSTB3RGV3Y3FqN3ZSUGZEeWNDSWtxZnhRdU1PY21TaGhoNm9mQXBxdDE4VjRBWEduWmk2V3VMbWpTRmpKWlNmclVzdGFHdEhUNVhWa0ZsSVZGSjNHS0R1aUIxWThBVXJ5MkhyL3lvR2xMRDc2eWpyWjJJcC8iLCJtYWMiOiIyZjM4NWQ3ZmE4MDg5YjAwYTYwMzVlNzhhYTM4ZGU0NzdjNTU4MzRjNTEwMTliOWRkODEyOTFhMDM3NzdmZjhkIiwidGFnIjoiIn0%3D; expires=Tue, 27 May 2025 09:17:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6IkpsOWtoOTVuN0d1WGFFeUM1WXphMkE9PSIsInZhbHVlIjoiZTlYbDVtak5ZNk5PT2ZjMnVzRTY2RTJ1TG5JWWhvL3lkRE5YU1JxT3ByWHJYUTdaOTh1ZUZlUXl0N0lvcFYyM3pRUXdLTjNJNW5sbnEvR2MvT0N5eHJvaFZTNnhVZ3FZY2FwZmRiNW9WU2RIeHMwUjVVV1pPZVJiR1NWTXhGM3EiLCJtYWMiOiIzMmFiYmI5NjNjYWEzNzEzNDA4ZDg3YWVmYjFmODZiMTViNmExY2ZjMTQ4NmYwNjI3MjYzYzdkNjM5MTczNWQ4IiwidGFnIjoiIn0%3D; expires=Tue, 27 May 2025 09:17:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkRIS1NvaXBoUGdiV1BoeGlFeklSd1E9PSIsInZhbHVlIjoiUUE5Ulhyc1dxVElhSTB3RGV3Y3FqN3ZSUGZEeWNDSWtxZnhRdU1PY21TaGhoNm9mQXBxdDE4VjRBWEduWmk2V3VMbWpTRmpKWlNmclVzdGFHdEhUNVhWa0ZsSVZGSjNHS0R1aUIxWThBVXJ5MkhyL3lvR2xMRDc2eWpyWjJJcC8iLCJtYWMiOiIyZjM4NWQ3ZmE4MDg5YjAwYTYwMzVlNzhhYTM4ZGU0NzdjNTU4MzRjNTEwMTliOWRkODEyOTFhMDM3NzdmZjhkIiwidGFnIjoiIn0%3D; expires=Tue, 27-May-2025 09:17:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6IkpsOWtoOTVuN0d1WGFFeUM1WXphMkE9PSIsInZhbHVlIjoiZTlYbDVtak5ZNk5PT2ZjMnVzRTY2RTJ1TG5JWWhvL3lkRE5YU1JxT3ByWHJYUTdaOTh1ZUZlUXl0N0lvcFYyM3pRUXdLTjNJNW5sbnEvR2MvT0N5eHJvaFZTNnhVZ3FZY2FwZmRiNW9WU2RIeHMwUjVVV1pPZVJiR1NWTXhGM3EiLCJtYWMiOiIzMmFiYmI5NjNjYWEzNzEzNDA4ZDg3YWVmYjFmODZiMTViNmExY2ZjMTQ4NmYwNjI3MjYzYzdkNjM5MTczNWQ4IiwidGFnIjoiIn0%3D; expires=Tue, 27-May-2025 09:17:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-95165466\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-747226616 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://localhost:8000/api/2024-12/organization</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747226616\", {\"maxDepth\":0})</script>\n"}}