<?php

namespace App\Classes\Shopify;

class ArrayCreate extends MetaFieldDefinition
{
    public function __construct(int $channel_id, int $organization_id , public $version_id = null)
    {
        parent::__construct($channel_id, $organization_id);
    }

    public function metaFieldArray(){
        $data = $this->data;
        $array = [];
        $count = 0;
        if(!isset($data['data'])){
           return ($array);
        }
        foreach ($data['data']['metafieldDefinitions']['edges'] as $key => $attr){
            $array_key = in_array($attr['node']['namespace'], array_column($array, 'name'));
            if ($key === array_key_first($array)) {
                $array[$count]['name'] = $attr['node']['namespace'];
                $array_key = $count;
            }
            if(!$array_key ){
                $count++;
                $array[$count]['name'] = $attr['node']['namespace'];
                $array_key = $count;
            }

            $array[$array_key]['attributes'][$attr['node']['key']] =$attr['node']['name'] ;
        }
        return ($array);
    }

    public function mainArray(){

        //shopify array
        $default[] =  [

            'name' => 'Shopify',
            'attributes' => [
                'handle' => 'Handle',
                'title' => 'Title',
                'body_html' => 'Body (HTML)',
                'sku' => 'SKU',
                'weight' => 'Weight',
                'price' => 'Price',
                'compare_at_price' => 'Compare At Price',
                'image_src' => 'Image Src',
                'barcode' => ' Barcode',
                'vendor' => 'Vendor',
                'status' => 'Status',
                'metafields_global_title_tag' => 'SEO Title',
                'seo_url' => 'SEO URL',
                'metafields_global_description_tag' => 'SEO Description',
                'tags' => 'Tags',
                'product_type' => 'Product Type',
//                'inventory_quantity' => 'Quantity',
//                'track_quantity' => 'Track Quantity',
//                'continue_selling' => 'Continue Selling',
            ]
        ];
        $variants[] = [

            'name' => 'Variants',
            'attributes' => [
                "sku" => "SKU",
                "title" => "Title",
                "price" => "Price",
                "quantity" => "Quantity",
                "barcode" => "Barcode",
                "image"=>'Image Src',
                "compare_at_price" => "Compare At Price",
                "weight" => "Weight",
                "weight_unit" => "Weight Unit",
                "inventory_quantity" => 'Inventory Quantity',
                "track_quantity" => "Track Quantity",
                "continue_selling" => "Continue Selling",
            ]
        ];
        $array_merge = array_merge($default, $this->metaFieldArray());
        $array_merge = array_merge($array_merge, $variants);
        $shopify_array = [
            'array_name' => "Shopify",
            'nodes' =>
                $array_merge

        ];

        return ($shopify_array);
    }


    public function default_shopify_template_mapping()
    {
        return [
            'organization_id' => $this->organization_id,
            'temp_type' => 'shopify',
            'nodes' => [
                "data" => [
//                    [
//                        "from" => ["Variant,sku"],
//                        "with_formula" => "assign",
//                        "to" => ["Shopify,sku"],
//                    ],
                    [
                        "from" => ["Default,status"],
                        "with_formula" => "assign",
                        "to" => ["Shopify,status"],
                    ],
                    [
                        "from" => ["Default,vendor"],
                        "with_formula" => "assign",
                        "to" => ["Shopify,vendor"],
                    ],
                    // [
                    //     "from" => ["Default,track_quantity"],
                    //     "with_formula" => "assign",
                    //     "to" => ["Shopify,track_quantity"],
                    // ],
                    // [
                    //     "from" => ["Default,continue_selling"],
                    //     "with_formula" => "assign",
                    //     "to" => ["Shopify,continue_selling"],
                    // ],
//                    [
//                        "from" => ["Variant,name"],
//                        "with_formula" => "assign",
//                        "to" => ["Shopify,title"],
//                    ],
                    [
                        "from" => ["Default,handle"],
                        "with_formula" => "assign",
                        "to" => ["Shopify,handle"],
                    ],
//                    [
//                        "from" => ["Variant,barcode"],
//                        "with_formula" => "assign",
//                        "to" => ["Shopify,barcode"],
//                    ],
//                    [
//                        "from" => ["Variant,price"],
//                        "with_formula" => "assign",
//                        "to" => ["Shopify,price"],
//                    ],
//                    [
//                        "from" => ["Variant,compare_at_price"],
//                        "with_formula" => "assign",
//                        "to" => ["Shopify,compare_at_price"],
//                    ],
//                    [
//                        "from" => ["Default,Quantity"],
//                        "with_formula" => "assign",
//                        "to" => ["Shopify,inventory_quantity"],
//                    ],
//                    [
//                        "from" => ["Variant,weight"],
//                        "with_formula" => "assign",
//                        "to" => ["Shopify,weight"],
//                    ],
                    [
                        "from" => ["General,product_name"],
                        "with_formula" => "assign",
                        "to" => ["Shopify,title"],
                    ],
                    [
                        "from" => ["General,description"],
                        "with_formula" => "assign",
                        "to" => ["Shopify,body_html"],
                    ],
                    [
                        "from" => ["SEO,seo_title"],
                        "with_formula" => "assign",
                        "to" => ["Shopify,metafields_global_title_tag"],
                    ],
                    [
                        "from" => ["SEO,seo_description"],
                        "with_formula" => "assign",
                        "to" => ["Shopify,metafields_global_description_tag"],
                    ],
                    [
                        "from" => ["SEO,seo_keyword"],
                        "with_formula" => "assign",
                        "to" => ["Shopify,tags"],
                    ],
                    [
                        "from" => ["Default,file"],
                        "with_formula" => "assign",
                        "to" => ["Shopify,image_src"],
                    ],
                    [
                        "from" => ["Variant,name"],
                        "with_formula" => "assign",
                        "to" => ["Variants,title"],
                    ],
                    [
                        "from" => ["Variant,price"],
                        "with_formula" => "assign",
                        "to" => ["Variants,price"],
                    ],
                    [
                        "from" => ["Variant,quantity"],
                        "with_formula" => "assign",
                        "to" => ["Variants,inventory_quantity"],
                    ],
                    [
                        "from" => ["Variant,barcode"],
                        "with_formula" => "assign",
                        "to" => ["Variants,barcode"],
                    ],
                    [
                        "from" => ["Variant,file"],
                        "with_formula" => "assign",
                        "to" => ["Variants,image"],
                    ],
                    [
                        "from" => ["Variant,sku"],
                        "with_formula" => "assign",
                        "to" => ["Variants,sku"],
                    ],
                    [
                        "from" => ["Variant,compare_at_price"],
                        "with_formula" => "assign",
                        "to" => ["Variants,compare_at_price"],
                    ],
                    [
                        "from" => ["Variant,weight"],
                        "with_formula" => "assign",
                        "to" => ["Variants,weight"],
                    ],
                    [
                        "from" => ["Variant,weight_unit"],
                        "with_formula" => "assign",
                        "to" => ["Variants,weight_unit"],
                    ],
                    [
                        "from" => ["Variant,track_quantity"],
                        "with_formula" => "assign",
                        "to" => ["Variants,track_quantity"],
                    ],
                    [
                        "from" => ["Variant,continue_selling"],
                        "with_formula" => "assign",
                        "to" => ["Variants,continue_selling"],
                    ]
                ],
            ],
            'version' => $this->version_id,
            'catalog' => $this->channel_id,
            'status' => '1',
            'temp_status' => 'on',
            'temp_name' => 'Shopify Default',
        ];
    }

}
