<?php

namespace App\Models\Channel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ChannelProductStatus extends Model
{
    protected $guarded = [];
    protected $casts  = [
        'response' => 'object',
    ];
    public static function boot()
    {
        parent::boot();
        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            }
        });
        static::creating(function ($model) {
            if (Auth::check()) {
                $model->organization_id = Auth::user()->organization_id;
            }
        });
    }

    public function channel_product()
    {
        return $this->belongsTo(ChannelProduct::class,'channel_product_id','id');
    }
}
