<?php

namespace App\Jobs\PreviousData;

use App\User;
use stdClass;
use Illuminate\Bus\Queueable;
use App\Jobs\Shopify\LocationJob;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use App\Models\Channel\ShopifyChannel;
use Illuminate\Queue\SerializesModels;
use App\Jobs\Shopify\UpdateInventoryJob;
use Illuminate\Queue\InteractsWithQueue;
use App\Notifications\ApimioNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;

class InventoryJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public array $data)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    { 
        try {
            //start batching
            $user = $this->data['user'];
            $shopify_channel = ShopifyChannel::query()->where('channel_id', $this->data["channel_id"])->first();
            $batch = Bus::batch([])
                ->then(function () use ($user) {
                    // All jobs completed successfully
                    $path = route('products.index');
                    $details = [
                        'subject' => 'Shopify location import to Apimio successfully.',
                        'greeting' => 'Hi ',
                        'body' => "Your import Shopify location queue is processed successfully.",
                        'thanks' => 'Thank you for using ' . request()->getHttpHost(),
                        'actionText' => 'View',
                        'actionURL' => url($path),
                        'user_id' => $user->user_id,
                        'organization_id' => $user->organization_id,
                    ];

                    $notifier = new ApimioNotification($details);

                    //un-comment if you want to stop notification in email
                    $notifier->only_db_notify(true);
                    $user->notify($notifier);
                })->dispatch(); 
            $obj = new stdClass();
            $obj->organization_id = $this->data['organization_id']; // You can set your desired value
            $obj->channel_id = $this->data["channel_id"];      // You can set your desired value
            //FETCH and save Location
            $batch->add(new LocationJob($obj)); 
            // Update Inventory Location
            $batch->add(new UpdateInventoryJob($obj));
        } catch (\Exception $e) {
            Log::channel('shopify')->error($e);
        }
    }
}
