<?php

namespace App\Models\Channel;

use App\Models\Product\Product;
use App\Models\Product\Inventory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChannelProduct extends Model
{
  protected $guarded = [];

  protected $table = 'channel_product';

  public static function boot()
  {
          parent::boot();

          static::created(function ($model) {
               $product = Product::find($model->product_id);
                if($product){
                    foreach($product->variants as $variant){
                        $channelLocations = ChannelLocation::where('location_id', $model->channel_id)->get();
                        foreach ($channelLocations as $key => $location) {
                                $inventory = new Inventory();
                                $inventory->organization_id = $model->organization_id;
                                $inventory->product_id = $model->product_id;
                                $inventory->variant_id = $model->id;
                                $inventory->location_id = $location->id;
                                $inventory->store_type = 'shopify';
                                $inventory->available_quantity = null;
                                $model->save();
                        }
                    }
                    }


              });
}

    public function changeProductSyncStatus($data){
        $channel_products = ChannelProduct::query()
            ->where('channel_id',$data['channel_id'])
            ->whereIn('product_id',$data['product_ids'])->get();
      //  $channel_product_ids = $channel_product->pluck('id')->toArray();


        foreach($channel_products as $key=>$channel_product ){
            $status = ChannelProductStatus::query()
                ->where([
                    'channel_product_id'=>$channel_product->id,
                    'type'=>'shopify'
                ])
                ->first();

            if(!$status){
                $product= Product::query()->find($channel_product->product_id);
                $updated_at = $product->created_at;
                $status =new ChannelProductStatus();
                $status->channel_product_id =$channel_product->id;
                $status->type ='shopify';
                $status->created_at = $updated_at;
                //$updated_at = $channel_product[$key]->updated_at;
            }else{

                $updated_at = $status->updated_at;

            }
            $status->timestamps = false;

            $status->updated_at = $updated_at??now();
            $status->status =$data['status'];
            $status->save();

            $status->timestamps = true;
            //2023-03-17 08:53:11
        }
    }

    public static function changeSyncStatus($data): void
    {
        $channelProducts = ChannelProduct::with('product')
        ->where('channel_id', $data['channel_id'])
        ->whereIn('product_id', $data['product_ids'])
        ->get();

        foreach ($channelProducts as $channelProduct) {
            $status = ChannelProductStatus::firstOrCreate(
                [
                    'channel_product_id' => $channelProduct->id,
                    'type' => 'shopify'
                ],
                [
                    'created_at' => $channelProduct->product->created_at ?? now(),
                    'updated_at' => now(),
                    'status' => $data['status'],
                    'organization_id' => $data['organization_id'],
                    'response' => ['sync_id'=>$data['shopify_product_id']],
                ]
            );

            if (!$status->wasRecentlyCreated) {
                $status->updated_at = now();
                $status->status = $data['status'];
                $status->save();
            }
        }
    }

    /**
     *
     *Relationships
     *
     */
  public function status(){
      return $this->hasMany('App\Models\Channel\ChannelProductStatus','channel_product_id','id');
  }

  public function shopify_status(){
      return $this->hasOne(ChannelProductStatus::class,'channel_product_id','id')->where('type','shopify');
  }
  public function channel(){
      return $this->belongsTo('App\Models\Channel\Channel','channel_id ','id');
  }


  public function channelFileProducts() : HasMany
  {
      return $this->hasMany(ChannelFileProduct::class);
  }

  public function product() : BelongsTo
  {
      return $this->belongsTo(Product::class , 'product_id','id');
  }
}
