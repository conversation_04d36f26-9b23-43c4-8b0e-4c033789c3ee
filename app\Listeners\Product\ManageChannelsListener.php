<?php

namespace App\Listeners\Product;

use App\Models\Channel\Channel;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class ManageChannelsListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        $channel_ids = [];
        if (!$event->data) {
            $event->product->attach_default_channel();
            return;
        }
        foreach ($event->data as $channel) {
            if (array_key_exists('id',$channel)) {

                $channel_ids[] = $channel['id'];
            } elseif (array_key_exists('name',$channel)) {
                if (!empty($channel['name'])) {
                    if(array_key_exists('version_id',$channel)) {
                        $versionId = $channel['version_id'];
                    } else {
                        $versionId = null;
                    }
                    $channel_ids[] = Channel::findOrCreateForProductWithVersion($channel['name'], $event->product, $versionId)->id;
                }
            } else {
                $event->product->attach_default_channel();
            }
        }
        if ($event->refresh) {
            $event->product->channels()->sync(array_filter($channel_ids));
        } else {
            $event->product->channels()->syncWithoutDetaching(array_filter($channel_ids));
        }
    }
}
