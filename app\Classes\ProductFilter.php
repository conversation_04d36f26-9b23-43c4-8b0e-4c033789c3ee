<?php
namespace App\Classes;

use Illuminate\Database\Eloquent\Builder;

Class ProductFilter
{
    function __construct()
    {
        //
    }

    public function relation_attributes()
    {
        return [
            'categories' => 'categories',
            'brands' => 'brands',
            'versions' => 'versions',
            'channels' => 'channels',
            'vendors' => 'invites',
        ];
    }


    /**
     * @param $products
     * @param $or_filters
     * @return void
     */
    public function apply_filters(&$products, $or_filters)
    {


        $relation_attributes = $this->relation_attributes();

        $products->where(function ($filterproducts) use ($or_filters, $relation_attributes) {

            $main_relation = 'where';

            foreach ($or_filters as $and_filters) {

                $filterproducts->{$main_relation}(function ($query) use ($and_filters, $relation_attributes) {
                    foreach ($and_filters as $filter) {
                        if (isset($filter['attribute']) && $filter['attribute'] != null) {

                            if ($filter['attribute'] == 'handle') {
                                switch ($filter['formula']) {
                                    case 'is_equal_to':
                                        $query->where('sku', $filter['value']);
                                        break;
                                    case 'is_not_equal_to':
                                        $query->where('sku', '!=', $filter['value']);
                                        break;
                                    case 'contain':
                                        $query->where('sku', 'like', '%' . $filter['value'] . '%');
                                        break;
                                    case 'does_not_contain':
                                        $query->where('sku', 'not like', '%' . $filter['value'] . '%');
                                        break;
                                }
                            } elseif ($filter['attribute'] == 'status') {
                                $query->where('status', $filter['formula']);
                            } elseif ($filter['attribute'] == 'variants') {
                                switch ($filter['formula']) {
                                    case 'is_defined':
                                        // Products with variants
                                        $query->WhereHas('variants', function (Builder $q) {
                                            $q->havingRaw('COUNT(variants.id) > 1');
                                        });
                                        break;
                                    case 'is_not_defined':
                                        // Products with no variants or exactly one variant
                                        $query->whereDoesntHave('variants')
                                            ->orWhereHas('variants', function (Builder $q) {
                                                $q->havingRaw('COUNT(variants.id) = 1');
                                            });
                                        break;
                                }
                            } elseif (isset($filter['family']) && $filter['family'] == 'variants') {
                                $query->whereHas('variants', function (Builder $q) use ($filter) {
                                    if (isset($filter['attribute'])) {
                                        switch ($filter['formula']) {
                                            case 'is_defined':
                                                $q->whereNotNull("variants.{$filter['attribute']}");
                                                break;
                                            case 'is_not_defined':
                                                $q->whereNull("variants.{$filter['attribute']}");
                                                break;
                                            case 'is_equal_to':
                                                $q->where("variants.{$filter['attribute']}", $filter['value']);
                                                break;
                                            case 'is_not_equal_to':
                                                $q->where("variants.{$filter['attribute']}", '!=', $filter['value']);
                                                break;
                                            case 'contain':
                                                $q->where("variants.{$filter['attribute']}", 'like', '%' . $filter['value'] . '%');
                                                break;
                                            case 'does_not_contain':
                                                $q->where("variants.{$filter['attribute']}", 'not like', '%' . $filter['value'] . '%');
                                                break;
                                        }
                                    }

                                });
                            } elseif (isset($filter['json_data'])) {
                                $json_sub_relation = 'whereHas';
                                if (in_array($filter['formula'], ['is_not_equal_to', 'does_not_contain'])) {
                                    $query->whereDoesntHave('values');
                                    $json_sub_relation = 'orWhereHas';
                                }
                                $query->{$json_sub_relation}('values', function ($q) use ($filter) {
                                    $q->where('attribute_id', $filter['json_data']['attribute_id'] ?? null)
                                        ->where('family_id', $filter['json_data']['family_id'] ?? null);

                                    $filter_value = $filter['value'] ?? "";

                                    switch ($filter['formula']) {
                                        case 'is_defined':
                                            $q->whereNotNull("value")->where("value", '!=', ''); // Check for non-null and non-empty string
                                            break;
                                        case 'is_not_defined':
                                            $q->where(function ($query) {
                                                $query->whereNull("value") // Check for NULL
                                                ->orWhere("value", '=', ''); // Check for empty string
                                            });
                                            break;
                                        case 'is_equal_to':
                                            $q->where('value', $filter_value);
                                            break;
                                        case 'is_not_equal_to':
                                            $q->where('value', '!=', $filter_value);
                                            break;
                                        case 'contain':
                                            $q->where('value', 'LIKE', '%' . $filter_value . '%');
                                            break;
                                        case 'does_not_contain':
                                            $q->where('value', 'NOT LIKE', '%' . $filter_value . '%');
                                            break;
                                    }
                                });
                            } elseif (in_array($filter['attribute'], array_keys($relation_attributes))) {
                                $relation_filter_attribute = $relation_attributes[$filter['attribute']];
                                $sub_relation = 'whereHas';
                                if (in_array($filter['formula'], ['is_not_defined', 'does_not_contain_any_of'])) {
                                    $query->whereDoesntHave($relation_filter_attribute);
                                    $sub_relation = 'orWhereHas';
                                } elseif (in_array($filter['formula'], ['is_defined', 'contain_any_of'])) {
                                    $query->has($relation_filter_attribute);
                                }
                                $query->{$sub_relation}($relation_filter_attribute, function (Builder $q) use ($filter, $relation_filter_attribute) {
                                    switch ($filter['formula']) {
                                        case 'is_defined':
                                            $q->whereNotNull("{$relation_filter_attribute}.id");
                                            break;

                                        case 'is_not_defined':
                                            $q->whereNull("{$relation_filter_attribute}.id");
                                            break;

                                        case 'contain_any_of':
                                            $q->whereIn("{$relation_filter_attribute}.id", $filter['value']);
                                            break;

                                        case 'does_not_contain_any_of':
                                            $q->whereNotIn("{$relation_filter_attribute}.id", $filter['value']);
                                            break;

                                        case 'is_equal_to':
                                            $q->where("{$relation_filter_attribute}.id", $filter['value']);
                                            break;

                                        case 'is_not_equal_to':
                                            $q->where("{$relation_filter_attribute}.id", '!=', $filter['value']);
                                            break;

                                        case 'contain':
                                            $q->where("{$relation_filter_attribute}.name", 'like', '%' . $filter['value'] . '%');
                                            break;

                                        case 'does_not_contain':
                                            $q->where("{$relation_filter_attribute}.name", 'not like', '%' . $filter['value'] . '%');
                                            break;

                                        default:
                                            // Handle unknown formula cases (optional)
                                            break;
                                    }
                                });
                            }
                        }
                    }
                });
                $main_relation = 'orWhere';
            }
        });
    }

}
