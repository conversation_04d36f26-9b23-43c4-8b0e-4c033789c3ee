<?php

namespace App\Jobs\Bulk;

use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use App\Models\Product\Product;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use App\Classes\Shopify\Delete;
use App\Models\Channel\Channel;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;

class BulkDeleteQueue implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    protected $data;

    protected $bulk_delete;

    protected $product_ids;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public array $request_data, $product_ids)
    {
        $this->data = $request_data;
        $this->product_ids = $product_ids;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $channels = Channel::query()
            ->where('organization_id', $this->data['required_data']['organization_id'])
            ->get();
        foreach ($this->product_ids  as $id) {
            $product = Product::where('id', $id)
                ->get();
            foreach ($channels as $channel) {
                if ($channel->is_product_delete == 1) {
                    (new Delete([
                        'product' => $product,
                        'channel_id' => $channel->id,
                        'organization_id' => $this->data['required_data']['organization_id'],
                    ]))->delete_shopify_product();
                }
            }
            $product->first()->delete();
        }
    }
}
