<?php

namespace App\Events;

use DrewM\MailChimp\Batch;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\BatchProgress;
use Illuminate\Support\Facades\Bus;

class BatchProgressEvent implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    private $progress;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct( public $batch_id)
    {
        $this->getBatchDetails();
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
       // info('channel_trigger');

        if (isset($this->progress)){
            return new Channel('channel-'.$this->progress->organization_id);
        }
    }

    public function broadcastWith()
    {
        $batch = Bus::findBatch($this->progress->batch_id);
        if ($batch->progress() >= 100){
            $this->progress->status = 1;
            $this->progress->save();
        }
        return [
            'batch' =>  $batch,
            'user_id' => $this->progress->user_id,
            'organization_id' => $this->progress->organization_id,
            'type' => $this->progress->type,
            'status' => $this->progress->status,
        ];
    }

    public function broadcastAs()
    {  // info('batch-progress');
        return 'batch-progress';
    }

    public function getBatchDetails()
    {
        $this->progress = BatchProgress::query()
            ->where('batch_id',$this->batch_id)
            ->first();
        return $this;
    }

}
