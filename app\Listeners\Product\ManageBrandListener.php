<?php

namespace App\Listeners\Product;

use App\Models\Product\Brand;
use Exception;
use Illuminate\Support\Facades\Log;

class ManageBrandListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     * @throws Exception
     */
    public function handle($event)
    {
        $brand_ids = [];
        if (!$event->data) { return; }

        foreach ($event->data as $data) {

            if (array_key_exists('id',$data)) {
                $brand_ids[] = $data['id'];
            } elseif(array_key_exists('name',$data)) {
                if (!empty($data['name'])) {
                    $brand_ids[] = Brand::findOrCreateForProduct($data['name'], $event->product)->id;
                }
            } else {
                Log::warning('Both "id", "name" keys and data are required.');
            }
        }

        if ($event->refresh) {
            $event->product->brands()->sync(array_filter($brand_ids));
        } else {
            $event->product->brands()->syncWithoutDetaching(array_filter($brand_ids));
        }
    }
}
