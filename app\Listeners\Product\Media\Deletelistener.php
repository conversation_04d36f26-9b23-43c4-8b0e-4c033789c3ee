<?php

namespace App\Listeners\Product\Media;

use Apimio\Gallery\Models\File;
use App\Classes\Shopify\Delete;
use App\Models\Channel\Channel;
use App\Models\Product\Product;
use Illuminate\Queue\InteractsWithQueue;
use App\Events\Product\Media\DeleteEvent;
use Illuminate\Contracts\Queue\ShouldQueue;

class Deletelistener implements ShouldQueue
{
    // 
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\Product\Media\DeleteEvent  $event
     * @return void
     */
    public function handle(DeleteEvent $event)
    {
        $product = Product::where('id', $event->product_id)
            ->get();
        if ($product) {
            $channels = Channel::query()
                ->where('organization_id', $product->first()->organization_id)
                ->get();
            foreach ($channels as $channel) {
                if ($channel->type == "shopify") {
                    (new Delete([
                        'product' => $product,
                        'channel_id' => $channel->id,
                        'image_id' => $event->image_id,
                        'organization_id' => $event->organization_id
                    ]))->delete_shopify_product_image();
                }
            }
        }
    }
}
