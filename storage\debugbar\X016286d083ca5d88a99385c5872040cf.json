{"__meta": {"id": "X016286d083ca5d88a99385c5872040cf", "datetime": "2025-05-27 07:49:45", "utime": **********.17494, "method": "POST", "uri": "/products/import/save-template", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748332184.494241, "end": **********.17498, "duration": 0.6807389259338379, "duration_str": "681ms", "measures": [{"label": "Booting", "start": 1748332184.494241, "relative_start": 0, "end": **********.06736, "relative_end": **********.06736, "duration": 0.5731189250946045, "duration_str": "573ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.067373, "relative_start": 0.5731320381164551, "end": **********.174983, "relative_end": 3.0994415283203125e-06, "duration": 0.10760998725891113, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 31489720, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST products/import/save-template", "middleware": "web, check_billing, auth, verified, activeOrganization", "controller": "App\\Http\\Controllers\\Product\\ImportController@save_template", "namespace": "App\\Http\\Controllers\\Product", "prefix": "/products", "where": [], "as": "import.save.template", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FImportController.php&line=471\" onclick=\"\">app/Http/Controllers/Product/ImportController.php:471-568</a>"}, "queries": {"nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.013640000000000001, "accumulated_duration_str": "13.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.1178489, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 29.692}, {"sql": "select * from `organizations` where `organizations`.`id` = '11' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 3) limit 1", "type": "query", "params": [], "bindings": ["11", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "check_billing", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\BillingMiddleware.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 86}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 86}], "start": **********.13895, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "check_billing:21", "source": {"index": 21, "namespace": "middleware", "name": "check_billing", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\BillingMiddleware.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FMiddleware%2FBillingMiddleware.php&line=21", "ajax": false, "filename": "BillingMiddleware.php", "line": "21"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 29.692, "width_percent": 26.393}, {"sql": "select * from `templates` where `organization_id` = '11' and (json_contains(`version_id`, '\\\"8\\\"')) and (json_contains(`channel_id`, '\\\"8\\\"')) and `name` = 'testing123' and `type` = 'import' and `export_type` is null and `id` not in ('22') and `organization_id` = '11'", "type": "query", "params": [], "bindings": ["11", "\"8\"", "\"8\"", "testing123", "import", "22", "11"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 515}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.146898, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "ImportController.php:515", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 515}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FImportController.php&line=515", "ajax": false, "filename": "ImportController.php", "line": "515"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 56.085, "width_percent": 8.504}, {"sql": "select * from `templates` where `templates`.`id` = '22' and `organization_id` = '11' limit 1", "type": "query", "params": [], "bindings": ["22", "11"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "packages/apimio/mapping-fields-package/src/models/Template.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\mapping-fields-package\\src\\models\\Template.php", "line": 90}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 532}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.1512542, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Template.php:90", "source": {"index": 19, "namespace": null, "name": "packages/apimio/mapping-fields-package/src/models/Template.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\mapping-fields-package\\src\\models\\Template.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fmapping-fields-package%2Fsrc%2Fmodels%2FTemplate.php&line=90", "ajax": false, "filename": "Template.php", "line": "90"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 64.589, "width_percent": 4.545}, {"sql": "update `templates` set `payload` = '{\\\"data\\\":[{\\\"from\\\":[\\\"Default,Product Name\\\"],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Default,handle\\\"],\\\"id\\\":\\\"fe16f7d9-05c5-421a-8a12-ecfeeba7d07b\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"split\\\",\\\"to\\\":[\\\"General,product_name\\\",\\\"Default,brand\\\"],\\\"id\\\":\\\"6705fb52-01b0-407f-955d-992099143c3f\\\",\\\"with\\\":\\\"32\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"cb32e4a7-5b2d-45cd-9d0f-93257e60c86d\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"Default,vendor\\\"],\\\"id\\\":\\\"85a3d130-d45a-412b-9210-0b5f732a5896\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"5be8b95a-e97e-4a3a-ac81-18c7f97c55c9\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"SEO,seo_keyword\\\"],\\\"id\\\":\\\"bf741402-0ad8-419e-afcc-6d020a92da69\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"693ed288-7891-46cc-8800-6e8c131481fd\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"cd0a5f3a-0273-49bc-968e-3e9c0b229ef9\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"c7bf20c8-4f47-446f-94d8-fa8666b95ba7\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"7f3351c0-d6bc-4183-aa2a-b79402001132\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"59f17836-b613-4dcc-aebc-d1a24512472c\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"6066071c-bd03-414e-9bf2-a17e19e1b3f1\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"6d8b6000-28fe-473e-a340-982ed136f69e\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"65ff55a6-3c9a-42fb-be0f-5e5a5f217180\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"100d4c68-49ed-4975-85c3-7cb616b34aec\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"9f367dfc-1f55-4ab7-bbc7-c6ddeae72148\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"59e8b96b-8d17-4c5e-b848-9c812646fd15\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"aaad8b73-a2c6-4745-b815-a18b63060d89\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"8d2e46c4-be66-4e20-98bd-a903e00f0e10\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"d4ed4b7e-23c6-43d8-b35a-02bb4bb8a42e\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"e9c6058e-d3ac-43c3-8d2d-074d62e2e257\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"03c015ba-f450-45e7-b0dc-932bdbf8fe45\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"6d34cf53-17e7-4b40-98a8-c179946ec769\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"7a9ad80a-f2f4-4d15-8374-62f7607eff9e\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"2258a13a-f9b1-4730-bc9f-61ea436d11c9\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"66679437-46fd-4382-a400-3368f59ea350\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"9c13d19d-fb94-4e20-a54e-05270aeaf283\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"8909e053-6392-4b52-8e7b-b952297a451f\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"SEO,seo_title\\\"],\\\"id\\\":\\\"a5a5f706-64dd-4c5a-89e9-c8f699a5b0b0\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[\\\"SEO,seo_description\\\"],\\\"id\\\":\\\"2ace1bb8-0be5-4890-af5d-dc205dfd3386\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"1dcd32fe-9a93-44b7-9120-1304a2ad1d05\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"a7ea99aa-29f7-4415-bda7-73d125fbc8fe\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"9d2e5a47-6752-4d0e-8d72-226f711141e2\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"c0a09c14-605b-4c71-a1bb-4b2a8852cf27\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"ae56d0c9-b5df-4fbc-8df6-44d97dcb9d2e\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"df3c757c-31da-415c-80ae-8bab629391c5\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"21981b1e-0913-4a3a-a24b-e73c68f4873c\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"0161d6d0-03b0-421a-b54a-c83346bcfc1d\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"76620d95-3ded-49aa-832d-2fdd09858a89\\\"},{\\\"from\\\":[],\\\"with_formula\\\":\\\"assign\\\",\\\"to\\\":[],\\\"id\\\":\\\"d1c6b48d-2a9d-426a-9fed-f459aa37719a\\\"}]}', `templates`.`updated_at` = '2025-05-27 07:49:45' where `id` = 22", "type": "query", "params": [], "bindings": ["{\"data\":[{\"from\":[\"Default,Product Name\"],\"with_formula\":\"assign\",\"to\":[\"Default,handle\"],\"id\":\"fe16f7d9-05c5-421a-8a12-ecfeeba7d07b\"},{\"from\":[],\"with_formula\":\"split\",\"to\":[\"General,product_name\",\"Default,brand\"],\"id\":\"6705fb52-01b0-407f-955d-992099143c3f\",\"with\":\"32\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"cb32e4a7-5b2d-45cd-9d0f-93257e60c86d\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[\"Default,vendor\"],\"id\":\"85a3d130-d45a-412b-9210-0b5f732a5896\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"5be8b95a-e97e-4a3a-ac81-18c7f97c55c9\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[\"SEO,seo_keyword\"],\"id\":\"bf741402-0ad8-419e-afcc-6d020a92da69\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"693ed288-7891-46cc-8800-6e8c131481fd\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"cd0a5f3a-0273-49bc-968e-3e9c0b229ef9\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"c7bf20c8-4f47-446f-94d8-fa8666b95ba7\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"7f3351c0-d6bc-4183-aa2a-b79402001132\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"59f17836-b613-4dcc-aebc-d1a24512472c\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"6066071c-bd03-414e-9bf2-a17e19e1b3f1\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"6d8b6000-28fe-473e-a340-982ed136f69e\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"65ff55a6-3c9a-42fb-be0f-5e5a5f217180\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"100d4c68-49ed-4975-85c3-7cb616b34aec\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"9f367dfc-1f55-4ab7-bbc7-c6ddeae72148\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"59e8b96b-8d17-4c5e-b848-9c812646fd15\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"aaad8b73-a2c6-4745-b815-a18b63060d89\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"8d2e46c4-be66-4e20-98bd-a903e00f0e10\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"d4ed4b7e-23c6-43d8-b35a-02bb4bb8a42e\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"e9c6058e-d3ac-43c3-8d2d-074d62e2e257\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"03c015ba-f450-45e7-b0dc-932bdbf8fe45\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"6d34cf53-17e7-4b40-98a8-c179946ec769\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"7a9ad80a-f2f4-4d15-8374-62f7607eff9e\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"2258a13a-f9b1-4730-bc9f-61ea436d11c9\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"66679437-46fd-4382-a400-3368f59ea350\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"9c13d19d-fb94-4e20-a54e-05270aeaf283\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"8909e053-6392-4b52-8e7b-b952297a451f\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[\"SEO,seo_title\"],\"id\":\"a5a5f706-64dd-4c5a-89e9-c8f699a5b0b0\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[\"SEO,seo_description\"],\"id\":\"2ace1bb8-0be5-4890-af5d-dc205dfd3386\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"1dcd32fe-9a93-44b7-9120-1304a2ad1d05\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"a7ea99aa-29f7-4415-bda7-73d125fbc8fe\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"9d2e5a47-6752-4d0e-8d72-226f711141e2\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"c0a09c14-605b-4c71-a1bb-4b2a8852cf27\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"ae56d0c9-b5df-4fbc-8df6-44d97dcb9d2e\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"df3c757c-31da-415c-80ae-8bab629391c5\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"21981b1e-0913-4a3a-a24b-e73c68f4873c\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"0161d6d0-03b0-421a-b54a-c83346bcfc1d\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"76620d95-3ded-49aa-832d-2fdd09858a89\"},{\"from\":[],\"with_formula\":\"assign\",\"to\":[],\"id\":\"d1c6b48d-2a9d-426a-9fed-f459aa37719a\"}]}", "2025-05-27 07:49:45", 22], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "packages/apimio/mapping-fields-package/src/models/Template.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\mapping-fields-package\\src\\models\\Template.php", "line": 108}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ImportController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ImportController.php", "line": 532}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.155934, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "Template.php:108", "source": {"index": 14, "namespace": null, "name": "packages/apimio/mapping-fields-package/src/models/Template.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\mapping-fields-package\\src\\models\\Template.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fmapping-fields-package%2Fsrc%2Fmodels%2FTemplate.php&line=108", "ajax": false, "filename": "Template.php", "line": "108"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 69.135, "width_percent": 30.865}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Organization\\Organization": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}, "Apimio\\MappingConnectorPackage\\models\\Template": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fmapping-fields-package%2Fsrc%2Fmodels%2FTemplate.php&line=1", "ajax": false, "filename": "Template.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/products/import/apply-template?template_id=22\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq", "organization_id": "11", "data": "array:11 [\n  \"input_array\" => array:2 [\n    \"array_name\" => \"CSV\"\n    \"nodes\" => array:1 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:4 [\n          \"Product Name\" => \"Product Name\"\n          \"SKU\" => \"SKU\"\n          \"Price\" => \"Price\"\n          \"Quantity\" => \"Quantity\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_input_array\" => array:1 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:4 [\n        0 => array:2 [\n          \"label\" => \"Product Name\"\n          \"value\" => \"Default,Product Name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"SKU\"\n          \"value\" => \"Default,SKU\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Price\"\n          \"value\" => \"Default,Price\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Quantity\"\n          \"value\" => \"Default,Quantity\"\n        ]\n      ]\n    ]\n  ]\n  \"file_path\" => \"mapping_fields/upload/json/1748332117_datafile.csv\"\n  \"file_url\" => \"https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1748332117_datafile.csv\"\n  \"data_required\" => array:9 [\n    \"template_method_type\" => \"import\"\n    \"output_type\" => \"Apimio\"\n    \"sync\" => false\n    \"redirect_url_route\" => \"products.import\"\n    \"organization_id\" => \"11\"\n    \"versions\" => array:1 [\n      8 => \"en-US\"\n    ]\n    \"catalogs\" => array:1 [\n      8 => \"Tanzayb Store\"\n    ]\n    \"import_action\" => \"3\"\n    \"selected_template\" => array:9 [\n      \"temp_id\" => 22\n      \"temp_name\" => \"testing123\"\n      \"created_at\" => Illuminate\\Support\\Carbon @1748331835 {#2354\n        #endOfTime: false\n        #startOfTime: false\n        #constructedObjectId: \"00000000000009320000000000000000\"\n        #localMonthsOverflow: null\n        #localYearsOverflow: null\n        #localStrictModeEnabled: null\n        #localHumanDiffOptions: null\n        #localToStringFormat: null\n        #localSerializer: null\n        #localMacros: null\n        #localGenericMacros: null\n        #localFormatFunction: null\n        #localTranslator: null\n        #dumpProperties: array:3 [\n          0 => \"date\"\n          1 => \"timezone_type\"\n          2 => \"timezone\"\n        ]\n        #dumpLocale: null\n        #dumpDateProperties: null\n        date: 2025-05-27 07:43:55.0 UTC (+00:00)\n      }\n      \"payload\" => array:1 [\n        \"data\" => array:40 [\n          0 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Handle\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => array:1 [\n              0 => \"Default,handle\"\n            ]\n            \"id\" => \"fe16f7d9-05c5-421a-8a12-ecfeeba7d07b\"\n          ]\n          1 => array:5 [\n            \"from\" => array:1 [\n              0 => \"Default,Title\"\n            ]\n            \"with_formula\" => \"split\"\n            \"to\" => array:2 [\n              0 => \"General,product_name\"\n              1 => \"Default,brand\"\n            ]\n            \"id\" => \"6705fb52-01b0-407f-955d-992099143c3f\"\n            \"with\" => \"32\"\n          ]\n          2 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Body (HTML)\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"cb32e4a7-5b2d-45cd-9d0f-93257e60c86d\"\n          ]\n          3 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Vendor\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => array:1 [\n              0 => \"Default,vendor\"\n            ]\n            \"id\" => \"85a3d130-d45a-412b-9210-0b5f732a5896\"\n          ]\n          4 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Type\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"5be8b95a-e97e-4a3a-ac81-18c7f97c55c9\"\n          ]\n          5 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Tags\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => array:1 [\n              0 => \"SEO,seo_keyword\"\n            ]\n            \"id\" => \"bf741402-0ad8-419e-afcc-6d020a92da69\"\n          ]\n          6 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Published\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"693ed288-7891-46cc-8800-6e8c131481fd\"\n          ]\n          7 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Option1 Name\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"cd0a5f3a-0273-49bc-968e-3e9c0b229ef9\"\n          ]\n          8 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Option1 Value\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"c7bf20c8-4f47-446f-94d8-fa8666b95ba7\"\n          ]\n          9 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Option2 Name\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"7f3351c0-d6bc-4183-aa2a-b79402001132\"\n          ]\n          10 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Option2 Value\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"59f17836-b613-4dcc-aebc-d1a24512472c\"\n          ]\n          11 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Option3 Name\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"6066071c-bd03-414e-9bf2-a17e19e1b3f1\"\n          ]\n          12 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Option3 Value\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"6d8b6000-28fe-473e-a340-982ed136f69e\"\n          ]\n          13 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant SKU\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"65ff55a6-3c9a-42fb-be0f-5e5a5f217180\"\n          ]\n          14 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Grams\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"100d4c68-49ed-4975-85c3-7cb616b34aec\"\n          ]\n          15 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Inventory Tracker\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"9f367dfc-1f55-4ab7-bbc7-c6ddeae72148\"\n          ]\n          16 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Inventory Qty\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"59e8b96b-8d17-4c5e-b848-9c812646fd15\"\n          ]\n          17 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Inventory Policy\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"aaad8b73-a2c6-4745-b815-a18b63060d89\"\n          ]\n          18 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Fulfillment Service\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"8d2e46c4-be66-4e20-98bd-a903e00f0e10\"\n          ]\n          19 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Price\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"d4ed4b7e-23c6-43d8-b35a-02bb4bb8a42e\"\n          ]\n          20 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Compare At Price\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"e9c6058e-d3ac-43c3-8d2d-074d62e2e257\"\n          ]\n          21 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Requires Shipping\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"03c015ba-f450-45e7-b0dc-932bdbf8fe45\"\n          ]\n          22 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Taxable\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"6d34cf53-17e7-4b40-98a8-c179946ec769\"\n          ]\n          23 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Variant Barcode\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"7a9ad80a-f2f4-4d15-8374-62f7607eff9e\"\n          ]\n          24 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Image Src\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"2258a13a-f9b1-4730-bc9f-61ea436d11c9\"\n          ]\n          25 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Image Position\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"66679437-46fd-4382-a400-3368f59ea350\"\n          ]\n          26 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Image Alt Text\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"9c13d19d-fb94-4e20-a54e-05270aeaf283\"\n          ]\n          27 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Gift Card\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"8909e053-6392-4b52-8e7b-b952297a451f\"\n          ]\n          28 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,SEO Title\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => array:1 [\n              0 => \"SEO,seo_title\"\n            ]\n            \"id\" => \"a5a5f706-64dd-4c5a-89e9-c8f699a5b0b0\"\n          ]\n          29 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,SEO Description\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => array:1 [\n              0 => \"SEO,seo_description\"\n            ]\n            \"id\" => \"2ace1bb8-0be5-4890-af5d-dc205dfd3386\"\n          ]\n          30 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / Google Product Category\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"1dcd32fe-9a93-44b7-9120-1304a2ad1d05\"\n          ]\n          31 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / Gender\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"a7ea99aa-29f7-4415-bda7-73d125fbc8fe\"\n          ]\n          32 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / Age Group\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"9d2e5a47-6752-4d0e-8d72-226f711141e2\"\n          ]\n          33 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / MPN\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"c0a09c14-605b-4c71-a1bb-4b2a8852cf27\"\n          ]\n          34 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / AdWords Grouping\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"ae56d0c9-b5df-4fbc-8df6-44d97dcb9d2e\"\n          ]\n          35 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / AdWords Labels\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"df3c757c-31da-415c-80ae-8bab629391c5\"\n          ]\n          36 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / Condition\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"21981b1e-0913-4a3a-a24b-e73c68f4873c\"\n          ]\n          37 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / Custom Product\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"0161d6d0-03b0-421a-b54a-c83346bcfc1d\"\n          ]\n          38 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / Custom Label 0\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"76620d95-3ded-49aa-832d-2fdd09858a89\"\n          ]\n          39 => array:4 [\n            \"from\" => array:1 [\n              0 => \"Default,Google Shopping / Custom Label 1\"\n            ]\n            \"with_formula\" => \"assign\"\n            \"to\" => []\n            \"id\" => \"d1c6b48d-2a9d-426a-9fed-f459aa37719a\"\n          ]\n        ]\n      ]\n      \"catalog\" => array:1 [\n        0 => \"8\"\n      ]\n      \"version\" => \"8\"\n      \"type\" => \"import\"\n      \"export_type\" => null\n      \"product_status\" => 1\n    ]\n  ]\n  \"import_action\" => 3\n  \"apimio_attributes_required\" => array:2 [\n    \"all_families\" => []\n    \"all_attributes\" => array:6 [\n      0 => array:2 [\n        \"id\" => 1\n        \"name\" => \"single line text\"\n      ]\n      1 => array:2 [\n        \"id\" => 2\n        \"name\" => \"number\"\n      ]\n      2 => array:2 [\n        \"id\" => 3\n        \"name\" => \"multi line text\"\n      ]\n      3 => array:2 [\n        \"id\" => 7\n        \"name\" => \"measurement\"\n      ]\n      4 => array:2 [\n        \"id\" => 9\n        \"name\" => \"json\"\n      ]\n      5 => array:2 [\n        \"id\" => 11\n        \"name\" => \"url\"\n      ]\n    ]\n  ]\n  \"template_attributes\" => []\n  \"output_array\" => array:2 [\n    \"array_name\" => \"Apimio\"\n    \"nodes\" => array:6 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:5 [\n          \"handle\" => \"Product Identifier\"\n          \"file\" => \"Product Images\"\n          \"vendor\" => \"Vendor\"\n          \"brand\" => \"Brand\"\n          \"categories\" => \"Category\"\n        ]\n      ]\n      1 => array:2 [\n        \"name\" => \"General\"\n        \"attributes\" => array:2 [\n          \"product_name\" => \"Product Name\"\n          \"description\" => \"Description\"\n        ]\n      ]\n      2 => array:2 [\n        \"name\" => \"Variant\"\n        \"attributes\" => array:11 [\n          \"sku\" => \"SKU\"\n          \"name\" => \"Name\"\n          \"file\" => \"Image\"\n          \"price\" => \"Price\"\n          \"compare_at_price\" => \"Compare at Price\"\n          \"cost_price\" => \"Cost Price\"\n          \"barcode\" => \"UPC / Barcode\"\n          \"weight\" => \"Weight\"\n          \"weight_unit\" => \"Weight Unit\"\n          \"track_quantity\" => \"Track Quantity\"\n          \"continue_selling\" => \"Continue Selling\"\n        ]\n      ]\n      3 => array:2 [\n        \"name\" => \"Variant Option\"\n        \"attributes\" => array:6 [\n          \"option1_name\" => \"Option 1 Name\"\n          \"option1_value\" => \"Option 1 Value\"\n          \"option2_name\" => \"Option 2 Name\"\n          \"option2_value\" => \"Option 2 Value\"\n          \"option3_name\" => \"Option 3 Name\"\n          \"option3_value\" => \"Option 3 Value\"\n        ]\n      ]\n      4 => array:2 [\n        \"name\" => \"Inventory -> Tanzayb Store\"\n        \"attributes\" => array:1 [\n          8 => \"Tanzayb Store Warehouse\"\n        ]\n      ]\n      5 => array:2 [\n        \"name\" => \"SEO\"\n        \"attributes\" => array:4 [\n          \"seo_url\" => \"URL Slug\"\n          \"seo_title\" => \"SEO Title\"\n          \"seo_description\" => \"SEO Description\"\n          \"seo_keyword\" => \"Tags\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_output_array\" => array:6 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:5 [\n        0 => array:2 [\n          \"label\" => \"Product Identifier\"\n          \"value\" => \"Default,handle\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Product Images\"\n          \"value\" => \"Default,file\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Vendor\"\n          \"value\" => \"Default,vendor\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Brand\"\n          \"value\" => \"Default,brand\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Category\"\n          \"value\" => \"Default,categories\"\n        ]\n      ]\n    ]\n    1 => array:3 [\n      \"label\" => \"General\"\n      \"title\" => \"General\"\n      \"options\" => array:2 [\n        0 => array:2 [\n          \"label\" => \"Product Name\"\n          \"value\" => \"General,product_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Description\"\n          \"value\" => \"General,description\"\n        ]\n      ]\n    ]\n    2 => array:3 [\n      \"label\" => \"Variant\"\n      \"title\" => \"Variant\"\n      \"options\" => array:11 [\n        0 => array:2 [\n          \"label\" => \"SKU\"\n          \"value\" => \"Variant,sku\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Name\"\n          \"value\" => \"Variant,name\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Image\"\n          \"value\" => \"Variant,file\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Price\"\n          \"value\" => \"Variant,price\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Compare at Price\"\n          \"value\" => \"Variant,compare_at_price\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Cost Price\"\n          \"value\" => \"Variant,cost_price\"\n        ]\n        6 => array:2 [\n          \"label\" => \"UPC / Barcode\"\n          \"value\" => \"Variant,barcode\"\n        ]\n        7 => array:2 [\n          \"label\" => \"Weight\"\n          \"value\" => \"Variant,weight\"\n        ]\n        8 => array:2 [\n          \"label\" => \"Weight Unit\"\n          \"value\" => \"Variant,weight_unit\"\n        ]\n        9 => array:2 [\n          \"label\" => \"Track Quantity\"\n          \"value\" => \"Variant,track_quantity\"\n        ]\n        10 => array:2 [\n          \"label\" => \"Continue Selling\"\n          \"value\" => \"Variant,continue_selling\"\n        ]\n      ]\n    ]\n    3 => array:3 [\n      \"label\" => \"Variant Option\"\n      \"title\" => \"Variant Option\"\n      \"options\" => array:6 [\n        0 => array:2 [\n          \"label\" => \"Option 1 Name\"\n          \"value\" => \"Variant Option,option1_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Option 1 Value\"\n          \"value\" => \"Variant Option,option1_value\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Option 2 Name\"\n          \"value\" => \"Variant Option,option2_name\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Option 2 Value\"\n          \"value\" => \"Variant Option,option2_value\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Option 3 Name\"\n          \"value\" => \"Variant Option,option3_name\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Option 3 Value\"\n          \"value\" => \"Variant Option,option3_value\"\n        ]\n      ]\n    ]\n    4 => array:3 [\n      \"label\" => \"Inventory -> Tanzayb Store\"\n      \"title\" => \"Inventory -> Tanzayb Store\"\n      \"options\" => array:1 [\n        0 => array:2 [\n          \"label\" => \"Tanzayb Store Warehouse\"\n          \"value\" => \"Inventory -> Tanzayb Store,8\"\n        ]\n      ]\n    ]\n    5 => array:3 [\n      \"label\" => \"SEO\"\n      \"title\" => \"SEO\"\n      \"options\" => array:4 [\n        0 => array:2 [\n          \"label\" => \"URL Slug\"\n          \"value\" => \"SEO,seo_url\"\n        ]\n        1 => array:2 [\n          \"label\" => \"SEO Title\"\n          \"value\" => \"SEO,seo_title\"\n        ]\n        2 => array:2 [\n          \"label\" => \"SEO Description\"\n          \"value\" => \"SEO,seo_description\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Tags\"\n          \"value\" => \"SEO,seo_keyword\"\n        ]\n      ]\n    ]\n  ]\n  \"mapping_data\" => array:40 [\n    0 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Handle\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Default,handle\"\n      ]\n      \"id\" => \"fe16f7d9-05c5-421a-8a12-ecfeeba7d07b\"\n    ]\n    1 => array:5 [\n      \"from\" => array:1 [\n        0 => \"Default,Title\"\n      ]\n      \"with_formula\" => \"split\"\n      \"to\" => array:2 [\n        0 => \"General,product_name\"\n        1 => \"Default,brand\"\n      ]\n      \"id\" => \"6705fb52-01b0-407f-955d-992099143c3f\"\n      \"with\" => \"32\"\n    ]\n    2 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Body (HTML)\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"cb32e4a7-5b2d-45cd-9d0f-93257e60c86d\"\n    ]\n    3 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Vendor\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Default,vendor\"\n      ]\n      \"id\" => \"85a3d130-d45a-412b-9210-0b5f732a5896\"\n    ]\n    4 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Type\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"5be8b95a-e97e-4a3a-ac81-18c7f97c55c9\"\n    ]\n    5 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Tags\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_keyword\"\n      ]\n      \"id\" => \"bf741402-0ad8-419e-afcc-6d020a92da69\"\n    ]\n    6 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Published\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"693ed288-7891-46cc-8800-6e8c131481fd\"\n    ]\n    7 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Option1 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"cd0a5f3a-0273-49bc-968e-3e9c0b229ef9\"\n    ]\n    8 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Option1 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"c7bf20c8-4f47-446f-94d8-fa8666b95ba7\"\n    ]\n    9 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Option2 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"7f3351c0-d6bc-4183-aa2a-b79402001132\"\n    ]\n    10 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Option2 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"59f17836-b613-4dcc-aebc-d1a24512472c\"\n    ]\n    11 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Option3 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"6066071c-bd03-414e-9bf2-a17e19e1b3f1\"\n    ]\n    12 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Option3 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"6d8b6000-28fe-473e-a340-982ed136f69e\"\n    ]\n    13 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant SKU\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"65ff55a6-3c9a-42fb-be0f-5e5a5f217180\"\n    ]\n    14 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Grams\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"100d4c68-49ed-4975-85c3-7cb616b34aec\"\n    ]\n    15 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Tracker\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"9f367dfc-1f55-4ab7-bbc7-c6ddeae72148\"\n    ]\n    16 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Qty\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"59e8b96b-8d17-4c5e-b848-9c812646fd15\"\n    ]\n    17 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Policy\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"aaad8b73-a2c6-4745-b815-a18b63060d89\"\n    ]\n    18 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Fulfillment Service\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"8d2e46c4-be66-4e20-98bd-a903e00f0e10\"\n    ]\n    19 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"d4ed4b7e-23c6-43d8-b35a-02bb4bb8a42e\"\n    ]\n    20 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Compare At Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"e9c6058e-d3ac-43c3-8d2d-074d62e2e257\"\n    ]\n    21 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Requires Shipping\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"03c015ba-f450-45e7-b0dc-932bdbf8fe45\"\n    ]\n    22 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Taxable\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"6d34cf53-17e7-4b40-98a8-c179946ec769\"\n    ]\n    23 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Barcode\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"7a9ad80a-f2f4-4d15-8374-62f7607eff9e\"\n    ]\n    24 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Src\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"2258a13a-f9b1-4730-bc9f-61ea436d11c9\"\n    ]\n    25 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Position\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"66679437-46fd-4382-a400-3368f59ea350\"\n    ]\n    26 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Alt Text\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"9c13d19d-fb94-4e20-a54e-05270aeaf283\"\n    ]\n    27 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Gift Card\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"8909e053-6392-4b52-8e7b-b952297a451f\"\n    ]\n    28 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,SEO Title\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_title\"\n      ]\n      \"id\" => \"a5a5f706-64dd-4c5a-89e9-c8f699a5b0b0\"\n    ]\n    29 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,SEO Description\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_description\"\n      ]\n      \"id\" => \"2ace1bb8-0be5-4890-af5d-dc205dfd3386\"\n    ]\n    30 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Google Product Category\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"1dcd32fe-9a93-44b7-9120-1304a2ad1d05\"\n    ]\n    31 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Gender\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"a7ea99aa-29f7-4415-bda7-73d125fbc8fe\"\n    ]\n    32 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Age Group\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"9d2e5a47-6752-4d0e-8d72-226f711141e2\"\n    ]\n    33 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / MPN\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"c0a09c14-605b-4c71-a1bb-4b2a8852cf27\"\n    ]\n    34 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / AdWords Grouping\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"ae56d0c9-b5df-4fbc-8df6-44d97dcb9d2e\"\n    ]\n    35 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / AdWords Labels\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"df3c757c-31da-415c-80ae-8bab629391c5\"\n    ]\n    36 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Condition\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"21981b1e-0913-4a3a-a24b-e73c68f4873c\"\n    ]\n    37 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Product\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"0161d6d0-03b0-421a-b54a-c83346bcfc1d\"\n    ]\n    38 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 0\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"76620d95-3ded-49aa-832d-2fdd09858a89\"\n    ]\n    39 => array:4 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 1\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n      \"id\" => \"d1c6b48d-2a9d-426a-9fed-f459aa37719a\"\n    ]\n  ]\n]"}, "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f0287e2-8bb8-4056-b5a8-cb645f3f7451\" target=\"_blank\">View in Telescope</a>", "path_info": "/products/import/save-template", "status_code": "<pre class=sf-dump id=sf-dump-1041670367 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1041670367\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1249199428 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1249199428\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1743274246 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n  \"<span class=sf-dump-key>template_method_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n  \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:40</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Product Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">fe16f7d9-05c5-421a-8a12-ecfeeba7d07b</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"5 characters\">split</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6705fb52-01b0-407f-955d-992099143c3f</span>\"\n        \"<span class=sf-dump-key>with</span>\" => \"<span class=sf-dump-str title=\"2 characters\">32</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">cb32e4a7-5b2d-45cd-9d0f-93257e60c86d</span>\"\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">85a3d130-d45a-412b-9210-0b5f732a5896</span>\"\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">5be8b95a-e97e-4a3a-ac81-18c7f97c55c9</span>\"\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">bf741402-0ad8-419e-afcc-6d020a92da69</span>\"\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">693ed288-7891-46cc-8800-6e8c131481fd</span>\"\n      </samp>]\n      <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">cd0a5f3a-0273-49bc-968e-3e9c0b229ef9</span>\"\n      </samp>]\n      <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">c7bf20c8-4f47-446f-94d8-fa8666b95ba7</span>\"\n      </samp>]\n      <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">7f3351c0-d6bc-4183-aa2a-b79402001132</span>\"\n      </samp>]\n      <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">59f17836-b613-4dcc-aebc-d1a24512472c</span>\"\n      </samp>]\n      <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6066071c-bd03-414e-9bf2-a17e19e1b3f1</span>\"\n      </samp>]\n      <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6d8b6000-28fe-473e-a340-982ed136f69e</span>\"\n      </samp>]\n      <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">65ff55a6-3c9a-42fb-be0f-5e5a5f217180</span>\"\n      </samp>]\n      <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">100d4c68-49ed-4975-85c3-7cb616b34aec</span>\"\n      </samp>]\n      <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f367dfc-1f55-4ab7-bbc7-c6ddeae72148</span>\"\n      </samp>]\n      <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">59e8b96b-8d17-4c5e-b848-9c812646fd15</span>\"\n      </samp>]\n      <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">aaad8b73-a2c6-4745-b815-a18b63060d89</span>\"\n      </samp>]\n      <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">8d2e46c4-be66-4e20-98bd-a903e00f0e10</span>\"\n      </samp>]\n      <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d4ed4b7e-23c6-43d8-b35a-02bb4bb8a42e</span>\"\n      </samp>]\n      <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">e9c6058e-d3ac-43c3-8d2d-074d62e2e257</span>\"\n      </samp>]\n      <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">03c015ba-f450-45e7-b0dc-932bdbf8fe45</span>\"\n      </samp>]\n      <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6d34cf53-17e7-4b40-98a8-c179946ec769</span>\"\n      </samp>]\n      <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">7a9ad80a-f2f4-4d15-8374-62f7607eff9e</span>\"\n      </samp>]\n      <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2258a13a-f9b1-4730-bc9f-61ea436d11c9</span>\"\n      </samp>]\n      <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">66679437-46fd-4382-a400-3368f59ea350</span>\"\n      </samp>]\n      <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9c13d19d-fb94-4e20-a54e-05270aeaf283</span>\"\n      </samp>]\n      <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">8909e053-6392-4b52-8e7b-b952297a451f</span>\"\n      </samp>]\n      <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">a5a5f706-64dd-4c5a-89e9-c8f699a5b0b0</span>\"\n      </samp>]\n      <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2ace1bb8-0be5-4890-af5d-dc205dfd3386</span>\"\n      </samp>]\n      <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">1dcd32fe-9a93-44b7-9120-1304a2ad1d05</span>\"\n      </samp>]\n      <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">a7ea99aa-29f7-4415-bda7-73d125fbc8fe</span>\"\n      </samp>]\n      <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9d2e5a47-6752-4d0e-8d72-226f711141e2</span>\"\n      </samp>]\n      <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">c0a09c14-605b-4c71-a1bb-4b2a8852cf27</span>\"\n      </samp>]\n      <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ae56d0c9-b5df-4fbc-8df6-44d97dcb9d2e</span>\"\n      </samp>]\n      <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">df3c757c-31da-415c-80ae-8bab629391c5</span>\"\n      </samp>]\n      <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">21981b1e-0913-4a3a-a24b-e73c68f4873c</span>\"\n      </samp>]\n      <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">0161d6d0-03b0-421a-b54a-c83346bcfc1d</span>\"\n      </samp>]\n      <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">76620d95-3ded-49aa-832d-2fdd09858a89</span>\"\n      </samp>]\n      <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => []\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d1c6b48d-2a9d-426a-9fed-f459aa37719a</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>version</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>catalog</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>temp_status</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>temp_name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">testing123</span>\"\n  \"<span class=sf-dump-key>temp_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">22</span>\"\n  \"<span class=sf-dump-key>export_type</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>file_path</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>ignore_unmapped</span>\" => \"<span class=sf-dump-str title=\"3 characters\">off</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1743274246\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1013586960 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3929</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkttU0QwTlF1OHMvblJMRHlnUkxZeXc9PSIsInZhbHVlIjoibmRqakhnTzdxS3g4cE12b1BUWFF3SzFGZWJRbnc0Z3ZHWkJxdXlUYWtDbEhUYlpYWUZmM242TW1vSjZjVzJuK2tTbU5BZzdla1FiS3ZKbnN4Q1ZSUm5ib0JKRFBNU1JwOFVPTmlBeTdmVnRUbjBZVXM4UnFLOEpIaXFWMVo3TGQiLCJtYWMiOiJkYzk5MTg2NzVjM2FlZjc1ZjFjMzhhZWVmYTU3ZjNjNjU1MmFjNjE2MTM3Y2RiYjlhNDQwZDIwM2ZhZmE5MDI1IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"67 characters\">http://localhost:8000/products/import/apply-template?template_id=22</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1317 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlBnUFZnSUIxd2RabTBsbnNiNVpmTUE9PSIsInZhbHVlIjoibEYxTENta2l3WE5hdnQ2UGVMeE9KU2lZdEE4YitHM1dsNlpJMDRpTUROUUtuc2FhU2l6Qnl4aklkYXZpK0l5WXhCT0g3Yi9UTE5XUXlpWUtUVFM4OXNkTG5BYnRtRlk2cUtuOUdKVC9NYmpQRmNSSWFCL20wSEVBekE0WVQ4L1dpZ21xVkE2bmJDblVGdWtZYmdhenprZ1J4NzFFZjFHWEs4NDhGM0l6ZkJ5RGJSRVAwVTFpSUFjNE5jOGdXY2JLcU02VWRjc2lwZWQxRzBHNlZSUFEydERmdW5CN3UvdGJwT2VkMDIwd08xQT0iLCJtYWMiOiJiYmM1ZTEzODhjYjA3MzUyMDE3Zjc0ZTI0YWE3N2RjNjUxYmJjOTgzOWZkYjJiNWI0ZTIzNWU0MjdiZjFlOTBhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkttU0QwTlF1OHMvblJMRHlnUkxZeXc9PSIsInZhbHVlIjoibmRqakhnTzdxS3g4cE12b1BUWFF3SzFGZWJRbnc0Z3ZHWkJxdXlUYWtDbEhUYlpYWUZmM242TW1vSjZjVzJuK2tTbU5BZzdla1FiS3ZKbnN4Q1ZSUm5ib0JKRFBNU1JwOFVPTmlBeTdmVnRUbjBZVXM4UnFLOEpIaXFWMVo3TGQiLCJtYWMiOiJkYzk5MTg2NzVjM2FlZjc1ZjFjMzhhZWVmYTU3ZjNjNjU1MmFjNjE2MTM3Y2RiYjlhNDQwZDIwM2ZhZmE5MDI1IiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6ImJZS2t6Q1hwbEs5NzBSaXFYRm1GY1E9PSIsInZhbHVlIjoiM256Q3Z2bWorM3kyMVdqOCtRZDV5TDZ4WGJIVVJjT21yUlFuNDBXTkdUbnI5aTZaQm9lN2NZQzM3ZnhXOFBjTm5FLzdQbWp0ajdRR2pPdjQ3dGZuN3BtVmNib25uQnl5dk1SSkZYNHlDVE5rNyt0R0dJZVIrWmF3S1V0dDdvMG8iLCJtYWMiOiJkNDgxZGZlYjRlMzI2MDhhYWU5NjlkMmRhMTYzMWZkNDliMWEwNTdkYTc0MDQ3NjNlMzY5MGI1NjFiMWY1MTE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1013586960\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">3|lyOnN9rFaNXD362RGbrGZMM746BZDyLiYeGFkfAHYF0FfGnJZdaE7RGsFJsg|$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k1WJWB6ZnSJcQ4dYcV3CZVMdsUzKOM46xMgpx7dI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2096243139 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 27 May 2025 07:49:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>vary</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">X-Inertia</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImdYVVZIUzNQTklKbVB1S1hZMmZzMXc9PSIsInZhbHVlIjoiVGFyNXJiWXdtWUtoNXNhZlN5UmdtUjkwYzU2eGN2a1ZvT2VFV081VzRNYUdqdjBNSjdFaFlucGNKNXlVTk1mUTFOQ0RDbXBmaGxTSnZxK3pZSjg3NDd6ZFYrbDgrZDNJR0FUaVlmUzdGNUFXUVl6WnMwQXRGVGt1cE4yazh6VW0iLCJtYWMiOiJmODY1OTM0N2U4YjY4OGZkY2E0Y2Y2NTRmNTA4YmJkYTExYTgzMzMyYzdlNDJiNzcxOGQ4NDdlY2M2ZDkyZWRlIiwidGFnIjoiIn0%3D; expires=Tue, 27 May 2025 09:49:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6IkMxYU9xV21ycFJlKy9aTWRwRVZQbVE9PSIsInZhbHVlIjoiZzJNSW1YMDhJaFV0YXhRQVczUWhDSjB4S0JuNEU4T2h1SDhNd0Qwbjk3aEZ4REYwN2tvTzBmbnNENWkyR3hIbmYxU0JQdkk0QjZlYUgvVFdNWjBjaEhXNXpIUlR2UkF0YzlwdzBzSkMzeHFzbis1ckNuMnQ1TUgvZGZabUVhbVciLCJtYWMiOiJmYTk0YTA1MjI4ODI0YmM3ZGIwOWNiMDU0Zjc5NWI4ZGFhYjA4OGVkM2U5MTY2MTQ1NzdkOTcwOTYyNjNlYWM1IiwidGFnIjoiIn0%3D; expires=Tue, 27 May 2025 09:49:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImdYVVZIUzNQTklKbVB1S1hZMmZzMXc9PSIsInZhbHVlIjoiVGFyNXJiWXdtWUtoNXNhZlN5UmdtUjkwYzU2eGN2a1ZvT2VFV081VzRNYUdqdjBNSjdFaFlucGNKNXlVTk1mUTFOQ0RDbXBmaGxTSnZxK3pZSjg3NDd6ZFYrbDgrZDNJR0FUaVlmUzdGNUFXUVl6WnMwQXRGVGt1cE4yazh6VW0iLCJtYWMiOiJmODY1OTM0N2U4YjY4OGZkY2E0Y2Y2NTRmNTA4YmJkYTExYTgzMzMyYzdlNDJiNzcxOGQ4NDdlY2M2ZDkyZWRlIiwidGFnIjoiIn0%3D; expires=Tue, 27-May-2025 09:49:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6IkMxYU9xV21ycFJlKy9aTWRwRVZQbVE9PSIsInZhbHVlIjoiZzJNSW1YMDhJaFV0YXhRQVczUWhDSjB4S0JuNEU4T2h1SDhNd0Qwbjk3aEZ4REYwN2tvTzBmbnNENWkyR3hIbmYxU0JQdkk0QjZlYUgvVFdNWjBjaEhXNXpIUlR2UkF0YzlwdzBzSkMzeHFzbis1ckNuMnQ1TUgvZGZabUVhbVciLCJtYWMiOiJmYTk0YTA1MjI4ODI0YmM3ZGIwOWNiMDU0Zjc5NWI4ZGFhYjA4OGVkM2U5MTY2MTQ1NzdkOTcwOTYyNjNlYWM1IiwidGFnIjoiIn0%3D; expires=Tue, 27-May-2025 09:49:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2096243139\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1150759918 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"67 characters\">http://localhost:8000/products/import/apply-template?template_id=22</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq</span>\"\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>input_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">CSV</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>Product Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>SKU</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>Price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>Quantity</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_input_array</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Product Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Default,SKU</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,Price</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Default,Quantity</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"50 characters\">mapping_fields/upload/json/1748332117_datafile.csv</span>\"\n    \"<span class=sf-dump-key>file_url</span>\" => \"<span class=sf-dump-str title=\"100 characters\">https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1748332117_datafile.csv</span>\"\n    \"<span class=sf-dump-key>data_required</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>template_method_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n      \"<span class=sf-dump-key>output_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>sync</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>redirect_url_route</span>\" => \"<span class=sf-dump-str title=\"15 characters\">products.import</span>\"\n      \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n      \"<span class=sf-dump-key>versions</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>8</span> => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>catalogs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>8</span> => \"<span class=sf-dump-str title=\"13 characters\">Tanzayb Store</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>selected_template</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>temp_id</span>\" => <span class=sf-dump-num>22</span>\n        \"<span class=sf-dump-key>temp_name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">testing123</span>\"\n        \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\Carbon @1748331835\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Carbon @1748331835</span> {<a class=sf-dump-ref>#2354</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000009320000000000000000</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n          <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Tuesday, May 27, 2025\n- 00:05:50.195718 from now\nDST Off\">2025-05-27 07:43:55.0 UTC (+00:00)</span>\n        </samp>}\n        \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:40</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Handle</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">fe16f7d9-05c5-421a-8a12-ecfeeba7d07b</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,Title</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"5 characters\">split</span>\"\n              \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6705fb52-01b0-407f-955d-992099143c3f</span>\"\n              \"<span class=sf-dump-key>with</span>\" => \"<span class=sf-dump-str title=\"2 characters\">32</span>\"\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Body (HTML)</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">cb32e4a7-5b2d-45cd-9d0f-93257e60c86d</span>\"\n            </samp>]\n            <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Vendor</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">85a3d130-d45a-412b-9210-0b5f732a5896</span>\"\n            </samp>]\n            <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Type</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">5be8b95a-e97e-4a3a-ac81-18c7f97c55c9</span>\"\n            </samp>]\n            <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Tags</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">bf741402-0ad8-419e-afcc-6d020a92da69</span>\"\n            </samp>]\n            <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Published</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">693ed288-7891-46cc-8800-6e8c131481fd</span>\"\n            </samp>]\n            <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option1 Name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">cd0a5f3a-0273-49bc-968e-3e9c0b229ef9</span>\"\n            </samp>]\n            <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option1 Value</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">c7bf20c8-4f47-446f-94d8-fa8666b95ba7</span>\"\n            </samp>]\n            <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option2 Name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">7f3351c0-d6bc-4183-aa2a-b79402001132</span>\"\n            </samp>]\n            <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option2 Value</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">59f17836-b613-4dcc-aebc-d1a24512472c</span>\"\n            </samp>]\n            <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option3 Name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6066071c-bd03-414e-9bf2-a17e19e1b3f1</span>\"\n            </samp>]\n            <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option3 Value</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6d8b6000-28fe-473e-a340-982ed136f69e</span>\"\n            </samp>]\n            <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Variant SKU</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">65ff55a6-3c9a-42fb-be0f-5e5a5f217180</span>\"\n            </samp>]\n            <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Grams</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">100d4c68-49ed-4975-85c3-7cb616b34aec</span>\"\n            </samp>]\n            <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Inventory Tracker</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f367dfc-1f55-4ab7-bbc7-c6ddeae72148</span>\"\n            </samp>]\n            <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Variant Inventory Qty</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">59e8b96b-8d17-4c5e-b848-9c812646fd15</span>\"\n            </samp>]\n            <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Inventory Policy</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">aaad8b73-a2c6-4745-b815-a18b63060d89</span>\"\n            </samp>]\n            <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Variant Fulfillment Service</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">8d2e46c4-be66-4e20-98bd-a903e00f0e10</span>\"\n            </samp>]\n            <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Price</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d4ed4b7e-23c6-43d8-b35a-02bb4bb8a42e</span>\"\n            </samp>]\n            <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Compare At Price</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">e9c6058e-d3ac-43c3-8d2d-074d62e2e257</span>\"\n            </samp>]\n            <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Requires Shipping</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">03c015ba-f450-45e7-b0dc-932bdbf8fe45</span>\"\n            </samp>]\n            <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Taxable</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6d34cf53-17e7-4b40-98a8-c179946ec769</span>\"\n            </samp>]\n            <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Barcode</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">7a9ad80a-f2f4-4d15-8374-62f7607eff9e</span>\"\n            </samp>]\n            <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Image Src</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2258a13a-f9b1-4730-bc9f-61ea436d11c9</span>\"\n            </samp>]\n            <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Position</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">66679437-46fd-4382-a400-3368f59ea350</span>\"\n            </samp>]\n            <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Alt Text</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9c13d19d-fb94-4e20-a54e-05270aeaf283</span>\"\n            </samp>]\n            <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Gift Card</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">8909e053-6392-4b52-8e7b-b952297a451f</span>\"\n            </samp>]\n            <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,SEO Title</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">a5a5f706-64dd-4c5a-89e9-c8f699a5b0b0</span>\"\n            </samp>]\n            <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,SEO Description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2ace1bb8-0be5-4890-af5d-dc205dfd3386</span>\"\n            </samp>]\n            <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">Default,Google Shopping / Google Product Category</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">1dcd32fe-9a93-44b7-9120-1304a2ad1d05</span>\"\n            </samp>]\n            <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Google Shopping / Gender</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">a7ea99aa-29f7-4415-bda7-73d125fbc8fe</span>\"\n            </samp>]\n            <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Age Group</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9d2e5a47-6752-4d0e-8d72-226f711141e2</span>\"\n            </samp>]\n            <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Google Shopping / MPN</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">c0a09c14-605b-4c71-a1bb-4b2a8852cf27</span>\"\n            </samp>]\n            <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">Default,Google Shopping / AdWords Grouping</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ae56d0c9-b5df-4fbc-8df6-44d97dcb9d2e</span>\"\n            </samp>]\n            <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / AdWords Labels</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">df3c757c-31da-415c-80ae-8bab629391c5</span>\"\n            </samp>]\n            <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Condition</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">21981b1e-0913-4a3a-a24b-e73c68f4873c</span>\"\n            </samp>]\n            <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Product</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">0161d6d0-03b0-421a-b54a-c83346bcfc1d</span>\"\n            </samp>]\n            <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 0</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">76620d95-3ded-49aa-832d-2fdd09858a89</span>\"\n            </samp>]\n            <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 1</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n              \"<span class=sf-dump-key>to</span>\" => []\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d1c6b48d-2a9d-426a-9fed-f459aa37719a</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>catalog</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>8</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>version</span>\" => \"<span class=sf-dump-str>8</span>\"\n        \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n        \"<span class=sf-dump-key>export_type</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>product_status</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>import_action</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>apimio_attributes_required</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>all_families</span>\" => []\n      \"<span class=sf-dump-key>all_attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">single line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">multi line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">measurement</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>template_attributes</span>\" => []\n    \"<span class=sf-dump-key>output_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>handle</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>categories</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>compare_at_price</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>cost_price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>barcode</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>weight_unit</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>track_quantity</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>continue_selling</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>option1_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>option1_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>option2_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>option2_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>option3_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>option3_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>8</span> => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>seo_url</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>seo_keyword</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_output_array</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,categories</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">General,description</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,name</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,compare_at_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Variant,cost_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant,barcode</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant,weight</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant,weight_unit</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Variant,track_quantity</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,continue_selling</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option1_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option1_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option2_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option2_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option3_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option3_value</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Inventory -&gt; Tanzayb Store,8</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">SEO,seo_url</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>mapping_data</span>\" => <span class=sf-dump-note>array:40</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Handle</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">fe16f7d9-05c5-421a-8a12-ecfeeba7d07b</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,Title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"5 characters\">split</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6705fb52-01b0-407f-955d-992099143c3f</span>\"\n        \"<span class=sf-dump-key>with</span>\" => \"<span class=sf-dump-str title=\"2 characters\">32</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Body (HTML)</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">cb32e4a7-5b2d-45cd-9d0f-93257e60c86d</span>\"\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Vendor</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">85a3d130-d45a-412b-9210-0b5f732a5896</span>\"\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Type</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">5be8b95a-e97e-4a3a-ac81-18c7f97c55c9</span>\"\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Tags</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">bf741402-0ad8-419e-afcc-6d020a92da69</span>\"\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Published</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">693ed288-7891-46cc-8800-6e8c131481fd</span>\"\n      </samp>]\n      <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option1 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">cd0a5f3a-0273-49bc-968e-3e9c0b229ef9</span>\"\n      </samp>]\n      <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option1 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">c7bf20c8-4f47-446f-94d8-fa8666b95ba7</span>\"\n      </samp>]\n      <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option2 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">7f3351c0-d6bc-4183-aa2a-b79402001132</span>\"\n      </samp>]\n      <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option2 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">59f17836-b613-4dcc-aebc-d1a24512472c</span>\"\n      </samp>]\n      <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option3 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6066071c-bd03-414e-9bf2-a17e19e1b3f1</span>\"\n      </samp>]\n      <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option3 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6d8b6000-28fe-473e-a340-982ed136f69e</span>\"\n      </samp>]\n      <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Variant SKU</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">65ff55a6-3c9a-42fb-be0f-5e5a5f217180</span>\"\n      </samp>]\n      <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Grams</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">100d4c68-49ed-4975-85c3-7cb616b34aec</span>\"\n      </samp>]\n      <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Inventory Tracker</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9f367dfc-1f55-4ab7-bbc7-c6ddeae72148</span>\"\n      </samp>]\n      <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Variant Inventory Qty</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">59e8b96b-8d17-4c5e-b848-9c812646fd15</span>\"\n      </samp>]\n      <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Inventory Policy</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">aaad8b73-a2c6-4745-b815-a18b63060d89</span>\"\n      </samp>]\n      <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Variant Fulfillment Service</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">8d2e46c4-be66-4e20-98bd-a903e00f0e10</span>\"\n      </samp>]\n      <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d4ed4b7e-23c6-43d8-b35a-02bb4bb8a42e</span>\"\n      </samp>]\n      <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Compare At Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">e9c6058e-d3ac-43c3-8d2d-074d62e2e257</span>\"\n      </samp>]\n      <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Requires Shipping</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">03c015ba-f450-45e7-b0dc-932bdbf8fe45</span>\"\n      </samp>]\n      <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Taxable</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6d34cf53-17e7-4b40-98a8-c179946ec769</span>\"\n      </samp>]\n      <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Barcode</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">7a9ad80a-f2f4-4d15-8374-62f7607eff9e</span>\"\n      </samp>]\n      <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Image Src</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2258a13a-f9b1-4730-bc9f-61ea436d11c9</span>\"\n      </samp>]\n      <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Position</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">66679437-46fd-4382-a400-3368f59ea350</span>\"\n      </samp>]\n      <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Alt Text</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9c13d19d-fb94-4e20-a54e-05270aeaf283</span>\"\n      </samp>]\n      <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Gift Card</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">8909e053-6392-4b52-8e7b-b952297a451f</span>\"\n      </samp>]\n      <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,SEO Title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">a5a5f706-64dd-4c5a-89e9-c8f699a5b0b0</span>\"\n      </samp>]\n      <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,SEO Description</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2ace1bb8-0be5-4890-af5d-dc205dfd3386</span>\"\n      </samp>]\n      <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">Default,Google Shopping / Google Product Category</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">1dcd32fe-9a93-44b7-9120-1304a2ad1d05</span>\"\n      </samp>]\n      <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Google Shopping / Gender</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">a7ea99aa-29f7-4415-bda7-73d125fbc8fe</span>\"\n      </samp>]\n      <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Age Group</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">9d2e5a47-6752-4d0e-8d72-226f711141e2</span>\"\n      </samp>]\n      <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Google Shopping / MPN</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">c0a09c14-605b-4c71-a1bb-4b2a8852cf27</span>\"\n      </samp>]\n      <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">Default,Google Shopping / AdWords Grouping</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ae56d0c9-b5df-4fbc-8df6-44d97dcb9d2e</span>\"\n      </samp>]\n      <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / AdWords Labels</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">df3c757c-31da-415c-80ae-8bab629391c5</span>\"\n      </samp>]\n      <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Condition</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">21981b1e-0913-4a3a-a24b-e73c68f4873c</span>\"\n      </samp>]\n      <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Product</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">0161d6d0-03b0-421a-b54a-c83346bcfc1d</span>\"\n      </samp>]\n      <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 0</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">76620d95-3ded-49aa-832d-2fdd09858a89</span>\"\n      </samp>]\n      <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 1</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d1c6b48d-2a9d-426a-9fed-f459aa37719a</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1150759918\", {\"maxDepth\":0})</script>\n"}}