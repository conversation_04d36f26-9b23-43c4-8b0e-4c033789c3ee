<?php
namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Organization\TeamInvite;
use App\Notifications\ApimioNotification;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Auth;

class ProcessEmailInvite implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $teamInvite;
    protected $user;

    /**
     * Create a new job instance.
     *
     * @param TeamInvite $teamInvite
     * @param User|null $user
     */
    public function __construct(TeamInvite $teamInvite, $user = null)
    {
        $this->teamInvite = $teamInvite;
        $this->user = $user; // Pass the user (can be null)
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $btn = url(route('register', [
            'email' => $this->teamInvite->email,
            'fname' => $this->teamInvite->first_name ?? '',
            'lname' => $this->teamInvite->last_name ?? ''
        ]));
        
        $details = [
            'subject' => "New Team Member invitation",
            'greeting' => 'Hi '  .( $this->teamInvite->email) ,
            'body' => 'You got an invitation as a team member. <br/><br/> <a href="' . $btn . '" style="color:white; padding: 5px 10px; background: #0D47A1; text-decoration: none;">Accept Invitation </a>',

            'thanks' => 'Thank you for using ' . env("APP_NAME", "Apimio"),
            'actionText' => 'Show Invitations',
            'actionURL' => url(route('register')),
        ];

        if (!isset($this->data["id"])) {
            $notifier = new ApimioNotification($details);
            $notifier->only_mail_notify(true);

            Notification::route('mail', $this->teamInvite->email)
                ->notify($notifier);
        }
        return;

    }
}
