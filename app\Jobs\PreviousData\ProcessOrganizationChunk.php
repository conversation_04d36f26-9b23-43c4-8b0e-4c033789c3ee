<?php

namespace App\Jobs\PreviousData;

use App\Classes\Shopify\CreateMetaFieldDefinition;
use App\Models\Channel\AttributeChannel;
use App\Models\Channel\Channel;
use App\Models\Product\Attribute;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessOrganizationChunk implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels , Batchable;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public array $organization_ids)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $channels = Channel::query()
            ->has('shopify_channels')
            ->withoutGlobalScopes()
            ->whereIn('organization_id', $this->organization_ids)
            ->get();

        foreach ($channels as $channel) {
            $attributes = Attribute::withoutGlobalScopes()
                ->with('families')
                ->where('organization_id', $channel->organization_id)
                ->whereHas('families', function ($q) {
                    $q->where('is_default', 0);
                })
                ->get();

            foreach ($attributes as $attribute) {
                $rules = json_decode($attribute->rules, true);

                if (!empty($rules)) {
                    $attr_channel = new AttributeChannel();
                    $attr_channel->channel_id = $channel->id;
                    $attr_channel->attribute_id = $attribute->id;
                    $attr_channel->store_connect_type = 'shopify';
                    $attr_channel->store_connect_id = $rules['shopify_id'] ?? null;
                    $attr_channel->save();
                }

                (new CreateMetaFieldDefinition($channel->id, $channel->organization_id, false))
                    ->setData($attribute)
                    ->create();
            }
        }
    }
}
