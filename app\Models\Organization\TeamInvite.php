<?php

namespace App\Models\Organization;

use App\Rules\OwnEmailCheck;
use App\User;
use App\Notifications\ApimioNotification;
use App\Rules\EmailUniqueForOrganization;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Validator;

class TeamInvite extends Model
{
    /**
     * @var mixed
     */
    private $data;

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'organization_id',
    ];

    protected $table = 'team_invites';
    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    public static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (!isset($model->organization_id)) {
                if (Auth::check()) {
                    $model->organization_id = Auth::user()->organization_id;
                }
            }
        });
        static::deleting(function ($model) {
            //get user
            $user = User::where('email', $model->email)->first();

            if ($user) {
                //get organization_user
                $organization_user = OrganizationUser::where([
                    'user_id' => $user->id,
                    'organization_id' => $model->organization_id,
                ])->first();

                //delete organization_user
                if ($organization_user) {
                    $organization_user->delete();
                }
            }
        });

        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            }
        });
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        if (isset($this->data['id'])) {
            $data = [
                'organization_id' => $this->data['organization_id'],
                'id' => $this->data['id'],
            ];
        } else {
            $data = [
                'organization_id' => $this->data['organization_id'],
            ];
        }
        return [
            'first_name' => 'nullable|max:35',
            'last_name' => 'nullable|max:35',
            'email' => ['required', 'max:255', new OwnEmailCheck($this->data['own_id']), new EmailUniqueForOrganization($data)],
            'permission_ids' => 'required|array',
            'permission_ids.*' => 'required',
        ];
    }

    /**
     * Get the custom message for request validations.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'permission_ids.required' => 'Permissions are required.',
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    public function validation(): \Illuminate\Contracts\Validation\Validator
    {
        return Validator::make($this->data, $this->rules(), $this->messages());
    }

    /**
     * @param $data
     * @return $this
     */
    public function setData($data): TeamInvite
    {
        $this->data = $data;
        return $this;
    }


    /**
     * @param $error_callback
     * @param $success_callback
     * @return mixed
     */
    public function store($error_callback, $success_callback)
    {
        $validation = $this->validation();
        if ($validation->fails()) {
            return $error_callback($validation->errors());
        }
        DB::beginTransaction();
        //  try{

        if (isset($this->data["id"])) {
            $obj = $this->find($this->data["id"]);
        } else {
            $obj = new TeamInvite();
        }

        $obj->first_name = $this->data["first_name"];
        $obj->last_name = $this->data["last_name"];
        $obj->email =  $this->data["email"];
        $obj->save();

        $permission = OrganizationUserPermission::query()
            ->where('team_invite_id', $obj->id)
            ->whereNotNull('organization_user_id')
            ->first();
        //saving permission_ids
        if ($permission) {
            $obj->permissions()->sync($this->data["permission_ids"], ['organization_user_id' => $permission->organization_user_id]);
        } else {
            $obj->permissions()->sync($this->data["permission_ids"]);
        }

        DB::Commit();
        $btn = route("register", ["email" => $obj->email, 'fname' => $obj->first_name, 'lname' => $obj->last_name]);
        $details = [
            'subject' => "New Team Member invitation",
            'greeting' => 'Hi ' . $this->data["first_name"],
            'body' => 'You got an invitation as a team member. <br/><br/> <a href="' . $btn . '" style="color:white; padding: 5px 10px; background: #0D47A1; text-decoration: none;">Accept Invitation </a>',

            'thanks' => 'Thank you for using ' . env("APP_NAME", "Apimio"),
            'actionText' => 'Show Invitations',
            'actionURL' => url(route('register')),
        ];

        if (!isset($this->data["id"])) {
            $notifier = new ApimioNotification($details);
            $notifier->only_mail_notify(true);

            Notification::route('mail', $this->data["email"])
                ->notify($notifier);
        }

        return $success_callback();
        //       }
        //       catch (Exception $e)
        //       {
        //           DB::rollback();
        //           return $error_callback(["main" => 'Something went wrong. Please try again later.']);
        //       }
    }

    /**
     * @param $callback
     * @return Builder
     */
    public function fetch($callback)
    {
        $invite_team = $this->with('permissions');

        if ($callback) {
            $invite_team = $callback($invite_team);
        }

        return $invite_team;
    }

    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'organization_user_permissions', 'team_invite_id', 'permission_id')->withPivot('organization_user_id');
    }
}
