<?php

namespace App\Http\Controllers\Product;

use Illuminate\Http\Request;
use App\Models\Product\Brand;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Session;

class BrandsController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified', 'activeOrganization']);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (Gate::denies('SubscriptionAccess', 'brand')) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        $brand = new Brand();
        if ($request->has('q')) {
            $brand->filter(["name" => $request->get('q')]);
        }
        $data['brands'] = $brand->fetch();

        return view('products.brands.view', compact('data', 'request'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('products.brands.add');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $brand = new Brand();
        return $brand->set_data($request->all())->store(function ($error) {
            return back()->withInput()->withErrors($error);
        }, function () {
            return redirect(route('brands.index'))->withSuccess('Brand created successfully.');
        });
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $brand = new Brand();
        $brand = $brand->find($id);
        return view('products.brands.add', compact('brand'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $brand = new Brand();
        return $brand->set_id($id)->set_data($request->all())->store(function ($errors) {
            return back()->withInput()->withErrors($errors);
        }, function () {
            return redirect(route('brands.index'))->withSuccess('success');
        });
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        if (isset($id)) {
            $brand = new Brand();
            $brand->delete_by_id($id);
            return redirect()->back();
        }
    }

}
