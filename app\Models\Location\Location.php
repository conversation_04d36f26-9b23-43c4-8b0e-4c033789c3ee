<?php

namespace App\Models\Location;

use App\Models\Channel\Channel;
use App\Models\Product\Inventory;
use Illuminate\Support\Facades\Auth;
use App\Models\Channel\ChannelLocation;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Location extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id', 'name', 'address', 'apartment', 'postal_code', 'city', 'phone_number', 'fulfill_online_orders', 'default_location'
    ];
    protected static function booted()
    {
        parent::booted();
        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where("organization_id", Auth::user()->organization_id);
            }
        });

        static::creating(function ($location) {
            if (!isset($location->organization_id)) {
                if (Auth::check()) {
                    $location->organization_id = Auth::user()->organization_id;
                }
            }
            // Check if this is the first location for the organization
            $isFirstLocation = static::where('organization_id', $location->organization_id)->count() == 0;
            // If it's not the first location and the 'default_location' field is not explicitly set, set it to false
            if (!$isFirstLocation && !$location->default_location) {
                $location->default_location = false;
            }
            // If it's the first location, set it as the default location
            if ($isFirstLocation) {
                $location->default_location = true;
            } elseif ($location->default_location) {
                // If a new location is set as default, unset default for all other locations
                static::where('organization_id', $location->organization_id)
                    ->where('id', '!=', $location->id) // exclude the current location
                    ->update(['default_location' => false]);
            }
        });
        // Add an updating event handler
        static::updating(function ($location) {
            // If the location is being set as default
            if ($location->isDirty('default_location') && $location->default_location) {
                // Unset default_location for other locations in the same organization
                static::where('organization_id', $location->organization_id)
                    ->where('id', '!=', $location->id) // exclude the current location
                    ->update(['default_location' => false]);
            }
        });
    }
    public static function CreateOrUpdate($data, $channel)
    {
        $location = static::where('organization_id', $channel->organization_id)
            ->where(function ($query) use ($data) {
                $query->where("id", $data["id"] ?? null)
                    ->orWhere("name", $data['name']);
            })
            ->whereHas('channels', function ($query) use ($channel) {
                $query->where('channels.id', $channel->id);
            })
            ->first();
        if (!$location) {
            $location = new static();
        }
        $location->name = $data['name'] ?? null;
        $location->address = $data['address'] ?? null;
        $location->apartment = $data['apartment'] ?? null;
        $location->postal_code = $data['postal_code'] ?? null;
        $location->city = $data['city'] ?? null;
        $location->phone_number = $data['phone_number'] ?? null;
        $location->fulfill_online_orders = $data['fulfill_online_orders'] ?? null;
        $location->default_location = $data['default_location'] ?? null;
        $location->organization_id = $channel->organization_id;
        $location->save();
        return $location;
    }

    //    Relationship
    public function channels()
    {
        return $this->belongsToMany(Channel::class);
    }

    public function inventory()
    {
        return $this->hasMany(Inventory::class);
    }

    public function channelLocation() // channel_location
    {
        return $this->belongsToMany(Channel::class, 'channel_location', 'location_id', 'channel_id')
                    ->withPivot('store_type', 'store_connect_id')
                    ->withTimestamps();
    }

}
