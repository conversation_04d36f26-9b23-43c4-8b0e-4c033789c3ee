<?php

namespace App\Classes\Shopify;

use App\Models\Channel\AttributeChannel;
use App\Models\Channel\ShopifyChannel;
use App\Models\Notification\ErrorLog;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Str;

class CreateMetaFieldDefinition
{
    protected mixed $attribute;

    public function __construct(public int $channel_id, public int $organization_id  , public $showErrorMsg = true , public $base_url = null ){
        $this->base_url = (new ShopifyChannel())->set_base_url($this->channel_id);
    }

    public function setData($attribute)
    {
        $this->attribute = $attribute;
        return $this;
    }

    public function create() : bool
    {

        $validation = $this->createValidation() ;

        $type = $this->getType();

        if($type != "") {
            foreach($this->attribute->families as $family){

                $family_name = Str::slug($family->name, '-');

                $query = <<<GQL
                            mutation {
                                metafieldDefinitionCreate(definition: {
                                    name: "{$this->attribute->name}",
                                    namespace: "{$family_name}",
                                    key: "{$this->attribute->handle}",
                                    description: "{$this->attribute->description}",
                                    type: "{$type}",
                                    validations: [$validation],
                                    ownerType: PRODUCT
                                }) {
                                    createdDefinition {
                                        id
                                        key
                                        namespace
                                        name
                                    }
                                    userErrors {
                                        field
                                        message
                                    }
                                }
                            }
                            GQL;

                $error_count = 0;
                $response = Http::withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ])
                ->post($this->base_url . "graphql.json", [
                    'query' => $query,
                ]);

                if($response->successful()){
                    Log::channel('shopify')->info('META-FIELD DEFINITION CREATE ON SHOPIFY SUCCESSFUL');
                    $json = $response->json();
                  //  Log::channel('shopify')->info($json);
                    // for saving id of meta-field definition
                    $this->saveMetafieldDefinitionId($json);

                    //for error sending
                    if($this->showErrorMsg) {
                       return $this->errorNotification($json, $error_count);
                    }

                }else{
                    $json = $response->json();
                    Log::channel('shopify')->info('METAFIELD DEFINITION CREATE ON SHOPIFY ERROR');
                    Log::channel('shopify')->error($json);
                }

            }


        }
        return false;
    }

    public function getmetafieldDefinitionsCount()
    {
            $totalCount = 0;
            $query = <<<GQL
            {
                metafieldDefinitions(first: 250, ownerType: PRODUCT) {
                    edges {
                        node {
                            id
                        }
                    },
                    pageInfo {
                        hasNextPage
                        endCursor
                    }
                }
            }
            GQL;
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($this->base_url . "graphql.json", [
                'query' => $query,
            ]);
        if($response->successful()){
            $data = $response->json();
            $totalCount = count($data["data"]['metafieldDefinitions']['edges']);
            return $totalCount;
        }else{
            $json = $response->json();
            Log::channel('shopify')->error($json);
            return null;
        }

    }

    /**
     * @param $response
     * @return void
     */
    public function saveMetafieldDefinitionId($response): void
    {

        $id = $response['data']['metafieldDefinitionCreate']['createdDefinition']['id'] ?? null;

        if($id){
            $id = str_replace('gid://shopify/MetafieldDefinition/', '', $id);
            $attributeChannel = AttributeChannel::query()->where([
                'channel_id'=>$this->channel_id,
                'attribute_id'=>$this->attribute->id,
                'store_connect_type'=>'shopify',
            ])->first();
            if(!$attributeChannel){
                $attributeChannel = new AttributeChannel();
                $attributeChannel->channel_id = $this->channel_id;
                $attributeChannel->attribute_id = $this->attribute->id;
                $attributeChannel->store_connect_type = 'shopify';
            }
            $attributeChannel->store_connect_id = $id;
            $attributeChannel->save();
        }
    }


    /**
     * @param $json
     * @param $error_count
     * @return bool
     */
    public function errorNotification($json, $error_count): bool
    {
        $limit_complete = false;
        if(isset($json["data"])){
            $error = "There are some errors in syncing attributes to shopify. <br><ul>";
            foreach ($json["data"]['metafieldDefinitionCreate']['userErrors'] as $error_row) {
                if(str_contains($error_row['message'], '250 definitions for each store resource')) {
                    $limit_complete = true;
                }
                $error .= "<li>{$error_row['message']}</li>" ;
                $error_count++;
            }

            $error .="</ul>";
            if($error_count > 0 ){
                $error_array = [
                    'organization_id' => $this->organization_id,
                    'description' => $error,
                    'type' => 'Shopify syncing',
                    'link' => route('attributes.edit',$this->attribute->id),
                    'link_text' => 'View Attribute',
                    'status' => 'error',
                ];

                $error = new ErrorLog();
                $error->setData($error_array)->store(function ($error) {
                    Log::channel('shopify')->info('Error in saving ErrorLogs.');
                    Log::channel('shopify')->error($error);
                }, function () {
                });
            }
        }

        return $limit_complete;
    }
    /**
     * @return string
     */
    public function getType(): string
    {
        $attr = $this->attribute->attribute_type_id;
        $rules = json_decode($this->attribute->rules, true);
        $type = $rules['type'] ?? null;
        $valueType = $rules['value_type'] ?? null;
        $listPrefix = $valueType == "list" ? 'list.' : '';

        $typeMap = [
            1 => 'single_line_text_field',
            2 => [
                'integer' => 'number_integer',
                'decimal' => 'number_decimal',
                'price' => 'money'
            ],
            3 => 'multi_line_text_field',
            4 => 'single_line_text_field',
            5 => [
                'date' => 'date',
                'date_and_time' => 'date_time'
            ],
            7 => [
                'dimension' => 'dimension',
                'volume' => 'volume',
                'weight' => 'weight'
            ],
            8 => 'rating',
            9 => 'json',
            10 => 'boolean',
            11 => 'url',
            12 => 'color',
        ];
        if(!isset($typeMap[$attr])){
            return "";
        }
        if (is_array($typeMap[$attr])) {
            return $listPrefix . $typeMap[$attr][$type];
        }

        return $listPrefix . $typeMap[$attr];
    }

    /**
     * @return string
     */
    public function createValidation(): string
    {

        $rules = json_decode($this->attribute->rules,true);
        $validations = array();
        if(isset($rules['type']) && $rules['type'] != "price" )
        {
            if($this->attribute->attribute_type_id == 8){
                $validations[] = [
                    'name'=>'scale_min',
                    'value'=>$rules['min']??0,
                ];
                $validations[] = [
                    'name'=>'scale_max',
                    'value'=>$rules['max']??100,
                ];
            }
            else{
                if(isset($rules['regex'])){
                    $validations[] = [
                        'name'=>'regex',
                        'value'=>$rules['regex'],
                    ];
                }

                if(isset($rules['max'])){
                    if($rules['type'] == "date_and_time"){
                        $rules['max'] = \Carbon\Carbon::parse($rules['max'])->format('Y-m-d\TH:i:s');
                    }
                    elseif($rules['type'] == "date"){
                        $rules['max'] = \Carbon\Carbon::parse($rules['max'])->format('Y-m-d');
                    }
                    elseif( in_array($rules['type'], ["dimension", "volume", "weight"])){

                        $rules["max_unit"] = $rules["max_unit"] ?? null;
                        $array = ['unit'=>$rules["max_unit"],'value'=>$rules["max"]];
                        $jsonString = json_encode($array, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
                        // Add backslashes before double quotes
                        $rules['max'] = addslashes($jsonString);

                    }
                    $validations[] = [
                        'name'=>'max',
                        'value'=>$rules['max'],
                    ];
                }

                if(isset($rules['min'])){
                    if($rules['type']=="date_and_time"){
                        $rules['min'] = \Carbon\Carbon::parse($rules['min'])->format('Y-m-d\TH:i:s');
                    }
                    elseif($rules['type'] == "date"){
                        $rules['min'] = \Carbon\Carbon::parse($rules['min'])->format('Y-m-d');
                    }
                    elseif(in_array($rules['type'], ["dimension", "volume", "weight"])){
                        $rules["min_unit"] = $rules["min_unit"] ?? null;
                        $array = ['unit'=>$rules["min_unit"],'value'=>$rules["min"]];
                        $jsonString = json_encode($array, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
                        $rules['min'] = addslashes($jsonString);
                    }
                    $validations[] = [
                        'name'=>'min',
                        'value'=> $rules['min'],
                    ];

                }

                if(isset($rules['precision'])){
                    $validations[] = [
                        'name'=>'max_precision',
                        'value'=>$rules['precision'],
                    ];
                }

                if(isset($rules['type'])&& $rules['type'] == "choices"){

                    $validations[] = [
                        'name'=>'choices',
                        'value'=>'['.implode(',',array_map(function($item){
                                return "\\\"{$item}\\\"";
                            },$this->attribute->attribute_options->pluck('name')->toArray())).']',
                    ];
                }
            }


            return implode(', ', array_map(function($item) {
                return "{name: \"{$item['name']}\", value: \"{$item['value']}\"}";
            }, $validations));

        }
        return "";



    }
}
