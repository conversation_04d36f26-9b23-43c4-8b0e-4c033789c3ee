<?php

namespace App\Models\Product;

use App\Models\Invite\Invite;
use App\Rules\UniqueManyToMany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Validator;

class Vendor extends Model
{
    private $data, $filter;

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    public static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if(!isset($model->organization_id)) {
                if (Auth::check()) {
                    $model->organization_id = Auth::user()->organization_id;
                }
            }

        });
        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            }
        });
    }

    public function fetch()
    {
        $vendor = $this;
        if ($this->filter) {
            if (isset($this->filter["name"]))
                $vendor = $vendor->where("name", "LIKE", "%" . $this->filter["name"] . "%");
        }
        return $vendor->orderBy("id", "DESC")->paginate(8);
    }

    public function filter($filter)
    {
        $this->filter = $filter;
        return $this;
    }

    public function set_data($data)
    {
        if (isset($data['_token']))
            unset($data['_token']);

        $this->data = $data;
        return $this;
    }

    public function set_id($id)
    {
        $this->id = $id;
        return $this->find($this->id);
    }

    public function rules()
    {
        $attributes = [];

        if(isset($this->data["organization_id"])) {
            $attributes["organization_id"] = $this->data["organization_id"];
        }

        if(isset($this->data["id"])) {
            $attributes["id"] = $this->data["id"];
        }

        return [
            'name' => ['required', 'max:255', new UniqueManyToMany(new Vendor(), $attributes)],
        ];
    }

    public function validation()
    {
        $validator = Validator::make($this->data, $this->rules());
        if (!$validator->fails()) {
            $this->attributes = $this->data;
        }
        return $validator;
    }

    public function store($error_callback, $success_callback)
    { 
        $validation = $this->validation();
        if ($validation->fails()) {
            return $error_callback($validation->errors());
        }
        
        if (isset($this->data['id'])) {
            $vendor = $this->find($this->data['id']);
        } else {
            $vendor = $this;
        }
       
        if (isset($this->data['organization_id']))
        {
            $vendor->organization_id = $this->data['organization_id'];
        }
        $vendor->name = $this->data['name'];
        $vendor->save();
        return $success_callback($vendor);
    }

    public function delete_by_id($id)
    {
        $this->set_id($id)->delete();
    }

    public function products()
    {
        return $this->belongsToMany(Product::class);
    }

    public function setData($data):Vendor
    {
        $this->data = $data;
        return $this;
    }
}
