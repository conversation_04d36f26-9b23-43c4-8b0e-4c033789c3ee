{"__meta": {"id": "Xac4c0d7beedfae4dcc6c299fc0145194", "datetime": "2025-05-27 07:18:08", "utime": **********.418577, "method": "GET", "uri": "/api/2024-12/shopify-sync-status", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[07:18:08] LOG.warning: Optional parameter $id declared before required parameter $version_id is implicitly treated as a required parameter in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Channel\\ShopifyChannel.php on line 716", "message_html": null, "is_string": false, "label": "warning", "time": **********.397144, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1748330287.820759, "end": **********.418618, "duration": 0.5978589057922363, "duration_str": "598ms", "measures": [{"label": "Booting", "start": 1748330287.820759, "relative_start": 0, "end": **********.326584, "relative_end": **********.326584, "duration": 0.5058250427246094, "duration_str": "506ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.326597, "relative_start": 0.5058379173278809, "end": **********.418621, "relative_end": 3.0994415283203125e-06, "duration": 0.09202408790588379, "duration_str": "92.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 30724048, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/shopify-sync-status", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=182\" onclick=\"\">app/Http/Controllers/Api/DashboardController.php:182-219</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.009179999999999999, "accumulated_duration_str": "9.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.370802, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 39.107}, {"sql": "select * from `channels` where `organization_id` = '11' and exists (select * from `shopify_channels` where `channels`.`id` = `shopify_channels`.`channel_id`)", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 186}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.399992, "duration": 0.0055899999999999995, "duration_str": "5.59ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:186", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 186}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=186", "ajax": false, "filename": "DashboardController.php", "line": "186"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 39.107, "width_percent": 60.893}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/shopify-sync-status\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq", "organization_id": "11"}, "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f027c94-488e-4fc3-9261-c6126a88c6c4\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/2024-12/shopify-sync-status", "status_code": "<pre class=sf-dump id=sf-dump-306404087 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-306404087\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1623108896 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1623108896\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1400013483 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1400013483\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InZleHBOTnJOUVRCb205R3dlVE1EeEE9PSIsInZhbHVlIjoiMXJuS3luUy8rNDBiV0JWM0s4N2NnNk40SU1ySGVkUWViblljQkRXaC9kb3NQaUdTMHhpaHh3aUV0QXhpR085cVJTL3UrRW8vOFUyR3JlbUgxaDNQOGd5RVF4Q1M1OEJ1elF3SFd3OENVN01TS2I3UnYxSjVmRlFKUFprcUM0TkYiLCJtYWMiOiJhN2FhZTdjZjk4N2ZhOWUzMWRiZTUzZjQ2ZjZlNTdjMDgzZjA3ZWUzOWQzN2QyNWIwODcyZjFhZDA4MDc3MzQ1IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1317 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlBnUFZnSUIxd2RabTBsbnNiNVpmTUE9PSIsInZhbHVlIjoibEYxTENta2l3WE5hdnQ2UGVMeE9KU2lZdEE4YitHM1dsNlpJMDRpTUROUUtuc2FhU2l6Qnl4aklkYXZpK0l5WXhCT0g3Yi9UTE5XUXlpWUtUVFM4OXNkTG5BYnRtRlk2cUtuOUdKVC9NYmpQRmNSSWFCL20wSEVBekE0WVQ4L1dpZ21xVkE2bmJDblVGdWtZYmdhenprZ1J4NzFFZjFHWEs4NDhGM0l6ZkJ5RGJSRVAwVTFpSUFjNE5jOGdXY2JLcU02VWRjc2lwZWQxRzBHNlZSUFEydERmdW5CN3UvdGJwT2VkMDIwd08xQT0iLCJtYWMiOiJiYmM1ZTEzODhjYjA3MzUyMDE3Zjc0ZTI0YWE3N2RjNjUxYmJjOTgzOWZkYjJiNWI0ZTIzNWU0MjdiZjFlOTBhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InZleHBOTnJOUVRCb205R3dlVE1EeEE9PSIsInZhbHVlIjoiMXJuS3luUy8rNDBiV0JWM0s4N2NnNk40SU1ySGVkUWViblljQkRXaC9kb3NQaUdTMHhpaHh3aUV0QXhpR085cVJTL3UrRW8vOFUyR3JlbUgxaDNQOGd5RVF4Q1M1OEJ1elF3SFd3OENVN01TS2I3UnYxSjVmRlFKUFprcUM0TkYiLCJtYWMiOiJhN2FhZTdjZjk4N2ZhOWUzMWRiZTUzZjQ2ZjZlNTdjMDgzZjA3ZWUzOWQzN2QyNWIwODcyZjFhZDA4MDc3MzQ1IiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IjdjWjY0cjVxMS9nSUNRbFJwS2Rrc1E9PSIsInZhbHVlIjoic3d5U3NRcURQNzJkWHNmbGtBeUIyQ1lUeXcyM1NQNWkzdG9yRElOeXlhSWRmWGxHMmpVVko2aWg4NXJyMGIxalVpUjNVbllybTNmdU1SaEJvM05yRGtnS0RHVFBLYzM5RWEvNkgySG9PYVhTLzZOcHZvRHMzM1B3TllYUm9MRzAiLCJtYWMiOiJiODZlZGRhZTg1YjYwYTkzNmI1NzMwNzY4NzY0MTRlZjVlZDI4YWEwOTkyNjc4NDljOThkMzg3ODE5MjI1ZWVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1311575878 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">3|lyOnN9rFaNXD362RGbrGZMM746BZDyLiYeGFkfAHYF0FfGnJZdaE7RGsFJsg|$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k1WJWB6ZnSJcQ4dYcV3CZVMdsUzKOM46xMgpx7dI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1311575878\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1301319288 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 27 May 2025 07:18:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">55</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkRNWUFrTzA5eEYybm45SHBsQ3MvakE9PSIsInZhbHVlIjoiWkZNZGtSa3pQMTJPU1RWb2hJWHREVFhUb2Z2NlBReFhoOVhZSVd6U0hXbWhJVjlyTE02dFROcDZUQWpEcmJzcjB1UWdrZXlPQjBUdWtWUy9kWldObHRFbm5MbDlQdkorZ2Z1Z3RNV1FrQi82Mlk4aFVlWHJDUUY1cXNNaFk5YXciLCJtYWMiOiI1NjhlNjAyY2E5MDVmODBmMmJkMzkzZjg4ZDUwMDFmZDcxZWQ4YmE4MGNiZjkyOWQ5ZWFlZjBhYzEyZTE0NTUzIiwidGFnIjoiIn0%3D; expires=Tue, 27 May 2025 09:18:08 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6Ik10VkpIVkF1K0pUQWxiUk40elhFS3c9PSIsInZhbHVlIjoiakd3UGFnTFFkTzNTQ1pJRW9RdkxCaUdINEFmRkVZb3R5TEphd0liQk10SjI4UjQ0QkNYNjYwc0syWFZzMzRucGhnVm5rSVA4STNzMnBJcG5JcnNGcjd6eHFpaEQyY1A2ajAvYTNxeHg2bW1jejIrYlhIei96TmVZMGhNZlJHeWsiLCJtYWMiOiIwNGVjM2RmOWViM2VlM2M5NGI1YjVjNDMzMDgzNzBkNTE1Y2FjM2Y1ZTM0NTlmNzdjOTIxYjVlZDFlY2QwZTczIiwidGFnIjoiIn0%3D; expires=Tue, 27 May 2025 09:18:08 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkRNWUFrTzA5eEYybm45SHBsQ3MvakE9PSIsInZhbHVlIjoiWkZNZGtSa3pQMTJPU1RWb2hJWHREVFhUb2Z2NlBReFhoOVhZSVd6U0hXbWhJVjlyTE02dFROcDZUQWpEcmJzcjB1UWdrZXlPQjBUdWtWUy9kWldObHRFbm5MbDlQdkorZ2Z1Z3RNV1FrQi82Mlk4aFVlWHJDUUY1cXNNaFk5YXciLCJtYWMiOiI1NjhlNjAyY2E5MDVmODBmMmJkMzkzZjg4ZDUwMDFmZDcxZWQ4YmE4MGNiZjkyOWQ5ZWFlZjBhYzEyZTE0NTUzIiwidGFnIjoiIn0%3D; expires=Tue, 27-May-2025 09:18:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6Ik10VkpIVkF1K0pUQWxiUk40elhFS3c9PSIsInZhbHVlIjoiakd3UGFnTFFkTzNTQ1pJRW9RdkxCaUdINEFmRkVZb3R5TEphd0liQk10SjI4UjQ0QkNYNjYwc0syWFZzMzRucGhnVm5rSVA4STNzMnBJcG5JcnNGcjd6eHFpaEQyY1A2ajAvYTNxeHg2bW1jejIrYlhIei96TmVZMGhNZlJHeWsiLCJtYWMiOiIwNGVjM2RmOWViM2VlM2M5NGI1YjVjNDMzMDgzNzBkNTE1Y2FjM2Y1ZTM0NTlmNzdjOTIxYjVlZDFlY2QwZTczIiwidGFnIjoiIn0%3D; expires=Tue, 27-May-2025 09:18:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1301319288\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1911021123 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aHDLhIGDYKcrV2X2uZRCbXmsjRfcqprJzAavMEOU</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost:8000/api/2024-12/shopify-sync-status</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$ar0S0xCNw/yE4VPwTr47neBbMN2miK2cIa7QSdnKw0rHn/TJ4k0Xq</span>\"\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1911021123\", {\"maxDepth\":0})</script>\n"}}