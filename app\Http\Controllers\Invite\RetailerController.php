<?php

namespace App\Http\Controllers\Invite;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Organization\InviteTeamController;
use App\Models\Channel\Channel;
use App\Models\Invite\Invite;
use App\Models\Organization\Organization;
use App\Models\Product\Vendor;
use App\Notifications\Invite\RetailerInvite;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Redirect;

class RetailerController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        $this->middleware(["activeOrganization"])->only("checkpoint");
    }
    public function index(Request $request) {

        $data["retailers"] = Invite::query()->retailer()->orderBy("type", "DESC")
            ->with(['organization_sender_without_scope.users_without_scope',"organization_sender.users"]);
        if ($request->filled('q') ) {
            $data["retailers"] = $data["retailers"]->where(function($q) use($request){
                $q->where("email", "LIKE", "%".$request->q ."%")
                    ->orWhereHas("organization_sender_without_scope.users_without_scope" ,
                        function ($query) use ($request){
                            $query->where("email", "LIKE", "%".$request->q ."%");}
                    );
            });
        }
        $data["retailers"] = $data["retailers"]->get();
        return view('invite.retailer.index')->with("data", $data);
    }

    public function create() {
        $channels = Channel::orderBy("id", "DESC")->get();
        return view("invite.retailer.create", ["channels" => $channels]);
    }

    public function store(Request $request) {
        $response = Gate::inspect('create',Vendor::class);
        if ($response->allowed()) {
            $invite = new Invite();
            return $invite->set_data($request->all())->set_user(Auth::user())->store(
            // error function
                function ($errors) {
                    return back()->withInput()->withErrors($errors);
                },
                // success function
                function ($obj) {
                    return Redirect::route("retailer.show", $obj->id)->withSuccess("Retailer added successfully.");
                }
            );
        }  else {
            return \redirect(route('dashboard'))->withErrors(['main' => $response->message()]);
        }
    }

    public function show($id)
    {
        $data["retailer"] = Invite::retailer()->findOrFail($id);
        if ($data["retailer"]->type == "vendor"){
            $data["products"] =[];
        }
        else{
            $data["products"] = $data["retailer"]->get_products();
        }
        $organization = Invite::query()
            ->withoutGlobalScopes()
            ->with('organization_sender_without_scope.users_without_scope')
            ->where("id",$id)
            ->get()->first();

        if( $organization->email != auth()->user()->email){//email invite was sent
            if($organization->get_status() == 4){ // sent status invite table data used
                $organization_user = $organization;
            }
            else{ // find user data from users table
                $organization_user =  User::where('email' ,$organization->email )->first();
            }
        }
        else{ //email invite is received
            if($organization->get_status() == 3){ //receive status sender data from relation
                $organization_user = $organization->organization_sender_without_scope->users_without_scope->first();
            }
            else{ //find relation data from user table
                $organization_user =  User::where('email' ,$organization->organization_sender_without_scope->users_without_scope->first()->email )->first();
            }
        }
        return view('invite.retailer.show')->with("data", $data)->with("organization_user", $organization_user);
    }

    public function edit($id)
    {
        try {
            $data["retailer"] = Invite::with('channels_without_scope')->find($id);
            $channels = Channel::orderBy("id", "DESC")->get();
            $data["products"] = $data["retailer"]->get_products();
            return view('invite.retailer.create', ["channels" => $channels])->with("data", $data);
        }
        catch (Exception $e) {
            log:error($e);
            return Redirect::route("vendor.index")->withErrors(['main' => "Something went wrong."]);
        }
    }

    //Sends Data to Vendor invites and save new data
    public function update(Request $request)
    {
        return (new Invite())->set_data($request->all())->set_user(auth::user())
            ->store(
                function ($errors) {
                    return back()->withErrors($errors)->withInput();
                },
                function () {
                    return redirect()->route('retailer.index')->withSuccess('retailer updated successfully.');
                });
    }

    public function accept($id) {
        $invite = Invite::withoutGlobalScopes()->findOrFail($id);
        $invite->is_accepted = 1;
        $invite->is_declined = 0;
        $invite->organization_id_receiver = Auth::user()->organization_id;
        $invite->save();
        return Redirect::back()->withSuccess("Success");
    }

    public function decline($id) {
        $invite = Invite::withoutGlobalScopes()->findOrFail($id);
        $invite->is_accepted = 0;
        $invite->is_declined = 1;
        $invite->save();
        return Redirect::back()->withSuccess("Success");
    }
    public function disconnect($id) {
        $invite = Invite::withoutGlobalScopes()->findOrFail($id);
        $invite->is_accepted = 1;
        $invite->is_declined = 1;
        $invite->save();
        return Redirect::back()->withSuccess("Success");
    }
    public function resend_email($id) {
        $invite = Invite::withoutGlobalScopes()->findOrFail($id);
        try{
            $invite = Invite::withoutGlobalScopes()->findOrFail($id);
            $invite->is_accepted = 0;
            $invite->is_declined = 0;
            $invite->save();
            Notification::route("mail", $invite->email)->notify(new RetailerInvite(auth()->user(), $invite));
            return Redirect::back()->withSuccess("Email sent successfully");
        }
        catch (Exception $e) {
            log:error($e);
            return Redirect::back()->withErrors("Something went wrong");
        }
    }

}
