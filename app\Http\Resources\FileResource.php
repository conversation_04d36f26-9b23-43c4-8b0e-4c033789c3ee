<?php

namespace App\Http\Resources;

use Apimio\Gallery\Models\File;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray($request)
    {
        if ($this->resource instanceof File && $this->resource->simple) {
            return [
                'id' => $this->id,
                'link' => $this->link,
            ];
        }

        return [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'link' => $this->link,
            'width' => $this->width,
            'height' => $this->height,
            'size' => $this->size,
            'ext' => $this->ext,
            'type' => $this->type,
            'should_sync' => $this->should_sync,
            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at->toDateTimeString(),
        ];
    }
}
