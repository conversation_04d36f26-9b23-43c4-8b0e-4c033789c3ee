<?php

namespace App\Events\Product;

use App\Models\Product\Product;
use App\Models\Product\Version;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CalculateScore
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Product $model;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($model , public array $version_ids = [])
    {
        $this->model = $model;
    }
}
