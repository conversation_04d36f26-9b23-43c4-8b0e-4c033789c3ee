<?php

namespace App\Http\Controllers\Product;

use Illuminate\Http\Request;
use App\Models\Product\Family;
use App\Models\Product\Attribute;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Gate;
use App\Models\Product\ProductVersion;
use App\Models\Product\AttributeFamily;
use App\Models\Organization\Organization;
use App\Models\Product\AttributeFamilyProductVersion;


class FamilyController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified', "activeOrganization"]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if (Gate::denies('SubscriptionAccess', 'attribute-set')) {
            return redirect(route('billing'))->withErrors(['main' => "upgrade your plan to access all modules"]);
        }
        $family = new Family();
        if ($request->has('q')) {
            $family->filter(["name" => $request->get('q')]);
        }
        $data['family'] = $family->fetch();
        return view("products.family.view", compact('data', 'request'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {

        return view('products.family.add', ["attributes" => Attribute::where("is_default", 0)->where("attribute_type_id", "!=", 13)->get()]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $family = new Family();
        return $family->set_data($request->all())->store(
        // when error
            function ($error) {
                return back()->withInput()->withErrors($error);
            },
            // when success
            function () {
                return redirect(route("family.index"))->withSuccess("Attribute set created successfully.");
            });
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $family = Family::with("attributes")->findOrFail($id);
        if (!$family->is_default) {
            if ($family->attributes->toArray()) {
                $family->attributes = array_column($family->attributes->toArray(), "id");
            }
            return view('products.family.add', [
                "attributes" => Attribute::where("is_default", 0)->where('attribute_type_id', '!=', 13)->get(),
                "family" => $family
            ]);
        } else {
            return back()->withErrors(['main' => 'Default attribute set cannot be edited.']);
        }

    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $family = new Family();
        return $family->set_data($request->all())->store(
        // when error
            function ($error) {
                return back()->withInput()->withErrors($error);
            },
            // when success
            function () {
                return redirect(route("family.index"))->withSuccess("Attribute set updated successfully.");
            });
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $fam = Family::findOrFail($id);
        if ($fam->is_default) {
            return back()->withErrors(['main' => 'Default attribute set cannot be deleted']);
        }
        $fam->delete();
        return back()->withSuccess('Deleted successfully.');
    }

    /**unassign attribute set from product
     * @param int $id
     * @param int $version_id
     */
    public function unassign_family($id, $version_id, $product_id)
    {
        $attribute_family_id = AttributeFamily::where('family_id', $id)->pluck('id')->toArray();
        $product_version_id = ProductVersion::where('version_id', $version_id)->where('product_id', $product_id)->value('id');
        AttributeFamilyProductVersion::whereIn('attribute_family_id', $attribute_family_id)->where('product_version_id', $product_version_id)->delete();
        return redirect(route('products.attribute_set',['id'=>$product_id, 'version_id'=>$version_id]))->withSuccess('Attribute set unassigned successfully');
    }
}
