<?php

namespace App\Http\Controllers\Api\Brand;

use App\Http\Controllers\Controller;
use App\Http\Requests\Brand\BrandRequest;
use App\Http\Resources\BrandResource;
use Illuminate\Http\Request;
use App\Models\Product\Brand;

class BrandController extends Controller
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware("auth:sanctum");
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();

            if(get_class($user) != 'App\Models\Organization\Organization'){
                $this->check_user_organization($user, $request);
            }


            // Validate the request parameters
            $validatedData = $request->validate([
                'id' => 'integer|exists:brands,id',
                'name' => 'string|max:255',
                'is_default' => 'boolean',
                'paginate' => 'integer|min:1|max:255',
            ]);

            // Query to get brands without global scopes

            $brands = $user->brands();

            // Apply filters if provided
            if ($request->filled("id")) {
                $brands->where("id", $request->get("id"));
            }

            if ($request->filled("name")) {
                $brands->where("name", "LIKE", "%" . $request->get("name") . "%");
            }

            if ($request->filled("is_default")) {
                $brands->where("is_default", $request->get("is_default"));
            }

            // Determine the number of items per page
            $paginate = $request->filled("paginate") ? $request->get("paginate") : 255;

            // Get the paginated data
            $paginatedBrands = $brands->paginate($paginate);

            // Prepare pagination details
            $pagination = [
                'current_page' => $paginatedBrands->currentPage(),
                'last_page' => $paginatedBrands->lastPage(),
                'per_page' => $paginatedBrands->perPage(),
                'total' => $paginatedBrands->total(),
            ];

            return response([
                'message' => 'Brands retrieved successfully',
                'brands' => BrandResource::collection($paginatedBrands),
                'pagination' => $pagination
            ]);
        } catch (\Exception $e) {
            return response([
                'message' => 'Failed to retrieve brands',
                'error' => $e->getMessage()
            ], $e->getCode() != 0 ? $e->getCode() : 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(BrandRequest $request)
    {
        $brand =  $request->user()->brands()->create($request->validated());
            return response()->json([
                'message' => 'Brand created successfully.',
                'brand' => $brand
            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, string $id)
    {
        $brand = $request->user()->brands()->find($id);
        return response([
            'message' => 'Brand retrieved successfully',
            'brand' => $brand
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(BrandRequest $request, string $id)
    {
        $brand = $request->user()->brands()->findOrFail($id);
        $brand->update($request->validated());
        return response([
            'message' => 'Brand updated successfully',
            'brand' => $brand
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, string $id)
    {
        $request->user()->brands()->findOrFail($id)->delete();
        return response([
            'message' => 'Brand deleted successfully'
        ]);
    }
}
