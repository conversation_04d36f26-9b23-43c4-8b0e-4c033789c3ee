<?php

namespace App\Models\Organization;

use App\User;
use Apimio\Gallery\Models\Folder;
use App\Models\Billing\ShopifySubscription;
use App\Models\BrandsPortal;
use App\Models\Channel\Channel;
use App\Models\Channel\ChannelLocation;
use App\Models\Channel\ChannelVersion;
use App\Models\Invite\Invite;
use App\Models\Location\Location;
use App\Models\Product\Attribute;
use App\Models\Product\AttributeFamily;
use App\Models\Product\Category;
use App\Models\Organization\TeamInvite;
use App\Models\Product\Family;
use App\Models\Product\ProductShopifyMapping;
use App\Models\Product\Version;
use App\Models\Product\Brand;
use App\Models\Product\Product;
use App\Models\Setting;
use App\Notifications\CreateUserNotification;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Notification;
use Illuminate\Validation\Rule;
use App\Rules\UniqueManyToMany;
use App\Traits\AWSSetting;
use App\Traits\Billing\BillingTrait;
use App\Traits\Billing\LimitTrait;
use App\Traits\Organization\CountTrait;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Laravel\Cashier\Billable;
use Laravel\Cashier\Subscription as Cs;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Notifications\Notifiable;


class Organization extends Authenticatable
{
    private
        $data,
        $user;

    use HasApiTokens, Notifiable, LimitTrait, BillingTrait, Billable, CountTrait, AWSSetting;

    protected $fillable = [
        'shop_id',
        'name',
        'region',
        'units',
        'currency',
        'block_status',
        'plan_handle',
        'stripe_id',
        'pm_type',
        'pm_last_four',
        'card_brand',
        'trial_ends_at'
    ];
    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'trial_ends_at' => 'datetime',
    ];

    protected $casts = [
        'trial_ends_at' => 'datetime:Y-m-d',  // formats the date when accessing $model->trial_ends_at
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        parent::booted();
        static::addGlobalScope('user_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->whereHas("users", function ($query) {
                    $query->where("user_id", Auth::id());
                });
            }
        });

        static::created(function ($organization) {
            $organization->set_active_organization_by_id($organization->id);
            if (isset($organization->id)) {
                $organization->seed_default_account_data($organization);
            }
        
        });
    }

    /**
     * Constructor for storing data and applying post logic
     * to organization structure.
     *
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        if (isset($attributes["data"])) {
            if ($attributes["data"]["_token"])
                unset($attributes["data"]["_token"]);

            $this->data = $attributes["data"];

            $this->data["users_id"] = Auth::id();
        }

        parent::__construct($attributes);
    }

    public function set_active_organization_automatically()
    {
        $organization = $this->get()->first();
        $this->set_active_organization_by_id($organization->id);
    }

    public function set_active_organization_by_id($organization_id)
    {
        session(
            [
                'organization_id' => $organization_id,
            ]
        );
    }

    /**
     * Set data attribute.
     *
     *
     * @param array
     * @return Organization
     */
    public function set_data(array $data)
    {
        $this->data = $data;
        return $this;
    }

    public function set_id($id)
    {
        $this->data["id"] = $id;
        return $this;
    }

    public function set_user($user)
    {
        $this->user = $user;
        return $this;
    }

    /**
     * Get data attribute.
     *
     * @return string
     */
    public function get_data()
    {
        return $this->data;
    }



    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $attributes = [];
        if (isset($this->data["user_id"])) {
            $attributes["user_id"] = $this->data["user_id"];
        }

        if (isset($this->data["id"])) {
            $attributes["id"] = $this->data["id"];
        }

        return [
            'name' => ['required', 'max:255', new UniqueManyToMany(new Organization(), $attributes)],
            'region' => 'nullable|max:5',
            'units' => 'nullable|max:20',
            'currency' => 'nullable|max:3',
            //'language' => 'required'
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    public function validation()
    {
        $validator = Validator::make($this->data, $this->rules());
        return $validator;
    }

    public function store($error_callback, $success_callback)
    {

        $validation = $this->validation();
        if ($validation->fails()) {
            return $error_callback($validation->errors());
        }
        DB::beginTransaction();
        try {
            if (isset($this->data["id"])) {
                $obj = $this->find($this->data["id"]);
            } else {
                $obj = $this;
            }
            $obj->shop_id = isset($this->data["shop_id"]) ? $this->data["shop_id"] : null;;
            $obj->name = $this->data["name"];
            $obj->region = isset($this->data["region"]) ? $this->data["region"] : null;
            $obj->units = isset($this->data["units"]) ? $this->data["units"] : null;
            $obj->currency =/* isset($this->data["currency"]) ? $this->data["currency"] : */ null;

            if (isset($this->data["block_status"]))
                $obj->block_status = true;
            $obj->save();

            //checking if user invited
            $invited = new Invite();
            if(isset($this->user)&& isset($this->user->email))
            {   $is_invited = $invited->IsInvited($this->user->email);
            if ((!isset($obj->stripe_id) && !$is_invited) && (!$this->user->shopify_shop_id)) {
                $obj->createAsStripeCustomer(['name' => $obj->name, 'email' =>  $this->user->email]);
                $obj->trial_ends_at = now()->addDays(14);
                $obj->save();
            }}

            if (isset($this->data["trail_user_days"]) || isset($this->user->shopify_shop_id)) {
                $obj->trial_ends_at = now()->addDays(14);
                $obj->save();
            }

            if (isset($this->user)) {
                $user = User::findOrFail($this->user->id);

                if (isset($this->data['phone'])) {
                    $user->phone = $this->data['phone'];
                    $user->save();
                }
                if (isset($this->data['ip'])) {
                    $user->ip = $this->data['ip'];
                    $user->save();
                }
            }

            if (!isset($this->data["id"])) {
                if (isset($this->user)) {
                    $obj->users()->sync($this->user);
                }
                $organization_user_id = OrganizationUser::where('user_id', $this->user->id)
                    ->where('organization_id', $obj->id)->value('id');

                //get permissions
                $permissions = Permission::all();

                //give all permissions of new created organization.
                foreach ($permissions as $permission) {
                    $per = new OrganizationUserPermission();
                    $per->organization_user_id = $organization_user_id;
                    $per->permission_id = $permission->id;
                    $per->save();
                }
            }

            if (env('CUSTOM_QUEUE_WORKER') != 'local') {
                $this->createFifoSQS($obj->id);
            }

            DB::Commit();

            return $success_callback($this);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error($e);
            return $error_callback(["main" => 'Something went wrong. Please try again later.']);
        }
    }

    public function get_invite_user($email)
    {
        return Invite::query()
            ->withoutGlobalScopes()
            ->with('organization_sender_without_scope')
            ->where("email", $email)
            ->where("is_accepted", "0")
            ->where("is_declined", "0")
            ->get();
    }


    public function seed_default_account_data($organization)
    {
        if ( isset($this->data['separator']) || isset($organization->data['separator'])) {

            $channel = new Channel();
            $channel->organization_id = $organization->id;
            $channel->name = $organization->name . ' Store';
            $channel->type = "shopify";
            $channel->save();

            $location = new Location();
            $location->organization_id = $organization->id;
            $location->name = $channel->name . ' Warehouse';
            $location->default_location = true;
            $location->save();

            $channel_location = new ChannelLocation();
            $channel_location->channel_id = $channel->id;
            $channel_location->location_id = $location->id;
            $channel_location->save();


            // version
            $version = new Version();
            $version->name = isset($this->data['language']) ? $this->data['language'] :(isset($organziation->data['language'])? $organziation->data['language']: 'English');
            $version->organization_id = $organization->id;
            $version->is_default = 1;
            $version->currency = isset($this->data['currency']) ?  $this->data['currency'] :(isset($organziation->data['currency'])? $organziation->data['currency']: 'USD');
            $version->separator = isset($this->data['separator'])?$organization->data['separator'] :(isset($organziation->data['separator'])? $organziation->data['separator']: ',');
            $version->save();


            $channel_version = new ChannelVersion();
            $channel_version->channel_id = $channel->id;
            $channel_version->version_id = $version->id;
            $channel_version->save();

            /*Saving default folder*/
            $folder = new Folder();
            $folder->create_default_folder($organization);

            /*Saving social media folders*/
            $folder_names = ['Instagram', 'Youtube', 'Facebook'];
            $folder = new Folder();
            $folder->create_social_media_folder($organization, $folder_names);

            // family
            $family = new Family();
            $family->name = 'General';
            $family->is_default = 1;
            $family->organization_id = $organization->id;
            $family->save();

            DB::table('settings')->insert([
                [
                    'organization_id' => $organization->id,
                    'key' => 'sku',
                    'value' => '1'
                ],
                [
                    'organization_id' => $organization->id,
                    'key' => 'name',
                    'value' => '1'
                ],
                [
                    'organization_id' => $organization->id,
                    'key' => 'barcode',
                    'value' => '0'
                ],
                [
                    'organization_id' => $organization->id,
                    'key' => 'weight',
                    'value' => '1'
                ],
                [
                    'organization_id' => $organization->id,
                    'key' => 'price',
                    'value' => '1'
                ],
                [
                    'organization_id' => $organization->id,
                    'key' => 'compare_at_price',
                    'value' => '0'
                ],
                [
                    'organization_id' => $organization->id,
                    'key' => 'cost_price',
                    'value' => '0'
                ],
                [
                    'organization_id' => $organization->id,
                    'key' => 'brand',
                    'value' => '1'
                ],
                [
                    'organization_id' => $organization->id,
                    'key' => 'vendor',
                    'value' => '1'
                ],
                [
                    'organization_id' => $organization->id,
                    'key' => 'category',
                    'value' => '1'
                ]
            ]);


            $attribute = new Attribute();
            $attribute->name = 'Product Name';
            $attribute->handle = 'product_name';
            $attribute->attribute_type_id = 1;
            $attribute->organization_id = $organization->id;
            $attribute->rules = json_encode(["required" => 1, "max" => 255]);
            $attribute->is_default = 1;
            $attribute->save();

            $attribute_family = new \App\Models\Product\AttributeFamily();
            $attribute_family->family_id = $family->id;
            $attribute_family->attribute_id = $attribute->id;
            $attribute_family->save();

            // default variant option
            $attribute = new Attribute();
            $attribute->name = 'Title';
            $attribute->handle = 'title';
            $attribute->attribute_type_id = 13;
            $attribute->organization_id = $organization->id;
            $attribute->rules = Null;
            $attribute->is_default = 1;
            $attribute->save();

            $attribute_option = new \App\Models\Product\AttributeOption();
            $attribute_option->name = 'Default Title';
            $attribute_option->attribute_id = $attribute->id;
            $attribute_option->save();

            $attribute = new Attribute();
            $attribute->name = 'Description';
            $attribute->handle = 'description';
            $attribute->attribute_type_id = 3;
            $attribute->organization_id = $organization->id;
            $attribute->rules = json_encode(["required" => 1, 'max' => 63000]);
            $attribute->is_default = 1;
            $attribute->save();

            $attribute_family = new \App\Models\Product\AttributeFamily();
            $attribute_family->family_id = $family->id;
            $attribute_family->attribute_id = $attribute->id;
            $attribute_family->save();

            //second family
            /*Saving seo attribute family*/
            $family = new Family();
            $family->name = 'SEO';
            $family->is_default = 1;
            $family->organization_id = $organization->id;
            $family->save();

            /*Saving seo url attribute*/
            $attribute = new Attribute();
            $attribute->name = 'URL Slug';
            $attribute->handle = 'seo_url';
            $attribute->attribute_type_id = 11;
            $attribute->organization_id = $organization->id;
            $attribute->rules = json_encode(["required" => 1, "slug", "url", "max" => 255]);
            $attribute->is_default = 1;
            $attribute->save();

            $attribute_family = new \App\Models\Product\AttributeFamily();
            $attribute_family->family_id = $family->id;
            $attribute_family->attribute_id = $attribute->id;
            $attribute_family->save();

            /*Saving seo title attribute*/
            $attribute = new Attribute();
            $attribute->name = 'SEO Title';
            $attribute->handle = 'seo_title';
            $attribute->attribute_type_id = 1;
            $attribute->organization_id = $organization->id;
            $attribute->rules = json_encode(["required" => 1, "max" => 255]);
            $attribute->is_default = 1;
            $attribute->save();

            $attribute_family = new \App\Models\Product\AttributeFamily();
            $attribute_family->family_id = $family->id;
            $attribute_family->attribute_id = $attribute->id;
            $attribute_family->save();

            /*Saving seo description attribute*/
            $attribute = new Attribute();
            $attribute->name = 'SEO Description';
            $attribute->handle = 'seo_description';
            $attribute->attribute_type_id = 3;
            $attribute->organization_id = $organization->id;
            $attribute->rules = json_encode(["required" => 1, "max" => 160]);
            $attribute->is_default = 1;
            $attribute->save();

            $attribute_family = new \App\Models\Product\AttributeFamily();
            $attribute_family->family_id = $family->id;
            $attribute_family->attribute_id = $attribute->id;
            $attribute_family->save();

            /*Saving seo keyword attribute*/
            $attribute = new Attribute();
            $attribute->name = 'Tags';
            $attribute->handle = 'seo_keyword';
            $attribute->attribute_type_id = 1;
            $attribute->organization_id = $organization->id;
            $attribute->rules = json_encode(["required" => 1, "max" => 255]);
            $attribute->is_default = 1;
            $attribute->save();

            $attribute_family = new \App\Models\Product\AttributeFamily();
            $attribute_family->family_id = $family->id;
            $attribute_family->attribute_id = $attribute->id;
            $attribute_family->save();
            $this->save_shopify_fields($organization);

            // this function is used for assigning invitation notification of new user
            try {
                if (isset($this->user)) {
                    $invitation = new Invite();
                    // TODO: not use Auth in model
                    $invitation->generate_invite_notifications($this->user);
                }
            } catch (\Exception $e) {
                Log::error($e);
            }
        }
    }

    public function save_shopify_fields($organization)
    {
        $family_ids = Family::where('is_default', 1)->pluck('id')->toArray();
        $attribute_families = AttributeFamily::whereIn('family_id', $family_ids)->with('attributes')->get();
        foreach ($attribute_families as $attribute_family) {
           
            $shopify_mapping = new ProductShopifyMapping();
            $shopify_mapping->attribute_family_id = $attribute_family->id;
            $shopify_mapping->organization_id = $organization->id;
            if ($attribute_family['attributes']->handle == 'product_name') {
                $shopify_mapping->shopify_fields = 'title';
                $shopify_mapping->save();
            } elseif ($attribute_family['attributes']->handle == 'description') {
                $shopify_mapping->shopify_fields = 'body_html';
                $shopify_mapping->save();
            } elseif ($attribute_family['attributes']->handle == 'price') {
                $shopify_mapping->shopify_fields = 'price';
                $shopify_mapping->save();
            } elseif ($attribute_family['attributes']->handle == 'compare_at_price') {
                $shopify_mapping->shopify_fields = 'compare_at_price';
                $shopify_mapping->save();
            } elseif ($attribute_family['attributes']->handle == 'upc_barcode') {
                $shopify_mapping->shopify_fields = 'barcode';
                $shopify_mapping->save();
            } elseif ($attribute_family['attributes']->handle == 'weight') {
                $shopify_mapping->shopify_fields = 'weight';
                $shopify_mapping->save();
            } elseif ($attribute_family['attributes']->handle == 'seo_title') {
                $shopify_mapping->shopify_fields = 'metafields_global_title_tag';
                $shopify_mapping->save();
            } elseif ($attribute_family['attributes']->handle == 'seo_description') {
                $shopify_mapping->shopify_fields = 'metafields_global_description_tag';
                $shopify_mapping->save();
            }
        }
    }

    /**
     * Get true or false if organization is more than one.
     *
     * @return boolean
     */
    public function more_than_one()
    {
        return $this->limit(2)->count() > 1;
    }

    public function only_one()
    {
        return $this->count() == 1;
    }

    /**
     * Get true or false if organization is less than one.
     *
     * @return boolean
     */
    public function less_than_one()
    {
        return $this->limit(1)->count() < 1;
    }

    public function is_checkpoint_passed($organization_id)
    {
        return $this->whereNotNull("region")->find($organization_id) != null;
    }

    public function remaining_grace_period()
    {
        return Cs::where('organization_id', $this->id)->value('ends_at');
    }








    public function users()
    {
        return $this->belongsToMany(User::class);
    }

    public function users_without_scope()
    {
        return $this->belongsToMany(User::class)->withoutGlobalScopes();
    }

    public function stripe_subscriptions()
    {
        return DB::table("subscriptions")->where("organization_id", $this->id);
    }

    public function shopify_subscriptions()
    {
        return ShopifySubscription::withoutGlobalScopes()->where("organization_id", $this->id);
    }

    public function channels()
    {
        return $this->hasMany(Channel::class, 'organization_id', 'id')->withoutGlobalScopes();
    }

    public function categories()
    {
        return $this->hasMany(Category::class)->withoutGlobalScopes();
    }

    public function versions()
    {
        return $this->hasMany(Version::class)->withoutGlobalScopes();
    }

    public function brandsPortals()
    {
        return $this->hasMany(BrandsPortal::class, 'organization_id', 'id')->withoutGlobalScopes();
    }

    public function files()
    {
        return $this->hasMany(File::class, 'organization_id', 'id')->withoutGlobalScopes();
    }

    public function attributes()
    {
        return $this->hasMany(Attribute::class)->withoutGlobalScopes();
    }

    public function brands()
    {
        return $this->hasMany(Brand::class)->withoutGlobalScopes();
    }
    public function families()
    {
        return $this->hasMany(Family::class)->withoutGlobalScopes();
    }
    public function products()
    {
        return $this->hasMany(Product::class)->withoutGlobalScopes();
    }

    public function invites()
    {
        return $this->hasMany(Invite::class, 'organization_id_sender')->withoutGlobalScopes();
    }

    public function relatedOrganizations()
    {
        return $this->users()->first()->organizations()->withoutGlobalScopes();
    }

    public function teamInvites()
    {
        return $this->hasMany(TeamInvite::class, 'organization_id', 'id')->withoutGlobalScopes();
    }

}
