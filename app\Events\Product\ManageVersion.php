<?php

namespace App\Events\Product;

use App\Models\Product\Product;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ManageVersion
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * @param  Product  $product
     * @param  array  $data
     * @param  array|null  $versionIds
     */
    public function __construct(public Product $product,public array $data, public bool $refresh = false)
    {
    }
}
